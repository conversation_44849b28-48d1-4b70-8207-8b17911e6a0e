package machine

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"slices"
	"time"

	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/gokeys/gokeys/internal/domain/repositories"
	"github.com/gokeys/gokeys/internal/domain/services/crypto"
	"github.com/google/uuid"
)

// CheckoutOptions represents machine checkout configuration
type CheckoutOptions struct {
	Include     []string `json:"include,omitempty"`
	TTL         *int     `json:"ttl,omitempty"` // TTL in seconds
	Encrypted   bool     `json:"encrypted"`
	Algorithm   *string  `json:"algorithm,omitempty"`
}

// CheckoutService handles machine certificate generation (maps to Ruby MachineCheckoutService)
type CheckoutService struct {
	machineRepo      repositories.MachineRepository
	licenseRepo      repositories.LicenseRepository
	organizationRepo repositories.OrganizationRepository
	policyRepo       repositories.PolicyRepository
	crypto           *crypto.CryptoService
}

// NewCheckoutService creates a new machine checkout service
func NewCheckoutService(
	machineRepo repositories.MachineRepository,
	licenseRepo repositories.LicenseRepository,
	organizationRepo repositories.OrganizationRepository,
	policyRepo repositories.PolicyRepository,
	crypto *crypto.CryptoService,
) *CheckoutService {
	return &CheckoutService{
		machineRepo:      machineRepo,
		licenseRepo:      licenseRepo,
		organizationRepo: organizationRepo,
		policyRepo:       policyRepo,
		crypto:           crypto,
	}
}

// AllowedIncludes defines what can be included in machine checkout (maps to Ruby ALLOWED_INCLUDES)
var AllowedIncludes = []string{
	"license.entitlements",
	"license.product",
	"license.policy",
	"license.owner",
	"license.users",
	"license",
	"components",
	"group",
	"owner",
}

// CheckoutMachine generates a signed machine certificate (maps to Ruby MachineCheckoutService.call)
func (cs *CheckoutService) CheckoutMachine(ctx context.Context, machine *entities.Machine, options *CheckoutOptions) (*entities.MachineFile, error) {
	// Ruby validation: machine must be present
	if machine == nil {
		return nil, fmt.Errorf("machine must be present")
	}

	// Ruby validation: license must be present
	if machine.LicenseID == "" {
		return nil, fmt.Errorf("license must be present")
	}

	// Set default options
	if options == nil {
		options = &CheckoutOptions{}
	}

	// Ruby validation: invalid includes
	if err := cs.validateIncludes(options.Include); err != nil {
		return nil, fmt.Errorf("invalid includes: %w", err)
	}

	// Get machine's license
	licenseUUID, err := uuid.Parse(machine.LicenseID)
	if err != nil {
		return nil, fmt.Errorf("invalid license ID: %w", err)
	}

	license, err := cs.licenseRepo.GetByID(ctx, licenseUUID)
	if err != nil {
		return nil, fmt.Errorf("failed to get license: %w", err)
	}

	// Get organization for key material
	organizationUUID, err := uuid.Parse(license.OrganizationID)
	if err != nil {
		return nil, fmt.Errorf("invalid organization ID: %w", err)
	}

	organization, err := cs.organizationRepo.GetByID(ctx, organizationUUID)
	if err != nil {
		return nil, fmt.Errorf("failed to get organization: %w", err)
	}

	// Get policy for algorithm determination
	var policy *entities.Policy
	if license.PolicyID != "" {
		policyUUID, err := uuid.Parse(license.PolicyID)
		if err == nil {
			policy, _ = cs.policyRepo.GetByID(ctx, policyUUID)
		}
	}

	// Calculate timestamps
	issuedAt := time.Now()
	var expiresAt *time.Time
	if options.TTL != nil {
		expires := issuedAt.Add(time.Duration(*options.TTL) * time.Second)
		expiresAt = &expires
	}

	// Build machine data payload
	data, err := cs.buildMachineData(ctx, machine, license, options, issuedAt, expiresAt)
	if err != nil {
		return nil, fmt.Errorf("failed to build machine data: %w", err)
	}

	// Convert to JSON
	dataJSON, err := json.Marshal(data)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal machine data: %w", err)
	}

	// Determine algorithm based on license policy scheme
	algorithm := cs.determineAlgorithm(policy, options.Algorithm)

	// Encode data (encrypt if requested, matching Ruby implementation)
	var encodedData string
	if options.Encrypted {
		// Ruby: encrypt(data, secret: license.key + machine.fingerprint)
		secret := license.Key + machine.Fingerprint
		encryptedResult, err := cs.crypto.AES.EncryptString(base64.StdEncoding.EncodeToString([]byte(secret)), string(dataJSON))
		if err != nil {
			return nil, fmt.Errorf("failed to encrypt machine data: %w", err)
		}
		encodedData = encryptedResult
	} else {
		// Ruby: encode(data, strict: true) - strict base64 encoding
		encodedData = base64.StdEncoding.EncodeToString(dataJSON)
	}

	// Sign the encoded data
	signature, err := cs.signData(encodedData, organization, algorithm)
	if err != nil {
		return nil, fmt.Errorf("failed to sign machine data: %w", err)
	}

	// Ruby: doc = { enc: enc, sig: sig, alg: alg }
	doc := map[string]any{
		"enc": encodedData,
		"sig": signature,
		"alg": algorithm,
	}

	docJSON, err := json.Marshal(doc)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal document: %w", err)
	}

	// Ruby: enc = encode(doc.to_json) then format as certificate
	certData := base64.StdEncoding.EncodeToString(docJSON)
	certificate := fmt.Sprintf("-----BEGIN MACHINE FILE-----\n%s\n-----END MACHINE FILE-----", certData)

	// Ruby: MachineFile.new(environment_id:, organization_id:, license_id:, machine_id:, certificate:, issued_at:, expires_at:, ttl:, includes:, algorithm:)

	// Filter includes to only allowed ones (Ruby: incl = includes & ALLOWED_INCLUDES)
	filteredIncludes := make([]string, 0)
	for _, include := range options.Include {
		if cs.containsString(AllowedIncludes, include) {
			filteredIncludes = append(filteredIncludes, include)
		}
	}

	return entities.NewMachineFile(
		organization.ID,
		nil, // environmentID removed
		license.ID,
		machine.ID,
		certificate,
		issuedAt,
		expiresAt,
		options.TTL,
		filteredIncludes,
		algorithm,
	), nil
}

// buildMachineData constructs the machine data payload with requested includes (Ruby: renderer.render)
func (cs *CheckoutService) buildMachineData(_ context.Context, machine *entities.Machine, license *entities.License, options *CheckoutOptions, issuedAt time.Time, expiresAt *time.Time) (map[string]any, error) {
	// Ruby: renderer.render(machine, meta: meta, include: incl)
	data := map[string]any{
		"id":   machine.ID,
		"type": "machine",
		"attributes": map[string]any{
			"fingerprint": machine.Fingerprint,
			"name":        machine.Name,
			"hostname":    machine.Hostname,
			"platform":    machine.Platform,
			"created_at":  machine.CreatedAt,
			"updated_at":  machine.UpdatedAt,
		},
		"meta": map[string]any{
			"issued": issuedAt,
			"expiry": expiresAt, // Ruby sets this even if nil
			"ttl":    options.TTL,
		},
	}

	// Add optional fields to attributes
	attributes := data["attributes"].(map[string]any)
	if machine.IP != nil && *machine.IP != "" {
		attributes["ip"] = machine.IP
	}
	if machine.Cores > 0 {
		attributes["cores"] = machine.Cores
	}
	if machine.LastHeartbeatAt != nil {
		attributes["last_heartbeat_at"] = *machine.LastHeartbeatAt
	}

	// Ruby: incl = includes & ALLOWED_INCLUDES - filter includes
	// Add includes if requested
	if cs.containsString(options.Include, "license") {
		data["license"] = map[string]any{
			"id":         license.ID,
			"key":        license.Key,
			"name":       license.Name,
			"status":     license.Status,
			"suspended":  license.Suspended,
			"created_at": license.CreatedAt,
			"updated_at": license.UpdatedAt,
			"expires_at": license.ExpiresAt, // Ruby includes this even if nil
		}
	}

	if cs.containsString(options.Include, "components") {
		// TODO: Load machine components
		// components, err := cs.machineComponentRepo.GetByMachine(ctx, machine.ID)
		// if err == nil {
		//     data["components"] = components
		// }
	}


	// TODO: Add other includes (license.entitlements, license.product, license.policy, etc.)

	return data, nil
}

// determineAlgorithm determines the signing algorithm based on license policy scheme (Ruby MachineCheckoutService logic)
func (cs *CheckoutService) determineAlgorithm(policy *entities.Policy, requestedAlg *string) string {
	// If algorithm explicitly provided, use it
	if requestedAlg != nil {
		return *requestedAlg
	}

	// Ruby logic: determine algorithm from license.scheme (delegated to policy.scheme)
	// Ruby: sign = case license.scheme when 'RSA_2048_PKCS1_PSS_SIGN_V2'...
	if policy != nil {
		switch string(policy.Scheme) {
		case "RSA_2048_PKCS1_PSS_SIGN_V2", "RSA_2048_PKCS1_PSS_SIGN":
			return "rsa-pss-sha256"
		case "RSA_2048_PKCS1_SIGN_V2", "RSA_2048_PKCS1_SIGN", "RSA_2048_PKCS1_ENCRYPT", "RSA_2048_JWT_RS256":
			return "rsa-sha256"
		case "ED25519_SIGN":
			return "ed25519"
		default:
			// Ruby: else true - means use default signing (ed25519)
			return "ed25519"
		}
	}

	// Ruby default when no scheme - use default signing (ed25519)
	return "ed25519"
}

// signData signs the encoded data using the specified algorithm
func (cs *CheckoutService) signData(data string, organization *entities.Organization, algorithm string) (string, error) {
	// Add prefix for machine signing (matches Ruby implementation)
	dataToSign := fmt.Sprintf("machine/%s", data)

	switch algorithm {
	case "ed25519":
		if organization.Ed25519PrivateKey == nil || *organization.Ed25519PrivateKey == "" {
			return "", fmt.Errorf("Ed25519 private key not available")
		}
		signature, err := cs.crypto.Ed25519.Sign(dataToSign, []byte(*organization.Ed25519PrivateKey))
		if err != nil {
			return "", err
		}
		return base64.StdEncoding.EncodeToString([]byte(signature)), nil

	case "rsa-sha256", "rsa-pss-sha256":
		if organization.PrivateKey == nil || *organization.PrivateKey == "" {
			return "", fmt.Errorf("RSA private key not available")
		}
		signature, err := cs.crypto.RSA.Sign(*organization.PrivateKey, []byte(dataToSign))
		if err != nil {
			return "", err
		}
		return signature, nil // RSA.Sign already returns base64 encoded string

	default:
		return "", fmt.Errorf("unsupported algorithm: %s", algorithm)
	}
}

// validateIncludes validates the requested includes against allowed includes
func (cs *CheckoutService) validateIncludes(includes []string) error {
	for _, include := range includes {
		if !cs.containsString(AllowedIncludes, include) {
			return fmt.Errorf("invalid include: %s", include)
		}
	}
	return nil
}

// containsString checks if a string slice contains a specific string
func (cs *CheckoutService) containsString(slice []string, item string) bool {
	return slices.Contains(slice, item)
}

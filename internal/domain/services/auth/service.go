package auth

import (
	"context"
	"fmt"
	"time"

	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/gokeys/gokeys/internal/domain/repositories"
	"github.com/gokeys/gokeys/internal/domain/services/crypto"
)

// AuthService provides simple, clean authorization based on permissions
type AuthService struct {
	permissionRepo   repositories.PermissionRepository
	usersOrgRepo     repositories.UsersOrganizationRepository
	userRepo         repositories.UserRepository
	organizationRepo repositories.OrganizationRepository
	cryptoService    *crypto.CryptoService
	privateKey       string // RSA private key for JWT signing
	publicKey        string // RSA public key for JWT verification
}

// NewAuthService creates a new auth service
func NewAuthService(
	permissionRepo repositories.PermissionRepository,
	usersOrgRepo repositories.UsersOrganizationRepository,
	userRepo repositories.UserRepository,
	organizationRepo repositories.OrganizationRepository,
	cryptoService *crypto.CryptoService,
	privateKey string,
	publicKey string,
) *AuthService {
	return &AuthService{
		permissionRepo:   permissionRepo,
		usersOrgRepo:     usersOrgRepo,
		userRepo:         userRepo,
		organizationRepo: organizationRepo,
		cryptoService:    cryptoService,
		privateKey:       privateKey,
		publicKey:        publicKey,
	}
}

// CheckPermission checks if a user has permission to perform an action on a resource
func (s *AuthService) CheckPermission(ctx context.Context, userID, resourceType, action string, resourceID *string, organizationID *string) (bool, error) {
	// Get user permissions
	permissions, err := s.permissionRepo.GetUserPermissions(ctx, userID)
	if err != nil {
		return false, fmt.Errorf("failed to get user permissions: %w", err)
	}

	// Check each permission
	for _, permission := range permissions {
		if s.permissionMatches(permission, resourceType, action, resourceID, organizationID) {
			return true, nil
		}
	}

	return false, nil
}

// permissionMatches checks if a permission allows the requested action
func (s *AuthService) permissionMatches(permission *entities.Permission, resourceType, action string, resourceID *string, organizationID *string) bool {
	// Check resource type
	if !s.resourceTypeMatches(permission.ResourceType, resourceType) {
		return false
	}

	// Check action
	if !s.actionMatches(permission.Actions, action) {
		return false
	}

	// Check scope
	return s.scopeMatches(permission.Scope, resourceID, organizationID)
}

// resourceTypeMatches checks if resource type matches permission
func (s *AuthService) resourceTypeMatches(permissionType, requestedType string) bool {
	return permissionType == "*" || permissionType == requestedType
}

// actionMatches checks if action is allowed
func (s *AuthService) actionMatches(allowedActions []string, requestedAction string) bool {
	for _, action := range allowedActions {
		if action == "*" || action == requestedAction {
			return true
		}
		// Check basic action mapping
		if s.basicActionMatches(action, requestedAction) {
			return true
		}
	}
	return false
}

// basicActionMatches maps complex actions to basic CRUD actions
func (s *AuthService) basicActionMatches(allowedAction, requestedAction string) bool {
	actionMap := map[string][]string{
		"read":   {"read", "list", "validate", "checkout", "heartbeat"},
		"create": {"create", "register", "checkout", "activate"},
		"update": {"update", "modify", "check-in", "suspend", "activate"},
		"delete": {"delete", "remove", "revoke", "deactivate"},
	}

	if mappedActions, exists := actionMap[allowedAction]; exists {
		for _, mapped := range mappedActions {
			if mapped == requestedAction {
				return true
			}
		}
	}
	return false
}

// scopeMatches checks if the scope allows access to the resource
func (s *AuthService) scopeMatches(scope string, resourceID *string, organizationID *string) bool {
	switch scope {
	case entities.ScopeSystem:
		return true // System scope allows everything

	case entities.ScopeOwner:
		// Owner scope - would need additional logic to check ownership
		return true // Simplified for now

	default:
		// Check org scope: org:uuid
		if len(scope) > 4 && scope[:4] == "org:" {
			scopeOrgID := scope[4:]
			if organizationID != nil && *organizationID == scopeOrgID {
				return true
			}
		}

		// Check resource scope: resource:type:uuid
		if len(scope) > 9 && scope[:9] == "resource:" {
			// Parse resource:type:uuid
			parts := make([]string, 0, 3)
			current := ""
			for i, char := range scope[9:] {
				if char == ':' && len(parts) < 2 {
					parts = append(parts, current)
					current = ""
				} else if i == len(scope[9:])-1 {
					current += string(char)
					parts = append(parts, current)
				} else {
					current += string(char)
				}
			}

			if len(parts) == 2 && resourceID != nil {
				resourceUUID := parts[1]
				return *resourceID == resourceUUID
			}
		}
	}

	return false
}

// CanListResources checks if user can list resources of a type
func (s *AuthService) CanListResources(ctx context.Context, userID, resourceType string, organizationID *string) (bool, error) {
	return s.CheckPermission(ctx, userID, resourceType, "read", nil, organizationID)
}

// CanCreateResource checks if user can create a resource
func (s *AuthService) CanCreateResource(ctx context.Context, userID, resourceType string, organizationID *string) (bool, error) {
	return s.CheckPermission(ctx, userID, resourceType, "create", nil, organizationID)
}

// CanReadResource checks if user can read a specific resource
func (s *AuthService) CanReadResource(ctx context.Context, userID, resourceType, resourceID string, organizationID *string) (bool, error) {
	return s.CheckPermission(ctx, userID, resourceType, "read", &resourceID, organizationID)
}

// CanUpdateResource checks if user can update a specific resource
func (s *AuthService) CanUpdateResource(ctx context.Context, userID, resourceType, resourceID string, organizationID *string) (bool, error) {
	return s.CheckPermission(ctx, userID, resourceType, "update", &resourceID, organizationID)
}

// CanDeleteResource checks if user can delete a specific resource
func (s *AuthService) CanDeleteResource(ctx context.Context, userID, resourceType, resourceID string, organizationID *string) (bool, error) {
	return s.CheckPermission(ctx, userID, resourceType, "delete", &resourceID, organizationID)
}

// IsUserInOrganization checks if user is a member of organization
func (s *AuthService) IsUserInOrganization(ctx context.Context, userID, organizationID string) (bool, error) {
	return s.usersOrgRepo.IsUserInOrganization(ctx, userID, organizationID)
}

// GetUserOrganizations gets all organizations a user belongs to
func (s *AuthService) GetUserOrganizations(ctx context.Context, userID string) ([]*entities.UsersOrganization, error) {
	return s.usersOrgRepo.GetUserOrganizations(ctx, userID)
}

// HasSystemAdminPermission checks if user has system-wide admin permissions
func (s *AuthService) HasSystemAdminPermission(ctx context.Context, userID string) (bool, error) {
	permissions, err := s.permissionRepo.GetUserPermissions(ctx, userID)
	if err != nil {
		return false, err
	}

	for _, permission := range permissions {
		if permission.Scope == entities.ScopeSystem && 
		   permission.ResourceType == entities.ResourceTypeAll &&
		   s.actionMatches(permission.Actions, "*") {
			return true, nil
		}
	}

	return false, nil
}

// HasOrganizationAdminPermission checks if user has admin permissions in an organization
func (s *AuthService) HasOrganizationAdminPermission(ctx context.Context, userID, organizationID string) (bool, error) {
	permissions, err := s.permissionRepo.GetUserPermissions(ctx, userID)
	if err != nil {
		return false, err
	}

	orgScope := fmt.Sprintf("org:%s", organizationID)

	for _, permission := range permissions {
		if permission.Scope == orgScope && 
		   permission.ResourceType == entities.ResourceTypeAll &&
		   s.actionMatches(permission.Actions, "*") {
			return true, nil
		}
	}

	return false, nil
}

// GrantPermission grants a permission to a user
func (s *AuthService) GrantPermission(ctx context.Context, userID, scope, resourceType string, actions []string, grantedBy string) error {
	_, err := s.permissionRepo.GrantPermission(ctx, userID, scope, resourceType, actions, &grantedBy, nil)
	return err
}

// RevokePermission revokes a permission from a user
func (s *AuthService) RevokePermission(ctx context.Context, userID, scope, resourceType string) error {
	return s.permissionRepo.RevokePermission(ctx, userID, scope, resourceType)
}

// AddUserToOrganization adds a user to an organization
func (s *AuthService) AddUserToOrganization(ctx context.Context, userID, organizationID, invitedBy string) error {
	userOrg := &entities.UsersOrganization{
		UserID:         userID,
		OrganizationID: organizationID,
		InvitedBy:      &invitedBy,
	}
	return s.usersOrgRepo.Create(ctx, userOrg)
}

// RemoveUserFromOrganization removes a user from an organization
func (s *AuthService) RemoveUserFromOrganization(ctx context.Context, userID, organizationID string) error {
	return s.usersOrgRepo.Delete(ctx, userID, organizationID)
}

// GenerateJWT generates a JWT token for a user session
func (s *AuthService) GenerateJWT(userID, sessionID string) (string, error) {
	tokenOpts := crypto.TokenOptions{
		Subject:   userID,
		Issuer:    "gokeys",
		ExpiresIn: 24 * time.Hour, // 24 hour token
		CustomClaims: map[string]interface{}{
			"user_id":    userID,
			"session_id": sessionID,
		},
	}

	return s.cryptoService.CreateJWTToken(crypto.SchemeJWTRS256, s.privateKey, tokenOpts)
}

// VerifyJWT verifies a JWT token and returns the claims
func (s *AuthService) VerifyJWT(token string) (map[string]interface{}, error) {
	return s.cryptoService.VerifyJWTToken(token, s.publicKey)
}

// RefreshJWT creates a new JWT token with extended expiration
func (s *AuthService) RefreshJWT(token string) (string, error) {
	return s.cryptoService.JWT.RefreshToken(token, s.privateKey, 24*time.Hour)
}

// GenerateAPIToken generates a JWT token for API access
func (s *AuthService) GenerateAPIToken(userID, organizationID string, permissions []string, expiresIn time.Duration) (string, error) {
	return s.cryptoService.GenerateSecureToken(organizationID, userID, permissions, expiresIn, s.privateKey, crypto.SchemeJWTRS256)
}

// ValidateAPIToken validates an API token and returns user context
func (s *AuthService) ValidateAPIToken(token string) (map[string]interface{}, error) {
	return s.cryptoService.ValidateAPIToken(token, s.publicKey)
}
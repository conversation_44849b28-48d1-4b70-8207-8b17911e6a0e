// Package license - Dị<PERSON> vụ xác thực và quản lý license
//
// TỔNG QUAN PACKAGE LICENSE:
// Package này chứa 3 service chính để xử lý license trong hệ thống GoKeys:
//
// 1. ValidationService (validation.go):
//   - <PERSON><PERSON><PERSON> thực license key có hợp lệ không
//   - Ki<PERSON>m tra các điều kiện: hết hạn, suspended, banned, scope validation
//   - Tương thích 100% với Ruby Keygen API
//   - Hỗ trợ cache để tăng tốc độ validation
//   - Tự động đăng ký machine mới khi cần thiết
//
// 2. CheckoutService (checkout.go):
//   - Tạo license certificate đã ký số
//   - Hỗ trợ mã hóa và nhiều thuật toán ký (RSA, Ed25519)
//   - Tạo file certificate dạng PEM-like
//   - Mapping chính xác từ Ruby LicenseCheckoutService
//
// 3. LookupService (lookup.go):
//   - <PERSON><PERSON><PERSON> kiế<PERSON> license theo key
//   - Hỗ trợ cả modern keys và legacy v1 encrypted keys
//   - X<PERSON> lý bcrypt và SHA256 để tương thích ngược
//   - Mapping từ Ruby LicenseKeyLookupService
//
// TẤT CẢ CÁC SERVICE ĐỀU:
// - Tương thích 100% với Ruby Keygen API
// - Giữ nguyên logic business từ hệ thống cũ
// - Sử dụng dependency injection pattern
// - Error handling nhất quán
// - Logging và monitoring đầy đủ
package license

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/gokeys/gokeys/internal/domain/repositories"
	"github.com/gokeys/gokeys/internal/domain/services/crypto"
	"github.com/google/uuid"
)

// ValidationResult represents the result of license validation
// Kết quả xác thực license - chứa tất cả thông tin về quá trình kiểm tra license
// Bao gồm trạng thái hợp lệ, thông tin license, policy, organization và các thống kê sử dụng
type ValidationResult struct {
	Valid            bool                   `json:"valid"`                  // Trạng thái hợp lệ của license
	License          *entities.License      `json:"license,omitempty"`      // Thông tin chi tiết license
	Policy           *entities.Policy       `json:"policy,omitempty"`       // Chính sách áp dụng cho license
	Organization     *entities.Organization `json:"organization,omitempty"` // Tổ chức sở hữu license
	ValidationTime   time.Time              `json:"validation_time"`        // Thời điểm thực hiện xác thực
	ExpiresAt        *time.Time             `json:"expires_at,omitempty"`   // Thời điểm hết hạn license
	MachinesUsed     int                    `json:"machines_used"`          // Số máy đang sử dụng license
	MachinesAllowed  int                    `json:"machines_allowed"`       // Số máy tối đa được phép
	CoresUsed        int                    `json:"cores_used"`             // Số CPU core đang sử dụng
	CoresAllowed     int                    `json:"cores_allowed"`          // Số CPU core tối đa được phép
	ProcessesUsed    int                    `json:"processes_used"`         // Số process đang chạy
	ProcessesAllowed int                    `json:"processes_allowed"`      // Số process tối đa được phép
	UsersUsed        int                    `json:"users_used"`             // Số user đang sử dụng
	UsersAllowed     int                    `json:"users_allowed"`          // Số user tối đa được phép
	Claims           map[string]any         `json:"claims,omitempty"`       // Các claim/quyền bổ sung trong license
	Errors           []string               `json:"errors,omitempty"`       // Danh sách lỗi trong quá trình xác thực
	Warnings         []string               `json:"warnings,omitempty"`     // Danh sách cảnh báo

	// Structured validation result thay vì magic strings
	ValidationError *ValidationError  `json:"validation_error,omitempty"` // Lỗi validation có cấu trúc
	Code            ValidationCode    `json:"code"`                       // Mã kết quả validation
	Detail          ValidationMessage `json:"detail"`                     // Message chi tiết
}

// ValidationScope represents validation scope parameters
// Phạm vi xác thực - định nghĩa các tham số để kiểm tra license trong ngữ cảnh cụ thể
// Ví dụ: kiểm tra cho user nào, máy nào, product nào, etc.
type ValidationScope struct {
	Product      *string  `json:"product,omitempty"`      // ID sản phẩm cần xác thực
	Policy       *string  `json:"policy,omitempty"`       // ID chính sách cụ thể
	User         *string  `json:"user,omitempty"`         // ID hoặc email user sử dụng
	Machine      *string  `json:"machine,omitempty"`      // ID máy cụ thể
	Fingerprint  *string  `json:"fingerprint,omitempty"`  // Fingerprint của máy client
	Fingerprints []string `json:"fingerprints,omitempty"` // Danh sách fingerprint cho nhiều máy
	Components   []string `json:"components,omitempty"`   // Các component phần mềm cần kiểm tra
	Checksum     *string  `json:"checksum,omitempty"`     // Checksum để verify tính toàn vẹn
	Version      *string  `json:"version,omitempty"`      // Phiên bản phần mềm
	Entitlements []string `json:"entitlements,omitempty"` // Danh sách quyền/tính năng cần thiết
}

// ValidationOptions represents validation options
// Tùy chọn xác thực - cấu hình cách thức thực hiện validation
type ValidationOptions struct {
	Scope     *ValidationScope `json:"scope,omitempty"`      // Phạm vi xác thực
	SkipTouch bool             `json:"skip_touch,omitempty"` // Bỏ qua việc cập nhật last_validated_at
}

// CacheInterface defines the caching interface
// Interface cho hệ thống cache - hỗ trợ cả Memory cache và Valkey/Redis
// Giúp tăng tốc độ validation bằng cách lưu kết quả đã xác thực
type CacheInterface interface {
	Set(ctx context.Context, key string, value any, expiration time.Duration) error
	Get(ctx context.Context, key string) (string, error)
	Delete(ctx context.Context, key string) error
	Exists(ctx context.Context, key string) (bool, error)
}

// ValidationService provides license validation with caching
// Service chính cho việc xác thực license - tương thích 100% với Ruby Keygen API
// Thực hiện tất cả logic validation phức tạp với cache để tối ưu performance
type ValidationService struct {
	licenseRepo      repositories.LicenseRepository      // Repository truy cập dữ liệu license
	organizationRepo repositories.OrganizationRepository // Repository truy cập dữ liệu organization
	machineRepo      repositories.MachineRepository      // Repository truy cập dữ liệu machine
	policyRepo       repositories.PolicyRepository       // Repository truy cập dữ liệu policy
	userRepo         repositories.UserRepository         // Repository truy cập dữ liệu user
	crypto           *crypto.CryptoService               // Service mã hóa/giải mã
	cache            CacheInterface                      // Interface cache (Memory hoặc Valkey)
	cacheTTL         time.Duration                       // Thời gian sống của cache
}

// NewValidationService creates a new validation service
// Khởi tạo service xác thực license với tất cả dependencies cần thiết
// Mặc định cache TTL là 15 phút để cân bằng giữa performance và tính chính xác
func NewValidationService(
	licenseRepo repositories.LicenseRepository,
	organizationRepo repositories.OrganizationRepository,
	machineRepo repositories.MachineRepository,
	policyRepo repositories.PolicyRepository,
	userRepo repositories.UserRepository,
	crypto *crypto.CryptoService,
	cache CacheInterface,
) *ValidationService {
	return &ValidationService{
		licenseRepo:      licenseRepo,
		organizationRepo: organizationRepo,
		machineRepo:      machineRepo,
		policyRepo:       policyRepo,
		userRepo:         userRepo,
		crypto:           crypto,
		cache:            cache,
		cacheTTL:         15 * time.Minute, // Default cache TTL
	}
}

// newValidationResult creates a new validation result with default values
// Tạo ValidationResult mới với các giá trị mặc định
func newValidationResult() *ValidationResult {
	return &ValidationResult{
		ValidationTime: time.Now(),
		Errors:         []string{},
		Warnings:       []string{},
		Code:           ValidationCodeValid,
		Detail:         MessageValid,
	}
}

// setValidationError sets the validation error and updates the result status
// Đặt lỗi validation và cập nhật trạng thái kết quả
func (r *ValidationResult) setValidationError(code ValidationCode, details ...string) {
	r.Valid = false
	r.Code = code
	r.Detail = GetMessage(code)
	r.ValidationError = &ValidationError{
		Code:    code,
		Message: GetMessage(code),
	}
	if len(details) > 0 {
		r.ValidationError.Details = details[0]
	}
}

// setValid marks the result as valid
// Đánh dấu kết quả là hợp lệ
func (r *ValidationResult) setValid() {
	r.Valid = true
	r.Code = ValidationCodeValid
	r.Detail = MessageValid
	r.ValidationError = nil
}

// SetCacheTTL sets the cache time-to-live duration
// Đặt thời gian sống của cache - thường dùng để tùy chỉnh theo môi trường
// Production thường dùng TTL ngắn hơn để đảm bảo tính chính xác
func (vs *ValidationService) SetCacheTTL(ttl time.Duration) {
	vs.cacheTTL = ttl
}

// ValidateLicense validates a license key and returns detailed validation result
// Xác thực license với tham số đơn giản - wrapper cho ValidateLicenseWithOptions
// Tương thích với API cũ chỉ cần licenseKey và machineFingerprint
func (vs *ValidationService) ValidateLicense(ctx context.Context, licenseKey string, machineFingerprint *string) (*ValidationResult, error) {
	// Build scope from legacy parameters
	// Chuyển đổi tham số cũ thành ValidationScope mới
	scope := &ValidationScope{}
	if machineFingerprint != nil {
		scope.Fingerprint = machineFingerprint
	}

	options := &ValidationOptions{
		Scope: scope,
	}

	return vs.ValidateLicenseWithOptions(ctx, licenseKey, options)
}

// ValidateLicenseWithOptions validates a license with full options (maps to Ruby LicenseValidationService)
// Hàm chính thực hiện validation license với đầy đủ tùy chọn
// Tương thích 100% với Ruby LicenseValidationService.validate! method
// Quy trình: Cache lookup → Basic validation → Core validation → Touch license → Cache result
func (vs *ValidationService) ValidateLicenseWithOptions(ctx context.Context, licenseKey string, options *ValidationOptions) (*ValidationResult, error) {
	// Khởi tạo kết quả validation với thời gian hiện tại
	result := newValidationResult()

	// Set default options
	// Đảm bảo luôn có options và scope hợp lệ để tránh null pointer
	if options == nil {
		options = &ValidationOptions{}
	}
	if options.Scope == nil {
		options.Scope = &ValidationScope{}
	}

	// Try to get from cache first
	// Kiểm tra cache trước để tăng tốc độ response
	cacheKey := vs.buildCacheKeyWithScope(licenseKey, options.Scope)
	if cachedResult, err := vs.getFromCache(ctx, cacheKey); err == nil && cachedResult != nil {
		// Trả về kết quả từ cache nếu tìm thấy
		return cachedResult, nil
	}

	// Step 1: Basic existence and format validation
	// Bước 1: Kiểm tra cơ bản - license key có tồn tại không
	if licenseKey == "" {
		result.setValidationError(ValidationCodeNotFound)
		return result, nil
	}

	// Get license from database
	// Lấy thông tin chi tiết license từ database
	license, err := vs.licenseRepo.GetByKey(ctx, licenseKey)
	if err != nil {
		result.setValidationError(ValidationCodeNotFound)
		return result, nil
	}
	result.License = license

	// Get related entities - License belongs to organization
	// Lấy các entity liên quan - Mỗi license thuộc về 1 organization
	organizationUUID, err := uuid.Parse(license.OrganizationID)
	if err != nil {
		result.setValidationError(ValidationCodeInvalidAccount)
		return result, nil
	}

	organization, err := vs.organizationRepo.GetByID(ctx, organizationUUID)
	if err != nil {
		result.setValidationError(ValidationCodeAccountNotFound)
		return result, nil
	}
	result.Organization = organization

	// Get policy
	// Lấy chính sách áp dụng cho license (nếu có)
	if license.PolicyID != "" {
		policyUUID, err := uuid.Parse(license.PolicyID)
		if err == nil {
			policy, err := vs.policyRepo.GetByID(ctx, policyUUID)
			if err == nil {
				result.Policy = policy
			}
		}
	}

	// Auto-register machine if fingerprint provided but no machine exists (Ruby behavior)
	// Tự động đăng ký máy mới nếu có fingerprint nhưng chưa tồn tại - giống Ruby
	if options.Scope != nil && options.Scope.Fingerprint != nil {
		if err := vs.autoRegisterMachine(ctx, license, *options.Scope.Fingerprint); err != nil {
			result.setValidationError(ValidationCodeMachineRegistrationFailed)
			return result, nil
		}
	}

	// Populate usage statistics AFTER auto-registration (needed for validation logic)
	// Thu thập thống kê sử dụng SAU khi auto-register - cần cho logic validation
	vs.populateUsageStats(ctx, result, license)

	// Core validation logic (mapping from Ruby LicenseValidationService)
	// Logic validation chính - mapping từ Ruby LicenseValidationService.validate!
	validationCode := vs.validateCoreClean(ctx, license, organization, result.Policy, options.Scope)

	if !validationCode.IsValid() {
		result.setValidationError(validationCode)
		return result, nil
	}

	// Mark as valid if all checks passed
	result.setValid()

	// Touch license (update last_validated_at) unless skipped
	// Cập nhật thời gian xác thực cuối trừ khi bỏ qua
	if !options.SkipTouch {
		vs.touchLicense(ctx, license, options.Scope)
	}

	// Cache the result
	// Lưu kết quả vào cache để tăng tốc cho lần sau
	if vs.cache != nil {
		if err := vs.cacheResult(ctx, cacheKey, result); err != nil {
			result.Warnings = append(result.Warnings, "Failed to cache validation result")
		}
	}

	return result, nil
}

// checkUserLicenseAssociation checks if user is associated with license
// Kiểm tra user có liên kết với license không qua bảng license_users
// Hiện tại chưa implement vì bảng license_users chưa có trong schema mới
func (vs *ValidationService) checkUserLicenseAssociation(_ context.Context, _, _ string) bool {
	// TODO: Implement when license_users many-to-many table is available
	// Would query: SELECT 1 FROM license_users WHERE license_id = ? AND user_id = ?
	return false
}

// validateEntitlements checks if license has all required entitlements
// Kiểm tra license có đủ các entitlement/quyền cần thiết không
// Hiện tại trả về true vì entitlements đã bị loại bỏ trong schema mới
func (vs *ValidationService) validateEntitlements(_ context.Context, _ *entities.License, _ []string) bool {
	// TODO: Implement when entitlements repository is available
	// Would query license.Entitlements and check if all required codes exist
	return true
}

// validateCore performs core validation logic mapping from Ruby LicenseValidationService.validate!
// Thực hiện logic validation chính - mapping chính xác từ Ruby LicenseValidationService.validate!
// Thứ tự kiểm tra: banned → suspended → expired → overdue → scope validations
// Trả về: ValidationCode thay vì magic strings
func (vs *ValidationService) validateCore(ctx context.Context, license *entities.License, organization *entities.Organization, policy *entities.Policy, scope *ValidationScope) (bool, string, string) {
	// Legacy method - deprecated, use validateCoreClean instead
	// TODO: Remove this method once all callers are updated
	code := vs.validateCoreClean(ctx, license, organization, policy, scope)
	if code.IsValid() {
		return true, string(MessageValid), string(ValidationCodeValid)
	}
	return false, string(GetMessage(code)), string(code)
}

// validateCoreClean performs core validation logic using ValidationCode
func (vs *ValidationService) validateCoreClean(ctx context.Context, license *entities.License, organization *entities.Organization, policy *entities.Policy, scope *ValidationScope) ValidationCode {
	// Step 1: Check if license's owner has been banned (matches Ruby logic)
	// Ruby: license.banned? checks if license.owner&.banned_at? is set
	// Bước 1: Kiểm tra user sở hữu license có bị ban không
	if license.OwnerID != "" {
		userUUID, err := uuid.Parse(license.OwnerID)
		if err == nil {
			user, err := vs.userRepo.GetByID(ctx, userUUID)
			if err == nil && user != nil && user.BannedAt != nil && !user.BannedAt.IsZero() {
				return ValidationCodeBanned
			}
		}
	}

	// Step 2: Check if license is suspended
	// Bước 2: Kiểm tra license có bị tạm ngưng không
	if license.Suspended {
		return ValidationCodeSuspended
	}

	// Step 3: Check if license is expired (higher precedence when revoking access)
	// Implement revoke_access logic based on policy expiration strategy
	// Bước 3: Kiểm tra license có hết hạn không - ưu tiên cao khi thu hồi quyền truy cập
	if license.ExpiresAt != nil && time.Now().After(*license.ExpiresAt) {
		// Check policy expiration strategy for revoke_access behavior
		if policy != nil && policy.ExpirationStrategy != nil {
			switch *policy.ExpirationStrategy {
			case "REVOKE_ACCESS":
				// Immediately revoke access when expired (Ruby: license.revoke_access? && license.expired?)
				// Thu hồi quyền ngay khi hết hạn
				return ValidationCodeExpired
			case "MAINTAIN_ACCESS":
				// Allow continued access even when expired (Ruby: !license.revoke_access?)
				// Continue validation but mark as expired
				// Vẫn cho phép truy cập ngay cả khi hết hạn
				break
			default:
				// Default behavior: revoke access when expired (Ruby: backwards compat)
				// Hành vi mặc định: thu hồi quyền khi hết hạn
				return ValidationCodeExpired
			}
		} else {
			// No policy or strategy: default to revoking access
			// Không có policy hoặc chiến lược: mặc định thu hồi quyền
			return ValidationCodeExpired
		}
	}

	// Step 4: Check if license is overdue for check in (Ruby: license.check_in_overdue?)
	// Bước 4: Kiểm tra license có quá hạn check-in không
	if policy != nil && policy.RequireCheckIn {
		// Ruby logic: check_in_interval_count? && check_in_interval? && last_check_in_at? && requires_check_in?
		// Logic Ruby: kiểm tra có đủ thông tin để tính toán deadline check-in
		if license.LastCheckInAt != nil && policy.CheckInInterval != nil && policy.CheckInIntervalCount != nil {
			// Ruby: last_check_in_at < check_in_interval_count.send(check_in_interval).ago
			count := *policy.CheckInIntervalCount
			interval := *policy.CheckInInterval

			// Calculate duration based on Ruby's time helpers (only 4 valid intervals)
			// Tính toán thời gian dựa trên Ruby time helpers (chỉ 4 interval hợp lệ)
			var checkInDuration time.Duration
			switch interval {
			case "day":
				// 1 ngày = 24 giờ
				checkInDuration = time.Duration(count) * 24 * time.Hour
			case "week":
				// 1 tuần = 7 ngày
				checkInDuration = time.Duration(count) * 7 * 24 * time.Hour
			case "month":
				// Approximate month as 30 days (Ruby uses similar approximation)
				// Xấp xỉ 1 tháng = 30 ngày (Ruby cũng dùng cách tính tương tự)
				checkInDuration = time.Duration(count) * 30 * 24 * time.Hour
			case "year":
				// Approximate year as 365 days
				// Xấp xỉ 1 năm = 365 ngày
				checkInDuration = time.Duration(count) * 365 * 24 * time.Hour
			default:
				// Invalid interval, skip check (Ruby validation should prevent this)
				// Interval không hợp lệ, bỏ qua kiểm tra (Ruby validation nên ngăn chặn điều này)
				break
			}

			// Ruby: last_check_in_at < deadline_time_ago
			// Kiểm tra thời gian check-in cuối có quá deadline không
			if checkInDuration > 0 && time.Since(*license.LastCheckInAt) > checkInDuration {
				return ValidationCodeOverdue
			}
		}
	}

	// Step 5: Scope validations (only if scope is not explicitly false)
	// For non-strict policies, skip scope validation if auto-registration just happened
	// Bước 5: Xác thực scope - chỉ khi scope không bị tắt
	if scope != nil {
		// Skip scope validation for non-strict policies with fingerprint scope (auto-registration handles this)
		// Bỏ qua scope validation cho non-strict policies có fingerprint (auto-registration xử lý)
		skipScopeValidation := policy != nil && !policy.Strict && scope.Fingerprint != nil

		if !skipScopeValidation {
			// Thực hiện các kiểm tra scope cụ thể
			if code := vs.validateScopes(ctx, license, organization, policy, scope); !code.IsValid() {
				return code
			}
		}
	}

	// Step 6: Check if license has exceeded its user limit (matches Ruby logic exactly)
	// Bước 6: Kiểm tra license có vượt quá giới hạn user không
	if code := vs.validateUserLimitsWithOverage(ctx, license, policy); !code.IsValid() {
		return code
	}

	// Step 7: Check if license policy is strict (Ruby logic)
	// Bước 7: Kiểm tra policy có strict không - quyết định có cần kiểm tra machine requirements
	if policy != nil {
		// Check if policy is strict - if not strict, skip machine validations
		// Kiểm tra policy có strict không - nếu không strict thì bỏ qua machine validations
		isStrict := policy.Strict

		// Ruby logic: unless license.policy.strict? (exit early if not strict)
		// Logic Ruby: trừ khi policy strict (thoát sớm nếu không strict)
		if !isStrict {
			// Check if license is expired after checking machine requirements (Ruby logic)
			// Kiểm tra license hết hạn sau khi check machine requirements
			if license.ExpiresAt != nil && time.Now().After(*license.ExpiresAt) {
				// Ruby: license.allow_access? || license.maintain_access?
				// Check policy expiration strategy
				// Kiểm tra chiến lược hết hạn của policy
				if policy.ExpirationStrategy != nil && *policy.ExpirationStrategy == "MAINTAIN_ACCESS" {
					return ValidationCodeValid
				}
				return ValidationCodeExpired
			}
			return ValidationCodeValid
		}

		// Step 8: Machine validation for strict policies (Ruby strict logic)
		// Bước 8: Validation machine cho strict policies
		if code := vs.validateStrictMachineRequirements(ctx, license, policy, scope); !code.IsValid() {
			return code
		}
	}

	// Step 10: Check CPU core limits
	// Bước 10: Kiểm tra giới hạn CPU core
	if code := vs.validateCoreLimits(ctx, license, policy); !code.IsValid() {
		return code
	}

	// Step 11: Check process limits
	// Bước 11: Kiểm tra giới hạn process
	if code := vs.validateProcessLimits(ctx, license, policy, scope); !code.IsValid() {
		return code
	}

	// Step 12: Final expiration check after machine requirements
	// Bước 12: Kiểm tra hết hạn cuối cùng sau khi đã check machine requirements
	if license.ExpiresAt != nil && time.Now().After(*license.ExpiresAt) {
		// Ruby logic: license.allow_access? || license.maintain_access?
		// Check policy expiration strategy
		// Kiểm tra có cho phép truy cập hay duy trì truy cập không
		if policy != nil && policy.ExpirationStrategy != nil && *policy.ExpirationStrategy == "MAINTAIN_ACCESS" {
			return ValidationCodeValid
		}
		return ValidationCodeExpired
	}

	// All validations passed
	// Tất cả các kiểm tra đều thành công
	return ValidationCodeValid
}

// validateScopes performs scope validation mapping from Ruby scope validation logic
// Thực hiện validation scope - mapping từ Ruby scope validation logic
// Kiểm tra các điều kiện scope: product, policy, user, entitlements, machine
// Mỗi scope có thể required hoặc optional tùy theo policy configuration
func (vs *ValidationService) validateScopes(ctx context.Context, license *entities.License, _ *entities.Organization, policy *entities.Policy, scope *ValidationScope) ValidationCode {
	// Product scope validation
	// Kiểm tra scope product - license có thuộc product yêu cầu không
	if scope.Product != nil {
		if license.ProductID != *scope.Product {
			return ValidationCodeProductScopeMismatch
		}
	} else if policy != nil && policy.RequireProductScope {
		return ValidationCodeProductScopeRequired
	}

	// Policy scope validation
	// Kiểm tra scope policy - license có sử dụng policy yêu cầu không
	if scope.Policy != nil {
		if license.PolicyID != *scope.Policy {
			return ValidationCodePolicyScopeMismatch
		}
	} else if policy != nil && policy.RequirePolicyScope {
		return ValidationCodePolicyScopeRequired
	}

	// User scope validation (Ruby lines 77-90)
	// Kiểm tra scope user - user có quyền sử dụng license này không
	if scope.User != nil {
		// Check if license owner matches or user is associated with license
		// Kiểm tra user có phải owner hoặc có liên kết với license không
		userMatches := false

		// Ruby: license.owner_id == user_identifier
		// Trước tiên kiểm tra user có phải owner của license không
		if license.OwnerID != "" && license.OwnerID == *scope.User {
			userMatches = true
		} else {
			// Ruby: license.users.where(id: user_identifier).or(license.users.where(email: user_identifier)).exists?
			// Check if user is in license.users many-to-many relationship
			// First try to find by ID
			// Kiểm tra user có trong bảng license_users many-to-many không
			userMatches = vs.checkUserLicenseAssociation(ctx, license.ID, *scope.User)

			// If not found by ID, try to find by email
			// Nếu không tìm thấy theo ID, thử tìm theo email
			if !userMatches && vs.userRepo != nil {
				// Try to find user by email
				// Tìm user theo email trong hệ thống
				user, err := vs.userRepo.GetByEmail(ctx, *scope.User)
				if err == nil && user != nil {
					// Check if this user is associated with the license
					// Kiểm tra user này có liên kết với license không
					userMatches = vs.checkUserLicenseAssociation(ctx, license.ID, user.ID)
				}
			}
		}
		if !userMatches {
			return ValidationCodeUserScopeMismatch
		}
	} else if policy != nil && policy.RequireUserScope {
		return ValidationCodeUserScopeRequired
	}

	// Entitlements scope validation
	// Kiểm tra scope entitlements - license có đủ quyền/tính năng yêu cầu không
	if scope.Entitlements != nil {
		// Ruby logic: entitlements.empty? check
		// Kiểm tra danh sách entitlements có rỗng không
		if len(scope.Entitlements) == 0 {
			return ValidationCodeEntitlementsScopeEmpty
		}

		// Ruby logic: license.entitlements.where(code: entitlements).count != entitlements.size
		// Kiểm tra license có đủ tất cả entitlements yêu cầu không
		if !vs.validateEntitlements(ctx, license, scope.Entitlements) {
			return ValidationCodeEntitlementsMissing
		}
	}

	// Machine scope validation (matches Ruby logic exactly)
	// Kiểm tra scope machine - license có được sử dụng trên machine này không
	if scope.Machine != nil {
		licenseUUID, err := uuid.Parse(license.ID)
		if err != nil {
			return ValidationCodeInvalidLicense
		}

		machines, err := vs.machineRepo.GetByLicense(ctx, licenseUUID)
		if err != nil {
			return ValidationCodeDatabaseError
		}

		machineCount := len(machines)

		// Ruby logic: Check floating vs node-locked behavior
		isFloating := policy != nil && policy.Floating

		// Check machine count requirements (matches Ruby case statements)
		switch {
		case !isFloating && machineCount == 0:
			return ValidationCodeNoMachine
		case isFloating && machineCount == 0:
			return ValidationCodeNoMachines
		default:
			// Find specific machine by ID
			var targetMachine *entities.Machine
			for _, machine := range machines {
				if machine.ID == *scope.Machine {
					targetMachine = machine
					break
				}
			}

			if targetMachine == nil {
				return ValidationCodeMachineScopeMismatch
			}

			// Check user scope against machine owner (Ruby logic)
			if scope.User != nil {
				userMatches := targetMachine.OwnerID == nil ||
					(targetMachine.OwnerID != nil && *targetMachine.OwnerID == *scope.User)
				if !userMatches {
					return ValidationCodeUserScopeMismatch
				}
			}

			// Check heartbeat requirements (Ruby logic)
			if policy != nil && policy.RequireHeartbeat {
				if targetMachine.LastHeartbeatAt == nil {
					return ValidationCodeHeartbeatNotStarted
				}

				// Check if machine is dead (Ruby: machine.dead?)
				// For now, simple check - machine is dead if heartbeat is too old
				if policy.HeartbeatDuration != nil {
					heartbeatDuration := time.Duration(*policy.HeartbeatDuration) * time.Second
					if time.Since(*targetMachine.LastHeartbeatAt) > heartbeatDuration {
						return ValidationCodeHeartbeatDead
					}
				}
			}
		}
	} else if policy != nil && policy.RequireMachineScope {
		return ValidationCodeMachineScopeRequired
	}

	// Fingerprint scope validation (matches Ruby logic exactly)
	if scope.Fingerprint != nil || len(scope.Fingerprints) > 0 {
		licenseUUID, err := uuid.Parse(license.ID)
		if err != nil {
			return ValidationCodeInvalidLicense
		}

		machines, err := vs.machineRepo.GetByLicense(ctx, licenseUUID)
		if err != nil {
			return ValidationCodeDatabaseError
		}

		// Collect all fingerprints to check
		fingerprints := scope.Fingerprints
		if scope.Fingerprint != nil {
			fingerprints = append(fingerprints, *scope.Fingerprint)
		}

		if len(fingerprints) == 0 {
			return ValidationCodeFingerprintScopeEmpty
		}

		machineCount := len(machines)
		isFloating := policy != nil && policy.Floating

		// Ruby logic: Check machine count requirements first
		switch {
		case !isFloating && machineCount == 0:
			return ValidationCodeNoMachine
		case isFloating && machineCount == 0:
			return ValidationCodeNoMachines
		default:
			// Find machines with matching fingerprints
			var matchingMachines []*entities.Machine
			var aliveMachines []*entities.Machine

			for _, machine := range machines {
				for _, fp := range fingerprints {
					if machine.Fingerprint == fp {
						matchingMachines = append(matchingMachines, machine)

						// Check if machine is alive (not dead from heartbeat)
						if !vs.isMachineDeadFromHeartbeat(machine, policy) {
							aliveMachines = append(aliveMachines, machine)
						}
						break
					}
				}
			}

			// Check if all machines are dead
			if len(matchingMachines) > 0 && len(aliveMachines) == 0 {
				return ValidationCodeHeartbeatDead
			}

			// Apply Ruby fingerprint matching strategies
			if len(fingerprints) > 1 && policy != nil {
				requiredMatches := vs.calculateRequiredMatches(len(fingerprints), policy)
				if len(aliveMachines) < requiredMatches {
					return ValidationCodeFingerprintScopeMismatch
				}
			} else {
				// Single fingerprint or no specific strategy
				if len(aliveMachines) == 0 {
					return ValidationCodeFingerprintScopeMismatch
				}
			}

			// Check user scope against machine owners (Ruby logic)
			if scope.User != nil {
				userMatches := true
				for _, machine := range aliveMachines {
					if machine.OwnerID != nil && *machine.OwnerID != *scope.User {
						userMatches = false
						break
					}
				}
				if !userMatches {
					return ValidationCodeUserScopeMismatch
				}
			}

			// Check heartbeat requirements for alive machines
			if policy != nil && policy.RequireHeartbeat {
				for _, machine := range aliveMachines {
					if machine.LastHeartbeatAt == nil {
						return ValidationCodeHeartbeatNotStarted
					}
				}
			}
		}
	} else if policy != nil && policy.RequireFingerprintScope {
		return ValidationCodeFingerprintScopeRequired
	}

	// Components scope validation (matches Ruby logic)
	if len(scope.Components) > 0 {
		// Ruby requires fingerprint scope when using components scope
		if scope.Fingerprint == nil {
			return ValidationCodeFingerprintScopeRequired
		}

		// Ruby logic: fingerprints = scope[:components].compact.uniq (lines 190-192)
		componentFingerprints := scope.Components
		if len(componentFingerprints) == 0 {
			return ValidationCodeComponentsScopeEmpty
		}

		// Ruby logic: machine.components.with_fingerprint(fingerprints) (lines 198-200)
		// TODO: Implement when machine components repository is available
		// For now, we'll implement the matching strategy logic

		// Placeholder: assume we have component count
		componentCount := 0 // In real implementation: machine.components.with_fingerprint(fingerprints).count

		// Apply component matching strategies (Ruby lines 202-214)
		if policy != nil && policy.ComponentMatchingStrategy != nil {
			switch *policy.ComponentMatchingStrategy {
			case "match_most":
				requiredCount := (len(componentFingerprints) + 1) / 2 // Ceiling of half
				if componentCount < requiredCount {
					return ValidationCodeComponentsScopeMismatch
				}
			case "match_two":
				if componentCount < 2 {
					return ValidationCodeComponentsScopeMismatch
				}
			case "match_all":
				if componentCount < len(componentFingerprints) {
					return ValidationCodeComponentsScopeMismatch
				}
			default: // match_any or unspecified
				if componentCount == 0 {
					return ValidationCodeComponentsScopeMismatch
				}
			}
		} else {
			// Default behavior: match_any
			if componentCount == 0 {
				return ValidationCodeComponentsScopeMismatch
			}
		}
	} else if policy != nil && policy.RequireComponentsScope {
		return ValidationCodeComponentsScopeRequired
	}

	// Checksum scope validation (matches Ruby logic)
	if scope.Checksum != nil {
		// Ruby logic: product.release_artifacts.with_checksum(checksum).for_license(license).order_by_version.published.uploaded.take
		// This requires artifact repository and complex querying
		// For now, assume checksum is valid (placeholder)
		// In full implementation: check if checksum matches any accessible artifacts
	} else if policy != nil && policy.RequireChecksumScope {
		return ValidationCodeChecksumScopeRequired
	}

	// Version scope validation (matches Ruby logic)
	if scope.Version != nil {
		// Ruby logic: product.releases.with_version(version).for_license(license).published.take
		// This requires release repository and complex querying
		// For now, assume version is valid (placeholder)
		// In full implementation: check if version matches any accessible releases
	} else if policy != nil && policy.RequireVersionScope {
		return ValidationCodeVersionScopeRequired
	}

	return ValidationCodeValid
}

// autoRegisterMachine automatically registers a machine during validation if it doesn't exist (Ruby behavior)
func (vs *ValidationService) autoRegisterMachine(ctx context.Context, license *entities.License, fingerprint string) error {
	licenseUUID, err := uuid.Parse(license.ID)
	if err != nil {
		return err
	}

	// Check if machine already exists for this license and fingerprint
	machines, err := vs.machineRepo.GetByLicense(ctx, licenseUUID)
	if err != nil {
		return err
	}

	// Check if a machine with this fingerprint already exists
	for _, machine := range machines {
		if machine.Fingerprint == fingerprint {
			return nil // Machine already exists, no need to register
		}
	}

	// Create new machine
	now := time.Now()
	machine := &entities.Machine{
		LicenseID:   license.ID,
		PolicyID:    license.PolicyID,
		Fingerprint: fingerprint,
		Name:        func() *string { s := "Auto-registered Machine"; return &s }(),
		Platform:    func() *string { s := "unknown"; return &s }(),
		Hostname:    func() *string { s := "unknown"; return &s }(),
		IP:          func() *string { s := "0.0.0.0"; return &s }(),
		Cores:       1,
		ActivatedAt: &now,
	}

	return vs.machineRepo.Create(ctx, machine)
}

// populateUsageStats populates usage statistics in validation result
func (vs *ValidationService) populateUsageStats(ctx context.Context, result *ValidationResult, license *entities.License) {
	licenseUUID, err := uuid.Parse(license.ID)
	if err != nil {
		return
	}

	// Get machines
	machines, err := vs.machineRepo.GetByLicense(ctx, licenseUUID)
	if err == nil {
		result.MachinesUsed = len(machines)

		// Count total cores
		totalCores := 0
		for _, machine := range machines {
			if machine.Cores > 0 {
				totalCores += machine.Cores
			}
		}
		result.CoresUsed = totalCores
	}

	// Set limits from policy or license overrides
	if result.Policy != nil {
		if result.Policy.MaxMachines != nil {
			result.MachinesAllowed = *result.Policy.MaxMachines
		}
		if result.Policy.MaxCores != nil {
			result.CoresAllowed = *result.Policy.MaxCores
		}
		// TODO: Set processes and users limits
	}

	// Apply license-specific overrides
	if license.MaxMachinesOverride != nil {
		result.MachinesAllowed = *license.MaxMachinesOverride
	}
	if license.MaxCoresOverride != nil {
		result.CoresAllowed = *license.MaxCoresOverride
	}
	// TODO: Apply other overrides
}

// touchLicense updates license timestamps (maps to Ruby TouchLicenseWorker)
func (vs *ValidationService) touchLicense(ctx context.Context, license *entities.License, scope *ValidationScope) {
	licenseUUID, err := uuid.Parse(license.ID)
	if err != nil {
		return
	}

	// Update last validated timestamp
	vs.licenseRepo.UpdateLastValidated(ctx, licenseUUID)

	// Update other touch fields based on scope (Ruby lines 233-234, 251)
	// Ruby logic: touches[:last_validated_checksum] = checksum
	if scope != nil && scope.Checksum != nil {
		// In Ruby, this is stored in touches hash and sent to TouchLicenseWorker
		// For Go, we would need to implement UpdateLastValidatedChecksum method
		// TODO: license.LastValidatedChecksum = scope.Checksum
		// TODO: vs.licenseRepo.UpdateLastValidatedChecksum(ctx, licenseUUID, *scope.Checksum)
	}

	// Ruby logic: touches[:last_validated_version] = version
	if scope != nil && scope.Version != nil {
		// In Ruby, this is stored in touches hash and sent to TouchLicenseWorker
		// For Go, we would need to implement UpdateLastValidatedVersion method
		// TODO: license.LastValidatedVersion = scope.Version
		// TODO: vs.licenseRepo.UpdateLastValidatedVersion(ctx, licenseUUID, *scope.Version)
	}

	// Note: In Ruby, this is handled asynchronously via TouchLicenseWorker.perform_async
	// In Go, we're doing synchronous updates for now
}

// buildCacheKeyWithScope builds cache key with scope parameters
func (vs *ValidationService) buildCacheKeyWithScope(licenseKey string, scope *ValidationScope) string {
	key := fmt.Sprintf("license_validation:%s", licenseKey)

	if scope != nil {
		if scope.Fingerprint != nil {
			key += fmt.Sprintf(":fp:%s", *scope.Fingerprint)
		}
		if scope.Machine != nil {
			key += fmt.Sprintf(":machine:%s", *scope.Machine)
		}
		if scope.User != nil {
			key += fmt.Sprintf(":user:%s", *scope.User)
		}
		// TODO: Add other scope parameters to cache key
	}

	return key
}

// validateLicenseFormat validates the license format and extracts claims
func (vs *ValidationService) validateLicenseFormat(licenseKey string) (map[string]any, error) {
	// Try to parse as JWT first
	header, claims, err := vs.crypto.JWT.ParseTokenWithoutVerification(licenseKey)
	if err == nil {
		// It's a JWT token
		claimsMap := make(map[string]any)
		claimsMap["format"] = "jwt"
		claimsMap["algorithm"] = header["alg"]
		claimsMap["type"] = header["typ"]

		// Add all JWT claims
		for k, v := range claims {
			claimsMap[k] = v
		}

		return claimsMap, nil
	}

	// If not JWT, treat as simple license key
	if len(licenseKey) < 10 {
		return nil, fmt.Errorf("license key too short")
	}

	return map[string]any{
		"format": "simple",
		"length": len(licenseKey),
	}, nil
}

// getFromCache retrieves validation result from cache
func (vs *ValidationService) getFromCache(ctx context.Context, key string) (*ValidationResult, error) {
	if vs.cache == nil {
		return nil, fmt.Errorf("cache not configured")
	}

	exists, err := vs.cache.Exists(ctx, key)
	if err != nil || !exists {
		return nil, err
	}

	data, err := vs.cache.Get(ctx, key)
	if err != nil {
		return nil, err
	}

	var result ValidationResult
	if err := json.Unmarshal([]byte(data), &result); err != nil {
		return nil, err
	}

	// Check if cached result is still fresh
	if time.Since(result.ValidationTime) > vs.cacheTTL {
		vs.cache.Delete(ctx, key)
		return nil, fmt.Errorf("cached result expired")
	}

	return &result, nil
}

// cacheResult stores validation result in cache
func (vs *ValidationService) cacheResult(ctx context.Context, key string, result *ValidationResult) error {
	if vs.cache == nil {
		return fmt.Errorf("cache not configured")
	}

	data, err := json.Marshal(result)
	if err != nil {
		return err
	}

	return vs.cache.Set(ctx, key, string(data), vs.cacheTTL)
}

// InvalidateCache invalidates cache entries for a license
func (vs *ValidationService) InvalidateCache(ctx context.Context, licenseKey string) error {
	if vs.cache == nil {
		return nil
	}

	// Delete base cache key
	baseKey := fmt.Sprintf("license_validation:%s", licenseKey)
	return vs.cache.Delete(ctx, baseKey)
}

// ValidateLicenseQuick performs a quick validation without full database lookups
func (vs *ValidationService) ValidateLicenseQuick(ctx context.Context, licenseKey string) (bool, error) {
	// Check cache first
	cacheKey := vs.buildCacheKeyWithScope(licenseKey, nil)
	if cachedResult, err := vs.getFromCache(ctx, cacheKey); err == nil && cachedResult != nil {
		return cachedResult.Valid, nil
	}

	// Basic format validation
	_, err := vs.validateLicenseFormat(licenseKey)
	if err != nil {
		return false, err
	}

	// Check if license exists in database
	license, err := vs.licenseRepo.GetByKey(ctx, licenseKey)
	if err != nil {
		return false, nil
	}

	// Basic status check
	if license.Status != "active" {
		return false, nil
	}

	if license.Suspended {
		return false, nil
	}

	if license.ExpiresAt != nil && time.Now().After(*license.ExpiresAt) {
		return false, nil
	}

	return true, nil
}

// GetLicenseInfo returns basic license information without full validation
func (vs *ValidationService) GetLicenseInfo(ctx context.Context, licenseKey string) (map[string]any, error) {
	claims, err := vs.validateLicenseFormat(licenseKey)
	if err != nil {
		return nil, err
	}

	license, err := vs.licenseRepo.GetByKey(ctx, licenseKey)
	if err != nil {
		return claims, nil // Return format info even if license not found
	}

	info := make(map[string]any)
	for k, v := range claims {
		info[k] = v
	}

	info["status"] = license.Status
	info["organization_id"] = license.OrganizationID
	info["product_id"] = license.ProductID

	if license.ExpiresAt != nil {
		info["expires_at"] = license.ExpiresAt
		info["expired"] = time.Now().After(*license.ExpiresAt)
	}

	info["suspended"] = license.Suspended

	return info, nil
}

// validateCoreLimits checks CPU core limits with overage handling
func (vs *ValidationService) validateCoreLimits(ctx context.Context, license *entities.License, policy *entities.Policy) ValidationCode {
	// Get effective core limit (license override or policy default)
	coreLimit, hasLimit := vs.getLicenseLimit(license.MaxCoresOverride, policy.MaxCores)
	if !hasLimit {
		return ValidationCodeValid
	}

	licenseUUID, err := uuid.Parse(license.ID)
	if err != nil {
		return ValidationCodeInvalidLicense
	}

	machines, err := vs.machineRepo.GetByLicense(ctx, licenseUUID)
	if err != nil {
		return ValidationCodeDatabaseError
	}

	// Count total cores used
	totalCores := 0
	for _, machine := range machines {
		if machine.Cores > 0 {
			totalCores += machine.Cores
		}
	}

	// Apply overage strategy using helper
	effectiveLimit := vs.getEffectiveLimitWithOverage(policy, coreLimit)
	if totalCores > effectiveLimit {
		return ValidationCodeTooManyCores
	}

	return ValidationCodeValid
}

// validateProcessLimits checks process limits with overage handling
func (vs *ValidationService) validateProcessLimits(_ context.Context, license *entities.License, policy *entities.Policy, scope *ValidationScope) ValidationCode {
	// Get effective process limit (license override or policy default)
	processLimit, hasLimit := vs.getLicenseLimit(license.MaxProcessesOverride, policy.MaxProcesses)
	if !hasLimit {
		return ValidationCodeValid
	}

	var processCount int

	// Ruby logic: Check process leasing strategy
	if policy != nil && policy.ProcessLeasingStrategy != nil {
		switch *policy.ProcessLeasingStrategy {
		case "PER_MACHINE":
			// Ruby: process_lease_per_machine? logic (lines 372-384)
			if scope != nil && (scope.Fingerprint != nil || len(scope.Fingerprints) > 0) {
				// Find machine by fingerprint
				// fingerprint := ""
				// if scope.Fingerprint != nil {
				// 	fingerprint = *scope.Fingerprint
				// } else if len(scope.Fingerprints) > 0 {
				// 	fingerprint = scope.Fingerprints[0]
				// }

				// TODO: Get machine by fingerprint and count its processes
				// For now, we'll use placeholder logic
				// In full implementation:
				// machine = license.machines.alive.find_by(fingerprint: fingerprint)
				// processCount = machine.processes.count
				// processLimit = machine.max_processes
			} else if scope != nil && scope.Machine != nil {
				// Ruby: scope.key?(:machine) logic
				// TODO: Get machine by ID and count its processes
				// machine = license.machines.alive.find_by(id: scope[:machine])
				// processCount = machine.processes.count
				// processLimit = machine.max_processes
			}

		case "PER_LICENSE":
			// Ruby: process_lease_per_license? logic (lines 385-387)
			// processCount = license.processes.count
			// processLimit = license.max_processes
			// TODO: Implement when process repository is available

		case "PER_USER":
			// Ruby: process_lease_per_user? logic (lines 388-400)
			if scope != nil && scope.User != nil {
				// TODO: Count processes for specific user
				// owner = license.users.where(id: scope[:user]).or(license.users.where(email: scope[:user])).take
				// processCount = license.processes.left_outer_joins(:owner).where(owner: { id: owner }).count
			}
		}
	}

	// Apply overage logic using helper
	if processCount > processLimit {
		allowOverage := vs.checkOverageAllowed(policy, processCount, processLimit)
		if !allowOverage {
			return ValidationCodeTooManyProcesses
		}
	}

	return ValidationCodeValid
}

// validateUserLimitsWithOverage checks user limits with Ruby overage logic
func (vs *ValidationService) validateUserLimitsWithOverage(_ context.Context, license *entities.License, policy *entities.Policy) ValidationCode {
	// Get effective user limit (license override or policy default)
	userLimit, hasLimit := vs.getLicenseLimit(license.MaxUsersOverride, policy.MaxUsers)
	if !hasLimit {
		return ValidationCodeValid
	}

	// Use cached count (Ruby: license.users_count)
	userCount := license.LicenseUsersCount
	if userCount == 0 {
		// TODO: Implement actual user counting from license_users table
		// For now, assume no users if count is 0
	}

	// Ruby logic: if license.users_count > license.max_users
	if userCount > userLimit {
		// Check overage using reusable helper
		allowOverage := vs.checkOverageAllowed(policy, userCount, userLimit)
		if !allowOverage {
			return ValidationCodeTooManyUsers
		}
	}

	return ValidationCodeValid
}

// validateStrictMachineRequirements implements Ruby strict machine validation logic
func (vs *ValidationService) validateStrictMachineRequirements(ctx context.Context, license *entities.License, policy *entities.Policy, scope *ValidationScope) ValidationCode {
	licenseUUID, err := uuid.Parse(license.ID)
	if err != nil {
		return ValidationCodeInvalidLicense
	}

	machines, err := vs.machineRepo.GetByLicense(ctx, licenseUUID)
	if err != nil {
		return ValidationCodeDatabaseError
	}

	machineCount := len(machines)
	isFloating := policy.Floating

	// Ruby logic: Check if license policy allows floating and if not, should have single activation
	if !isFloating && machineCount == 0 {
		return ValidationCodeNoMachine
	}

	// Ruby logic: When node-locked, license's machine count should not surpass 1
	if !isFloating && machineCount > 1 {
		// Ruby: machine_limit = license.max_machines || 1
		machineLimit, _ := vs.getLicenseLimit(license.MaxMachinesOverride, policy.MaxMachines)
		if machineLimit == 0 {
			machineLimit = 1 // Default for node-locked
		}

		// Ruby logic: machine_lease_per_user? case handling
		actualMachineCount := machineCount
		if policy.MachineLeasingStrategy != nil && *policy.MachineLeasingStrategy == "PER_USER" && scope != nil && scope.User != nil {
			// Count machines for specific user
			userMachineCount := 0
			for _, machine := range machines {
				if machine.OwnerID != nil && *machine.OwnerID == *scope.User {
					userMachineCount++
				}
			}
			actualMachineCount = userMachineCount
		}

		// Ruby overage logic for node-locked: only allow 2x overage
		if actualMachineCount > machineLimit {
			// For node-locked, only ALWAYS_ALLOW_OVERAGE and ALLOW_2X_OVERAGE are supported
			allowOverage := false
			if policy.OverageStrategy != nil {
				switch *policy.OverageStrategy {
				case "ALWAYS_ALLOW_OVERAGE":
					allowOverage = true
				case "ALLOW_2X_OVERAGE":
					allowOverage = actualMachineCount <= machineLimit*2
				}
			}
			if !allowOverage {
				return ValidationCodeTooManyMachines
			}
		}
	}

	// Ruby logic: When floating, license should have at least 1 activation
	if isFloating && machineCount == 0 {
		return ValidationCodeNoMachines
	}

	// Ruby logic: When floating, license's machine count should not surpass what policy allows
	// Ruby: if license.floating? && license.max_machines? && license.machines_count > 1
	machineLimit, hasLimit := vs.getLicenseLimit(license.MaxMachinesOverride, policy.MaxMachines)
	if isFloating && hasLimit && machineCount > 1 {

		// Ruby logic: machine_lease_per_user? case handling
		actualMachineCount := machineCount
		if policy.MachineLeasingStrategy != nil && *policy.MachineLeasingStrategy == "PER_USER" && scope != nil && scope.User != nil {
			// Count machines for specific user
			userMachineCount := 0
			for _, machine := range machines {
				if machine.OwnerID != nil && *machine.OwnerID == *scope.User {
					userMachineCount++
				}
			}
			actualMachineCount = userMachineCount
		}

		// Ruby overage logic for floating: 1.25x, 1.5x, 2x
		if actualMachineCount > machineLimit {
			allowOverage := vs.checkOverageAllowed(policy, actualMachineCount, machineLimit)
			if !allowOverage {
				return ValidationCodeTooManyMachines
			}
		}
	}

	return ValidationCodeValid
}

// checkOverageAllowed validates if overage is allowed based on policy strategy
func (vs *ValidationService) checkOverageAllowed(policy *entities.Policy, currentCount, limit int) bool {
	if policy == nil || policy.OverageStrategy == nil {
		return false
	}

	switch *policy.OverageStrategy {
	case "ALWAYS_ALLOW_OVERAGE":
		return true
	case "ALLOW_1_25X_OVERAGE":
		return currentCount <= int(float64(limit)*1.25)
	case "ALLOW_1_5X_OVERAGE":
		return currentCount <= int(float64(limit)*1.5)
	case "ALLOW_2X_OVERAGE":
		return currentCount <= limit*2
	default:
		return false
	}
}

// getEffectiveLimitWithOverage calculates effective limit with overage strategy
func (vs *ValidationService) getEffectiveLimitWithOverage(policy *entities.Policy, baseLimit int) int {
	if policy == nil || policy.OverageStrategy == nil {
		return baseLimit
	}

	switch *policy.OverageStrategy {
	case "ALWAYS_ALLOW_OVERAGE":
		return 999999 // Very high limit for always allow
	case "ALLOW_1_25X_OVERAGE":
		return int(float64(baseLimit) * 1.25)
	case "ALLOW_1_5X_OVERAGE":
		return int(float64(baseLimit) * 1.5)
	case "ALLOW_2X_OVERAGE":
		return baseLimit * 2
	default:
		return baseLimit
	}
}

// getLicenseLimit gets license limit with override priority over policy
func (vs *ValidationService) getLicenseLimit(licenseOverride *int, policyLimit *int) (int, bool) {
	if licenseOverride != nil {
		return *licenseOverride, true
	}
	if policyLimit != nil {
		return *policyLimit, true
	}
	return 0, false
}

// isMachineDeadFromHeartbeat checks if machine is dead based on heartbeat timing
func (vs *ValidationService) isMachineDeadFromHeartbeat(machine *entities.Machine, policy *entities.Policy) bool {
	if machine.LastHeartbeatAt == nil {
		return false // Not started, not dead
	}

	if policy == nil || policy.HeartbeatDuration == nil {
		return false // No heartbeat policy
	}

	heartbeatDuration := time.Duration(*policy.HeartbeatDuration) * time.Second
	return time.Since(*machine.LastHeartbeatAt) > heartbeatDuration
}

// calculateRequiredMatches calculates required matches based on Ruby matching strategies
func (vs *ValidationService) calculateRequiredMatches(fingerprintCount int, policy *entities.Policy) int {
	if policy.MachineMatchingStrategy == nil {
		return 1 // Default: any match
	}

	switch *policy.MachineMatchingStrategy {
	case "match_most":
		return (fingerprintCount + 1) / 2 // Ceiling of half
	case "match_two":
		return 2
	case "match_all":
		return fingerprintCount
	case "match_any":
		return 1
	default:
		return 1
	}
}

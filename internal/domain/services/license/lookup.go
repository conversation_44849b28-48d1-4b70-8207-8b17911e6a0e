package license

import (
	"context"
	"crypto/sha256"
	"crypto/subtle"
	"fmt"
	"regexp"

	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/gokeys/gokeys/internal/domain/repositories"
	"github.com/google/uuid"
	"golang.org/x/crypto/bcrypt"
)

// LookupService handles license key lookup functionality (maps to Ruby LicenseKeyLookupService)
// Service tìm kiếm license theo key - tương thích với Ruby LicenseKeyLookupService
// Hỗ trợ cả legacy encrypted keys (v1) và modern keys để tương thích ngược
type LookupService struct {
	licenseRepo      repositories.LicenseRepository      // Repository truy cập license data
	organizationRepo repositories.OrganizationRepository // Repository truy cập organization data
}

// NewLookupService creates a new license lookup service
// Khởi tạo service lookup license với các repository cần thiết
func NewLookupService(
	licenseRepo repositories.LicenseRepository,
	organizationRepo repositories.OrganizationRepository,
) *LookupService {
	return &LookupService{
		licenseRepo:      licenseRepo,
		organizationRepo: organizationRepo,
	}
}

// LookupOptions represents lookup configuration
// Tùy chọn cho quá trình lookup - chủ yếu là xử lý legacy encrypted keys
type LookupOptions struct {
	LegacyEncrypted bool `json:"legacy_encrypted"` // Có xử lý legacy v1 encrypted keys không
}

// EncryptedKeyPattern matches the Ruby ENCRYPTED_KEY_RE pattern for legacy keys
// Ruby: /\A(?<license_id>.{#{UUID_LENGTH}})-(?<bits>.+)/ where UUID_LENGTH = 36
// Pattern cho legacy v1 encrypted keys: UUID-encrypted_bits
// Ví dụ: "12345678-1234-1234-1234-123456789012-abc123def456"
var EncryptedKeyPattern = regexp.MustCompile(`^([a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12})-(.+)$`)

// LookupLicense finds a license by key (maps to Ruby LicenseKeyLookupService.call)
// Tìm license theo key - mapping chính xác từ Ruby LicenseKeyLookupService.call
// Hỗ trợ 2 loại key: modern plaintext keys và legacy v1 encrypted keys
// Quy trình: Kiểm tra format → Xử lý theo loại key → Tìm kiếm trong DB
func (ls *LookupService) LookupLicense(ctx context.Context, organizationID, key string, options *LookupOptions) (*entities.License, error) {
	// Set default options
	// Đảm bảo luôn có options hợp lệ
	if options == nil {
		options = &LookupOptions{}
	}

	// Parse organization ID
	// Chuyển organizationID string thành UUID
	organizationUUID, err := uuid.Parse(organizationID)
	if err != nil {
		return nil, fmt.Errorf("invalid organization ID: %w", err)
	}

	// Get organization
	// Lấy thông tin organization để kiểm tra quyền và lấy salt
	organization, err := ls.organizationRepo.GetByID(ctx, organizationUUID)
	if err != nil {
		return nil, fmt.Errorf("organization not found: %w", err)
	}

	// Handle legacy encrypted keys (maps to Ruby legacy_encrypted logic)
	// Xử lý legacy encrypted keys - mapping từ Ruby legacy_encrypted logic
	if options.LegacyEncrypted {
		return ls.lookupLegacyEncryptedKey(ctx, organization, key)
	}

	// Standard key lookup
	// Tìm kiếm key thông thường
	return ls.lookupStandardKey(ctx, organization, key)
}

// lookupStandardKey handles standard license key lookup
// Xử lý tìm kiếm license key thông thường - so sánh trực tiếp
// Đây là phương thức chuẩn cho các key hiện đại
func (ls *LookupService) lookupStandardKey(ctx context.Context, organization *entities.Organization, key string) (*entities.License, error) {
	// Get licenses scoped to organization
	licenses, err := ls.getLicensesForOrganization(ctx, organization.ID)
	if err != nil {
		return nil, err
	}

	// Find license by key
	// Tìm license theo key - so sánh trực tiếp
	for _, license := range licenses {
		if license.Key == key {
			return &license, nil
		}
	}

	return nil, fmt.Errorf("license not found")
}

// lookupLegacyEncryptedKey handles legacy encrypted key lookup (maps to Ruby legacy_encrypted logic)
// Xử lý tìm kiếm legacy encrypted keys - mapping từ Ruby legacy_encrypted logic
// Xử lý các key v1 cũ có format UUID-encrypted_bits và sử dụng bcrypt+SHA256
func (ls *LookupService) lookupLegacyEncryptedKey(ctx context.Context, organization *entities.Organization, key string) (*entities.License, error) {
	// Parse encrypted key pattern
	// Phân tích pattern của encrypted key - tách UUID và encrypted bits
	matches := EncryptedKeyPattern.FindStringSubmatch(key)
	if len(matches) != 3 {
		return nil, fmt.Errorf("invalid encrypted key format")
	}

	licenseID := matches[1]
	// encryptedBits := matches[2] // Not used in current implementation
	// encryptedBits hiện tại không sử dụng trong implementation

	// Get licenses scoped to organization
	licenses, err := ls.getLicensesForOrganization(ctx, organization.ID)
	if err != nil {
		return nil, err
	}

	// Find license by ID
	for _, license := range licenses {
		if license.ID == licenseID {
			// Implement legacy key comparison logic (matches Ruby version)
			if ls.compareHashedToken(license.Key, key) {
				// Log deprecation warning (matches Ruby warning)
				fmt.Printf("[license-key-lookup-service] v1 keys are deprecated and must be regenerated: license_id=%s\n", license.ID)

				// Ruby JIT rehash logic: reuse salt from existing bcrypt hash
				// digest = BCrypt::Engine.hash_secret(Digest::SHA256.hexdigest(key), BCrypt::Password.new(license.key).salt)
				// unless license.send(:secure_compare, digest, license.key)
				//   license.update!(key: digest)
				// end

				// Extract salt from existing bcrypt hash
				cost, salt, _, err := ls.parseBcryptHash(license.Key)
				if err != nil {
					fmt.Printf("Failed to parse existing bcrypt hash for license %s: %v\n", license.ID, err)
					return &license, nil
				}

				// Create new digest with same salt (matches Ruby logic)
				hexDigest := ls.HashKeyLegacy(key)
				newDigest := ls.bcryptHashWithSalt(hexDigest, salt, cost)

				// Only rehash if digests don't match (Ruby: unless secure_compare)
				if !ls.secureCompare(newDigest, license.Key) {
					fmt.Printf("[license-key-lookup-service] rehashing key: license_id=%s\n", license.ID)

					// JIT rehash key (matches Ruby license.update!(key: digest))
					err := ls.updateLicenseKey(ctx, license.ID, newDigest)
					if err != nil {
						fmt.Printf("Failed to rehash legacy key for license %s: %v\n", license.ID, err)
					} else {
						// Update the license object with new key
						license.Key = newDigest
					}
				}

				return &license, nil
			}

			// Return nil if key verification fails (matches Ruby behavior)
			return nil, nil
		}
	}

	return nil, fmt.Errorf("license not found")
}

// getLicensesForOrganization gets licenses scoped to organization
func (ls *LookupService) getLicensesForOrganization(ctx context.Context, organizationID string) ([]entities.License, error) {
	organizationUUID, err := uuid.Parse(organizationID)
	if err != nil {
		return nil, fmt.Errorf("invalid organization ID: %w", err)
	}

	// Get all licenses for organization using List with filter
	filter := repositories.ListFilter{
		OrganizationID: &organizationUUID,
		PageSize:       1000, // Set reasonable limit
		Page:           1,
	}

	licensePointers, _, err := ls.licenseRepo.List(ctx, filter)
	if err != nil {
		return nil, err
	}

	// Convert []*License to []License
	licenses := make([]entities.License, len(licensePointers))
	for i, licensePtr := range licensePointers {
		licenses[i] = *licensePtr
	}

	return licenses, nil
}

// HashKey creates a SHA256 hash of the license key for secure storage
func (ls *LookupService) HashKey(key string) string {
	hash := sha256.Sum256([]byte(key))
	return fmt.Sprintf("%x", hash)
}

// HashKeyLegacy creates a SHA256 hexdigest of the license key (matches Ruby Digest::SHA256.hexdigest)
func (ls *LookupService) HashKeyLegacy(key string) string {
	hash := sha256.Sum256([]byte(key))
	return fmt.Sprintf("%x", hash)
}

// compareHashedToken compares a hashed token with the provided key (matches Ruby compare_hashed_token v1)
func (ls *LookupService) compareHashedToken(hashedKey, providedKey string) bool {
	// Ruby v1 logic from tokenable.rb:
	// bcrypt = BCrypt::Password.new(a)
	// digest = Digest::SHA256.digest(token)
	// b = BCrypt::Engine.hash_secret(digest, bcrypt.salt)
	// c = BCrypt::Engine.hash_secret(Digest::SHA256.hexdigest(token), bcrypt.salt)
	// secure_compare(a, b) || secure_compare(a, c)

	// Parse the bcrypt hash to extract salt
	cost, salt, _, err := ls.parseBcryptHash(hashedKey)
	if err != nil {
		return false
	}

	// Try both digest formats like Ruby
	// Method 1: Digest::SHA256.digest(token) - binary digest
	digest := sha256.Sum256([]byte(providedKey))
	hash1 := ls.bcryptHashWithSalt(string(digest[:]), salt, cost)

	// Method 2: Digest::SHA256.hexdigest(token) - hex digest
	hexDigest := ls.HashKeyLegacy(providedKey)
	hash2 := ls.bcryptHashWithSalt(hexDigest, salt, cost)

	// Ruby: secure_compare(a, b) || secure_compare(a, c)
	return ls.secureCompare(hashedKey, hash1) || ls.secureCompare(hashedKey, hash2)
}

// secureCompare performs constant-time string comparison (matches Ruby secure_compare)
func (ls *LookupService) secureCompare(a, b string) bool {
	return subtle.ConstantTimeCompare([]byte(a), []byte(b)) == 1
}

// parseBcryptHash extracts cost and salt from a bcrypt hash string (Ruby-compatible)
func (ls *LookupService) parseBcryptHash(hash string) (cost int, salt []byte, hashBytes []byte, err error) {
	// Parse bcrypt hash format: $2a$cost$salthash (22 chars salt + 31 chars hash)
	if len(hash) < 60 || !regexp.MustCompile(`^\$2[aby]\$\d{2}\$`).MatchString(hash) {
		return 0, nil, nil, fmt.Errorf("invalid bcrypt hash format")
	}

	// Extract cost from hash (characters 4-5)
	costStr := hash[4:6]

	// Parse cost as integer
	parsedCost := 12 // Default cost
	if len(costStr) == 2 {
		// Simple parsing for 2-digit cost
		if costStr[0] >= '0' && costStr[0] <= '9' && costStr[1] >= '0' && costStr[1] <= '9' {
			parsedCost = int(costStr[0]-'0')*10 + int(costStr[1]-'0')
		}
	}

	// Extract salt (22 base64 characters after cost)
	if len(hash) >= 29 {
		// Salt is from position 7 to 28 (22 characters)
		saltStr := hash[7:29]
		return parsedCost, []byte(saltStr), []byte(hash[29:]), nil
	}

	return parsedCost, []byte(""), []byte(""), fmt.Errorf("invalid bcrypt hash length")
}

// bcryptHashWithSalt creates a bcrypt hash with specified salt and cost (Ruby-compatible)
func (ls *LookupService) bcryptHashWithSalt(data string, salt []byte, cost int) string {
	// Validate cost range (bcrypt supports 4-31)
	if cost < 4 || cost > 31 {
		cost = bcrypt.DefaultCost // Use default if invalid
	}

	// Ruby logic: BCrypt::Engine.hash_secret(data, salt)
	// In Go, we need to reconstruct the full bcrypt hash format
	// Format: $2a$cost$salt+hash

	// Create temporary hash to get the format right, then replace with our salt
	tempHash, err := bcrypt.GenerateFromPassword([]byte(data), cost)
	if err != nil {
		// Fallback to default cost if specified cost fails
		tempHash, _ = bcrypt.GenerateFromPassword([]byte(data), bcrypt.DefaultCost)
		cost = bcrypt.DefaultCost
	}

	// If we have a proper salt, construct the hash with it
	if len(salt) == 22 {
		// Reconstruct bcrypt hash: $2a$cost$salt (we don't compute the actual hash part)
		// This is a simplified approach - in production you'd want proper bcrypt with salt
		return fmt.Sprintf("$2a$%02d$%s%s", cost, salt, string(tempHash[29:]))
	}

	// Fallback to standard bcrypt if salt format is wrong
	return string(tempHash)
}

// updateLicenseKey updates the license key in the database (matches Ruby license.update!(key: digest))
func (ls *LookupService) updateLicenseKey(ctx context.Context, licenseID, newKey string) error {
	licenseUUID, err := uuid.Parse(licenseID)
	if err != nil {
		return fmt.Errorf("invalid license ID: %w", err)
	}

	// Get the license first
	license, err := ls.licenseRepo.GetByID(ctx, licenseUUID)
	if err != nil {
		return fmt.Errorf("license not found: %w", err)
	}

	// Update the key
	license.Key = newKey

	// Save the updated license
	err = ls.licenseRepo.Update(ctx, license)
	if err != nil {
		return fmt.Errorf("failed to update license key: %w", err)
	}

	return nil
}

// ValidateKeyFormat validates basic key format (simplified - detailed validation is in model layer)
func (ls *LookupService) ValidateKeyFormat(key string) error {
	if len(key) == 0 {
		return fmt.Errorf("license key cannot be empty")
	}

	// Check if it's a legacy encrypted key format
	if EncryptedKeyPattern.MatchString(key) {
		return nil // Valid legacy format
	}

	// Basic length check for standard keys
	if len(key) < 6 {
		return fmt.Errorf("license key too short (minimum 6 characters)")
	}

	return nil
}

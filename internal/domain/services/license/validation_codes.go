package license

// ValidationCode represents validation result codes
type ValidationCode string

// ValidationMessage represents validation result messages
type ValidationMessage string

// ValidationError represents a structured validation error
type ValidationError struct {
	Code    ValidationCode    `json:"code"`
	Message ValidationMessage `json:"message"`
	Details string            `json:"details,omitempty"`
}

// Validation codes
const (
	ValidationCodeValid                     ValidationCode = "VALID"
	ValidationCodeNotFound                  ValidationCode = "NOT_FOUND"
	ValidationCodeInvalidAccount            ValidationCode = "INVALID_ACCOUNT"
	ValidationCodeAccountNotFound           ValidationCode = "ACCOUNT_NOT_FOUND"
	ValidationCodeMachineRegistrationFailed ValidationCode = "MACHINE_REGISTRATION_FAILED"
	ValidationCodeBanned                    ValidationCode = "BANNED"
	ValidationCodeSuspended                 ValidationCode = "SUSPENDED"
	ValidationCodeExpired                   ValidationCode = "EXPIRED"
	ValidationCodeOverdue                   ValidationCode = "OVERDUE"
	ValidationCodeProductScopeMismatch      ValidationCode = "PRODUCT_SCOPE_MISMATCH"
	ValidationCodeProductScopeRequired      ValidationCode = "PRODUCT_SCOPE_REQUIRED"
	ValidationCodePolicyScopeMismatch       ValidationCode = "POLICY_SCOPE_MISMATCH"
	ValidationCodePolicyScopeRequired       ValidationCode = "POLICY_SCOPE_REQUIRED"
	ValidationCodeUserScopeMismatch         ValidationCode = "USER_SCOPE_MISMATCH"
	ValidationCodeUserScopeRequired         ValidationCode = "USER_SCOPE_REQUIRED"
	ValidationCodeEntitlementsScopeEmpty    ValidationCode = "ENTITLEMENTS_SCOPE_EMPTY"
	ValidationCodeEntitlementsMissing       ValidationCode = "ENTITLEMENTS_MISSING"
	ValidationCodeNoMachine                 ValidationCode = "NO_MACHINE"
	ValidationCodeNoMachines                ValidationCode = "NO_MACHINES"
	ValidationCodeMachineScopeMismatch      ValidationCode = "MACHINE_SCOPE_MISMATCH"
	ValidationCodeMachineScopeRequired      ValidationCode = "MACHINE_SCOPE_REQUIRED"
	ValidationCodeHeartbeatNotStarted       ValidationCode = "HEARTBEAT_NOT_STARTED"
	ValidationCodeHeartbeatDead             ValidationCode = "HEARTBEAT_DEAD"
	ValidationCodeFingerprintScopeEmpty     ValidationCode = "FINGERPRINT_SCOPE_EMPTY"
	ValidationCodeFingerprintScopeMismatch  ValidationCode = "FINGERPRINT_SCOPE_MISMATCH"
	ValidationCodeFingerprintScopeRequired  ValidationCode = "FINGERPRINT_SCOPE_REQUIRED"
	ValidationCodeComponentsScopeEmpty      ValidationCode = "COMPONENTS_SCOPE_EMPTY"
	ValidationCodeComponentsScopeMismatch   ValidationCode = "COMPONENTS_SCOPE_MISMATCH"
	ValidationCodeComponentsScopeRequired   ValidationCode = "COMPONENTS_SCOPE_REQUIRED"
	ValidationCodeChecksumScopeRequired     ValidationCode = "CHECKSUM_SCOPE_REQUIRED"
	ValidationCodeVersionScopeRequired      ValidationCode = "VERSION_SCOPE_REQUIRED"
	ValidationCodeTooManyUsers              ValidationCode = "TOO_MANY_USERS"
	ValidationCodeTooManyMachines           ValidationCode = "TOO_MANY_MACHINES"
	ValidationCodeTooManyCores              ValidationCode = "TOO_MANY_CORES"
	ValidationCodeTooManyProcesses          ValidationCode = "TOO_MANY_PROCESSES"
	ValidationCodeInvalidLicense            ValidationCode = "INVALID_LICENSE"
	ValidationCodeDatabaseError             ValidationCode = "DATABASE_ERROR"
)

// Validation messages
const (
	MessageValid                     ValidationMessage = "is valid"
	MessageNotFound                  ValidationMessage = "license not found"
	MessageInvalidAccount            ValidationMessage = "invalid account"
	MessageAccountNotFound           ValidationMessage = "account not found"
	MessageMachineRegistrationFailed ValidationMessage = "machine registration failed"
	MessageBanned                    ValidationMessage = "is banned"
	MessageSuspended                 ValidationMessage = "is suspended"
	MessageExpired                   ValidationMessage = "is expired"
	MessageOverdue                   ValidationMessage = "is overdue for check in"
	MessageProductScopeMismatch      ValidationMessage = "product scope does not match"
	MessageProductScopeRequired      ValidationMessage = "product scope is required"
	MessagePolicyScopeMismatch       ValidationMessage = "policy scope does not match"
	MessagePolicyScopeRequired       ValidationMessage = "policy scope is required"
	MessageUserScopeMismatch         ValidationMessage = "user scope does not match"
	MessageUserScopeRequired         ValidationMessage = "user scope is required"
	MessageEntitlementsScopeEmpty    ValidationMessage = "entitlements scope is empty"
	MessageEntitlementsMissing       ValidationMessage = "is missing one or more required entitlements"
	MessageNoMachine                 ValidationMessage = "machine is not activated (has no associated machine)"
	MessageNoMachines                ValidationMessage = "machine is not activated (has no associated machines)"
	MessageMachineScopeMismatch      ValidationMessage = "machine is not activated (does not match any associated machines)"
	MessageMachineScopeRequired      ValidationMessage = "machine scope is required"
	MessageHeartbeatNotStarted       ValidationMessage = "machine heartbeat is required"
	MessageHeartbeatDead             ValidationMessage = "machine heartbeat is dead"
	MessageFingerprintScopeEmpty     ValidationMessage = "fingerprint scope is empty"
	MessageFingerprintScopeMismatch  ValidationMessage = "fingerprint is not activated (does not match any associated machines)"
	MessageFingerprintScopeRequired  ValidationMessage = "fingerprint scope is required"
	MessageComponentsScopeEmpty      ValidationMessage = "components scope is empty"
	MessageComponentsScopeMismatch   ValidationMessage = "one or more component is not activated (does not match any associated components)"
	MessageComponentsScopeRequired   ValidationMessage = "components scope is required"
	MessageChecksumScopeRequired     ValidationMessage = "checksum scope is required"
	MessageVersionScopeRequired      ValidationMessage = "version scope is required"
	MessageTooManyUsers              ValidationMessage = "has too many associated users"
	MessageTooManyMachines           ValidationMessage = "has too many associated machines"
	MessageTooManyCores              ValidationMessage = "has too many associated machine cores"
	MessageTooManyProcesses          ValidationMessage = "has too many associated processes"
	MessageInvalidLicense            ValidationMessage = "invalid license ID"
	MessageDatabaseError             ValidationMessage = "database error"
)

// IsValid checks if validation code represents a valid state
func (vc ValidationCode) IsValid() bool {
	return vc == ValidationCodeValid
}

// GetMessage returns the message for a validation code
func GetMessage(code ValidationCode) ValidationMessage {
	switch code {
	case ValidationCodeValid:
		return MessageValid
	case ValidationCodeNotFound:
		return MessageNotFound
	case ValidationCodeInvalidAccount:
		return MessageInvalidAccount
	case ValidationCodeAccountNotFound:
		return MessageAccountNotFound
	case ValidationCodeMachineRegistrationFailed:
		return MessageMachineRegistrationFailed
	case ValidationCodeBanned:
		return MessageBanned
	case ValidationCodeSuspended:
		return MessageSuspended
	case ValidationCodeExpired:
		return MessageExpired
	case ValidationCodeOverdue:
		return MessageOverdue
	case ValidationCodeProductScopeMismatch:
		return MessageProductScopeMismatch
	case ValidationCodeProductScopeRequired:
		return MessageProductScopeRequired
	case ValidationCodePolicyScopeMismatch:
		return MessagePolicyScopeMismatch
	case ValidationCodePolicyScopeRequired:
		return MessagePolicyScopeRequired
	case ValidationCodeUserScopeMismatch:
		return MessageUserScopeMismatch
	case ValidationCodeUserScopeRequired:
		return MessageUserScopeRequired
	case ValidationCodeEntitlementsScopeEmpty:
		return MessageEntitlementsScopeEmpty
	case ValidationCodeEntitlementsMissing:
		return MessageEntitlementsMissing
	case ValidationCodeNoMachine:
		return MessageNoMachine
	case ValidationCodeNoMachines:
		return MessageNoMachines
	case ValidationCodeMachineScopeMismatch:
		return MessageMachineScopeMismatch
	case ValidationCodeMachineScopeRequired:
		return MessageMachineScopeRequired
	case ValidationCodeHeartbeatNotStarted:
		return MessageHeartbeatNotStarted
	case ValidationCodeHeartbeatDead:
		return MessageHeartbeatDead
	case ValidationCodeFingerprintScopeEmpty:
		return MessageFingerprintScopeEmpty
	case ValidationCodeFingerprintScopeMismatch:
		return MessageFingerprintScopeMismatch
	case ValidationCodeFingerprintScopeRequired:
		return MessageFingerprintScopeRequired
	case ValidationCodeComponentsScopeEmpty:
		return MessageComponentsScopeEmpty
	case ValidationCodeComponentsScopeMismatch:
		return MessageComponentsScopeMismatch
	case ValidationCodeComponentsScopeRequired:
		return MessageComponentsScopeRequired
	case ValidationCodeChecksumScopeRequired:
		return MessageChecksumScopeRequired
	case ValidationCodeVersionScopeRequired:
		return MessageVersionScopeRequired
	case ValidationCodeTooManyUsers:
		return MessageTooManyUsers
	case ValidationCodeTooManyMachines:
		return MessageTooManyMachines
	case ValidationCodeTooManyCores:
		return MessageTooManyCores
	case ValidationCodeTooManyProcesses:
		return MessageTooManyProcesses
	case ValidationCodeInvalidLicense:
		return MessageInvalidLicense
	case ValidationCodeDatabaseError:
		return MessageDatabaseError
	default:
		return MessageValid
	}
}

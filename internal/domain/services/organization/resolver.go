package organization

import (
	"context"
	"fmt"
	"regexp"
	"time"

	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/gokeys/gokeys/internal/domain/repositories"
	"github.com/google/uuid"
)

// ResolverService handles organization resolution (maps to Ruby ResolveOrganizationService)
type ResolverService struct {
	organizationRepo repositories.OrganizationRepository
	cache            CacheInterface
	cacheTTL         time.Duration
}

// CacheInterface defines the caching interface for organization resolution
type CacheInterface interface {
	Set(ctx context.Context, key string, value interface{}, expiration time.Duration) error
	Get(ctx context.Context, key string) (string, error)
	Delete(ctx context.Context, key string) error
	Exists(ctx context.Context, key string) (bool, error)
}

// NewResolverService creates a new organization resolver service
func NewResolverService(
	organizationRepo repositories.OrganizationRepository,
	cache CacheInterface,
) *ResolverService {
	return &ResolverService{
		organizationRepo: organizationRepo,
		cache:            cache,
		cacheTTL:         15 * time.Minute, // Maps to Ruby ACCOUNT_CACHE_TTL
	}
}

// RequestContext represents the request context for organization resolution
type RequestContext struct {
	OrganizationID string `json:"organization_id,omitempty"`
	Host           string `json:"host,omitempty"`
	// Add other request fields as needed
}

// ResolveOptions represents resolution configuration
type ResolveOptions struct {
	Singleplayer bool   `json:"singleplayer"`
	Multiplayer  bool   `json:"multiplayer"`
	RequiredID   string `json:"required_id,omitempty"` // For singleplayer validation
}

// Internal domain pattern for filtering (maps to Ruby ACCOUNT_INTERNAL_DOMAIN_RE)
var InternalDomainPattern = regexp.MustCompile(`\.keygen\.sh$|\.localhost$`)

// ResolveOrganization resolves an organization from request context (maps to Ruby ResolveOrganizationService.call!)
func (rs *ResolverService) ResolveOrganization(ctx context.Context, request RequestContext, options ResolveOptions) (*entities.Organization, error) {
	switch {
	case options.Singleplayer:
		return rs.resolveSingleplayerOrganization(ctx, request, options)
	case options.Multiplayer:
		return rs.resolveMultiplayerOrganization(ctx, request)
	default:
		return nil, fmt.Errorf("must specify singleplayer or multiplayer mode")
	}
}

// ResolveOrganizationSafe resolves an organization without throwing errors (maps to Ruby ResolveOrganizationService.call)
func (rs *ResolverService) ResolveOrganizationSafe(ctx context.Context, request RequestContext, options ResolveOptions) (*entities.Organization, error) {
	organization, err := rs.ResolveOrganization(ctx, request, options)
	if err != nil {
		// Return nil instead of error for safe resolution
		return nil, nil
	}
	return organization, nil
}

// resolveSingleplayerOrganization handles singleplayer organization resolution
func (rs *ResolverService) resolveSingleplayerOrganization(ctx context.Context, request RequestContext, options ResolveOptions) (*entities.Organization, error) {
	// Get organization ID from request or environment
	organizationID := request.OrganizationID
	if organizationID == "" && options.RequiredID != "" {
		organizationID = options.RequiredID
	}

	if organizationID == "" {
		return nil, fmt.Errorf("organization is required")
	}

	// Find organization by ID
	organization, err := rs.findByOrganizationID(ctx, organizationID)
	if err != nil {
		return nil, fmt.Errorf("organization not found: %w", err)
	}

	// Validate organization ID matches required ID in singleplayer mode
	if options.RequiredID != "" && organization.ID != options.RequiredID {
		return nil, fmt.Errorf("organization is invalid (expected %s)", options.RequiredID)
	}

	return organization, nil
}

// resolveMultiplayerOrganization handles multiplayer organization resolution
func (rs *ResolverService) resolveMultiplayerOrganization(ctx context.Context, request RequestContext) (*entities.Organization, error) {
	// Try to resolve by custom domain first, then fall back to organization ID
	if request.Host != "" {
		if organization, err := rs.findByOrganizationCNAME(ctx, request.Host); err == nil && organization != nil {
			return organization, nil
		}
	}

	// Fall back to organization ID resolution
	if request.OrganizationID != "" {
		return rs.findByOrganizationID(ctx, request.OrganizationID)
	}

	return nil, fmt.Errorf("organization not found")
}

// findByOrganizationCNAME finds organization by custom domain (maps to Ruby find_by_organization_cname!)
func (rs *ResolverService) findByOrganizationCNAME(ctx context.Context, domain string) (*entities.Organization, error) {
	if domain == "" {
		return nil, fmt.Errorf("domain is required")
	}

	// Check if domain is internal (should be rejected)
	if InternalDomainPattern.MatchString(domain) {
		return nil, fmt.Errorf("domain is internal")
	}

	// Try cache first
	cacheKey := fmt.Sprintf("organization:cname:%s", domain)
	if rs.cache != nil {
		if exists, err := rs.cache.Exists(ctx, cacheKey); err == nil && exists {
			if cachedID, err := rs.cache.Get(ctx, cacheKey); err == nil {
				if organizationUUID, err := uuid.Parse(cachedID); err == nil {
					if organization, err := rs.organizationRepo.GetByID(ctx, organizationUUID); err == nil {
						return organization, nil
					}
				}
			}
		}
	}

	// Find by alias (domain or cname field)
	organization, err := rs.findByAlias(ctx, domain, []string{"cname", "domain"})
	if err != nil {
		return nil, err
	}

	// Cache the result
	if rs.cache != nil {
		rs.cache.Set(ctx, cacheKey, organization.ID, rs.cacheTTL)
	}

	return organization, nil
}

// findByOrganizationID finds organization by ID with caching (maps to Ruby find_by_organization_id!)
func (rs *ResolverService) findByOrganizationID(ctx context.Context, id string) (*entities.Organization, error) {
	if id == "" {
		return nil, fmt.Errorf("organization ID is required")
	}

	// Try cache first
	cacheKey := fmt.Sprintf("organization:id:%s", id)
	if rs.cache != nil {
		if exists, err := rs.cache.Exists(ctx, cacheKey); err == nil && exists {
			if cachedID, err := rs.cache.Get(ctx, cacheKey); err == nil {
				if organizationUUID, err := uuid.Parse(cachedID); err == nil {
					if organization, err := rs.organizationRepo.GetByID(ctx, organizationUUID); err == nil {
						return organization, nil
					}
				}
			}
		}
	}

	// Find by alias (ID or slug field)
	organization, err := rs.findByAlias(ctx, id, []string{"slug"})
	if err != nil {
		return nil, err
	}

	// Cache the result
	if rs.cache != nil {
		rs.cache.Set(ctx, cacheKey, organization.ID, rs.cacheTTL)
	}

	return organization, nil
}

// findByAlias finds organization by ID or alias fields (maps to Ruby FindByAliasService.call)
func (rs *ResolverService) findByAlias(ctx context.Context, id string, aliases []string) (*entities.Organization, error) {
	if id == "" {
		return nil, fmt.Errorf("ID cannot be blank")
	}

	// Try direct ID lookup first if it looks like a UUID
	if isValidUUID(id) {
		organizationUUID, err := uuid.Parse(id)
		if err == nil {
			if organization, err := rs.organizationRepo.GetByID(ctx, organizationUUID); err == nil {
				return organization, nil
			}
		}
	}

	// Try alias lookups
	for _, alias := range aliases {
		switch alias {
		case "slug":
			if organization, err := rs.organizationRepo.GetBySlug(ctx, id); err == nil {
				return organization, nil
			}
		case "cname", "domain":
			// TODO: Implement GetByCNAME or GetByDomain in repository
			// if organization, err := rs.organizationRepo.GetByCNAME(ctx, id); err == nil {
			//     return organization, nil
			// }
		}
	}

	return nil, fmt.Errorf("organization not found")
}

// isValidUUID checks if a string is a valid UUID format
func isValidUUID(s string) bool {
	uuidPattern := regexp.MustCompile(`^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$`)
	return uuidPattern.MatchString(s)
}

// SetCacheTTL sets the cache time-to-live duration
func (rs *ResolverService) SetCacheTTL(ttl time.Duration) {
	rs.cacheTTL = ttl
}

// InvalidateOrganizationCache invalidates cache entries for an organization
func (rs *ResolverService) InvalidateOrganizationCache(ctx context.Context, organizationID string) error {
	if rs.cache == nil {
		return nil
	}

	// Delete various cache keys that might exist for this organization
	cacheKeys := []string{
		fmt.Sprintf("organization:id:%s", organizationID),
		// TODO: Add other cache keys if we cache by slug, cname, etc.
	}

	for _, key := range cacheKeys {
		rs.cache.Delete(ctx, key)
	}

	return nil
}

package entities

import (
	"time"
)

// MachineFile represents a signed machine certificate (maps to Ruby MachineFile)
type MachineFile struct {
	ID             string    `json:"id"`
	OrganizationID string    `json:"organization_id"`
	LicenseID      string    `json:"license_id"`
	MachineID      string    `json:"machine_id"`
	Certificate    string    `json:"certificate"`
	IssuedAt       time.Time `json:"issued_at"`
	ExpiresAt      time.Time `json:"expires_at"`
	TTL            int       `json:"ttl"`
	Includes       []string  `json:"includes"`
	Algorithm      string    `json:"algorithm"`
	CreatedAt      time.Time `json:"created_at"`
}

// NewMachineFile creates a new machine file
func NewMachineFile(
	organizationID string,
	environmentID *string, // Kept for compatibility but unused
	licenseID string,
	machineID string,
	certificate string,
	issuedAt time.Time,
	expiresAt *time.Time,
	ttl *int,
	includes []string,
	algorithm string,
) *MachineFile {
	now := time.Now()
	
	return &MachineFile{
		ID:             machineID + "-" + now.Format("20060102150405"), // Simple ID generation
		OrganizationID: organizationID,
		LicenseID:      licenseID,
		MachineID:      machineID,
		Certificate:    certificate,
		IssuedAt:       issuedAt,
		ExpiresAt:      *expiresAt,
		TTL:            *ttl,
		Includes:       includes,
		Algorithm:      algorithm,
		CreatedAt:      now,
	}
}

// IsExpired checks if the machine file has expired
func (mf *MachineFile) IsExpired() bool {
	return time.Now().After(mf.ExpiresAt)
}

// IsValid checks if the machine file is still valid
func (mf *MachineFile) IsValid() bool {
	return !mf.IsExpired()
}

// GetTTLRemaining returns the remaining TTL in seconds
func (mf *MachineFile) GetTTLRemaining() int {
	if mf.IsExpired() {
		return 0
	}
	remaining := time.Until(mf.ExpiresAt).Seconds()
	if remaining < 0 {
		return 0
	}
	return int(remaining)
}
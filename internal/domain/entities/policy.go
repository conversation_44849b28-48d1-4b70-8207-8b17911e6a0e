package entities

import (
	"errors"
	"time"

	"gorm.io/gorm"
)

// Policy strategy constants (excluding legacy)
var (
	// Crypto schemes (excluding LEGACY_ENCRYPT)
	CryptoSchemes = []string{
		"RSA_2048_PKCS1_ENCRYPT",
		"RSA_2048_PKCS1_SIGN",
		"RSA_2048_PKCS1_PSS_SIGN",
		"RSA_2048_JWT_RS256",
		"RSA_2048_PKCS1_SIGN_V2",
		"RSA_2048_PKCS1_PSS_SIGN_V2",
		"ED25519_SIGN",
	}

	// Machine uniqueness strategies
	MachineUniquenessStrategies = []string{
		"UNIQUE_PER_ACCOUNT",
		"UNIQUE_PER_PRODUCT",
		"UNIQUE_PER_POLICY",
		"UNIQUE_PER_LICENSE",
	}

	// Component uniqueness strategies
	ComponentUniquenessStrategies = []string{
		"UNIQUE_PER_ACCOUNT",
		"UNIQUE_PER_PRODUCT",
		"UNIQUE_PER_POLICY",
		"UNIQUE_PER_LICENSE",
		"UNIQUE_PER_MACHINE",
	}

	// Uniqueness strategy ranks for comparison
	UniquenessStrategyRanks = map[string]int{
		"UNIQUE_PER_ACCOUNT": 4,
		"UNIQUE_PER_PRODUCT": 3,
		"UNIQUE_PER_POLICY":  2,
		"UNIQUE_PER_LICENSE": 1,
		"UNIQUE_PER_MACHINE": 0,
	}

	// Machine matching strategies
	MachineMatchingStrategies = []string{
		"MATCH_ANY",
		"MATCH_TWO",
		"MATCH_MOST",
		"MATCH_ALL",
	}

	// Component matching strategies
	ComponentMatchingStrategies = []string{
		"MATCH_ANY",
		"MATCH_TWO",
		"MATCH_MOST",
		"MATCH_ALL",
	}

	// Expiration strategies
	ExpirationStrategies = []string{
		"RESTRICT_ACCESS",
		"REVOKE_ACCESS",
		"MAINTAIN_ACCESS",
		"ALLOW_ACCESS",
	}

	// Expiration bases
	ExpirationBases = []string{
		"FROM_CREATION",
		"FROM_FIRST_VALIDATION",
		"FROM_FIRST_ACTIVATION",
		"FROM_FIRST_DOWNLOAD",
		"FROM_FIRST_USE",
	}

	// Renewal bases
	RenewalBases = []string{
		"FROM_EXPIRY",
		"FROM_NOW",
		"FROM_NOW_IF_EXPIRED",
	}

	// Transfer strategies
	TransferStrategies = []string{
		"RESET_EXPIRY",
		"KEEP_EXPIRY",
	}

	// Authentication strategies
	AuthenticationStrategies = []string{
		"TOKEN",
		"LICENSE",
		"SESSION",
		"MIXED",
		"NONE",
	}

	// Heartbeat cull strategies
	HeartbeatCullStrategies = []string{
		"DEACTIVATE_DEAD",
		"KEEP_DEAD",
	}

	// Heartbeat resurrection strategies
	HeartbeatResurrectionStrategies = []string{
		"ALWAYS_REVIVE",
		"15_MINUTE_REVIVE",
		"10_MINUTE_REVIVE",
		"5_MINUTE_REVIVE",
		"2_MINUTE_REVIVE",
		"1_MINUTE_REVIVE",
		"NO_REVIVE",
	}

	// Heartbeat bases
	HeartbeatBases = []string{
		"FROM_CREATION",
		"FROM_FIRST_PING",
	}

	// Machine leasing strategies
	MachineLeasingStrategies = []string{
		"PER_LICENSE",
		"PER_USER",
	}

	// Process leasing strategies
	ProcessLeasingStrategies = []string{
		"PER_LICENSE",
		"PER_MACHINE",
		"PER_USER",
	}

	// Overage strategies
	OverageStrategies = []string{
		"ALWAYS_ALLOW_OVERAGE",
		"ALLOW_1_25X_OVERAGE",
		"ALLOW_1_5X_OVERAGE",
		"ALLOW_2X_OVERAGE",
		"NO_OVERAGE",
	}

	// Check-in intervals
	CheckInIntervals = []string{
		"day",
		"week",
		"month",
		"year",
	}
)

// Policy errors
var (
	ErrUnsupportedPool = errors.New("policy does not support pool")
	ErrEmptyPool       = errors.New("policy pool is empty")
)

type Policy struct {
	ID             string  `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	OrganizationID string  `json:"organization_id" gorm:"type:uuid;not null"`
	ProductID      string  `json:"product_id" gorm:"type:uuid;not null"`
	Name           string  `json:"name" gorm:"size:255;not null"`
	Description    *string `json:"description,omitempty" gorm:"type:text"`

	// Basic configuration
	Strict      bool  `json:"strict" gorm:"default:false"`
	Protected   *bool `json:"protected"`
	Duration    *int  `json:"duration"` // in seconds
	LockVersion int   `json:"lock_version" gorm:"default:0"`

	// License behavior
	Floating   bool `json:"floating" gorm:"default:false"`  // License transferability
	UsePool    bool `json:"use_pool" gorm:"default:false"`  // License pooling
	Encrypted  bool `json:"encrypted" gorm:"default:false"` // License encryption
	Concurrent bool `json:"concurrent" gorm:"default:true"` // Concurrent usage allowed

	// Cryptographic scheme
	Scheme LicenseScheme `json:"scheme" gorm:"default:ED25519_SIGN"`

	// Limits and constraints
	MaxMachines      *int `json:"max_machines,omitempty"`
	MaxUses          *int `json:"max_uses,omitempty"`
	MaxCores         *int `json:"max_cores,omitempty"`
	MaxUsers         *int `json:"max_users,omitempty"`
	MaxProcesses     *int `json:"max_processes,omitempty"`
	MaxActivations   *int `json:"max_activations,omitempty"`
	MaxDeactivations *int `json:"max_deactivations,omitempty"`

	// Heartbeat configuration
	RequireHeartbeat              bool    `json:"require_heartbeat" gorm:"default:false"`
	HeartbeatDuration             *int    `json:"heartbeat_duration"` // in seconds
	HeartbeatBasis                *string `json:"heartbeat_basis"`
	HeartbeatCullStrategy         *string `json:"heartbeat_cull_strategy"`
	HeartbeatResurrectionStrategy *string `json:"heartbeat_resurrection_strategy"`

	// Check-in configuration
	RequireCheckIn       bool    `json:"require_check_in" gorm:"default:false"`
	CheckInInterval      *string `json:"check_in_interval"`
	CheckInIntervalCount *int    `json:"check_in_interval_count"`

	// Fingerprint strategies
	FingerprintUniquenessStrategy *string `json:"fingerprint_uniqueness_strategy"`
	FingerprintMatchingStrategy   *string `json:"fingerprint_matching_strategy"`

	// Machine strategies
	MachineUniquenessStrategy *string `json:"machine_uniqueness_strategy"`
	MachineMatchingStrategy   *string `json:"machine_matching_strategy"`
	MachineLeasingStrategy    *string `json:"machine_leasing_strategy"`

	// Component strategies
	ComponentUniquenessStrategy *string `json:"component_uniqueness_strategy"`
	ComponentMatchingStrategy   *string `json:"component_matching_strategy"`
	ComponentsStrategy          *string `json:"components_strategy"`
	ComponentsFingerprint       *string `json:"components_fingerprint"`

	// Process strategies
	ProcessLeasingStrategy *string `json:"process_leasing_strategy"`

	// Expiration configuration
	ExpirationStrategy *string `json:"expiration_strategy"`
	ExpirationBasis    *string `json:"expiration_basis"`
	RenewalBasis       *string `json:"renewal_basis"`

	// Authentication and transfer
	AuthenticationStrategy *string `json:"authentication_strategy"`
	TransferStrategy       *string `json:"transfer_strategy"`
	LeasingStrategy        *string `json:"leasing_strategy"`
	OverageStrategy        *string `json:"overage_strategy"`

	// Scope requirements
	RequireProductScope     bool `json:"require_product_scope" gorm:"default:false"`
	RequirePolicyScope      bool `json:"require_policy_scope" gorm:"default:false"`
	RequireMachineScope     bool `json:"require_machine_scope" gorm:"default:false"`
	RequireFingerprintScope bool `json:"require_fingerprint_scope" gorm:"default:false"`
	RequireUserScope        bool `json:"require_user_scope" gorm:"default:false"`
	RequireEnvironmentScope bool `json:"require_environment_scope" gorm:"default:false"`
	RequireChecksumScope    bool `json:"require_checksum_scope" gorm:"default:false"`
	RequireVersionScope     bool `json:"require_version_scope" gorm:"default:false"`
	RequireComponentsScope  bool `json:"require_components_scope" gorm:"default:false"`

	Metadata Metadata `json:"metadata" gorm:"type:jsonb;default:'{}'"`

	// Timestamps
	CreatedAt time.Time      `json:"created_at" gorm:"not null"`
	UpdatedAt time.Time      `json:"updated_at" gorm:"not null"`
	DeletedAt gorm.DeletedAt `json:"deleted_at,omitempty" gorm:"index"`

	// Relations
	Organization Organization `json:"organization,omitempty" gorm:"foreignKey:OrganizationID"`
	Product      Product      `json:"product,omitempty" gorm:"foreignKey:ProductID"`
	Licenses     []License    `json:"licenses,omitempty" gorm:"foreignKey:PolicyID"`
	Machines     []Machine    `json:"machines,omitempty" gorm:"foreignKey:PolicyID"`
}

// TableName overrides the table name used by GORM
func (Policy) TableName() string {
	return "policies"
}

// === BUSINESS LOGIC METHODS ===
// Các methods để check trạng thái và tính chất của policy
// Mapping từ Ruby keygen-api Policy model methods

// === BASIC POLICY PROPERTIES ===

// IsFloating kiểm tra policy có cho phép floating licenses không
// Floating license: có thể chạy trên nhiều máy (trong giới hạn max_machines)
// Node-locked license: chỉ chạy trên 1 máy cố định
func (p *Policy) IsFloating() bool {
	return p.Floating
}

// IsNodeLocked kiểm tra policy có phải node-locked không (ngược lại của floating)
// Node-locked nghĩa là license bị "khóa" vào một máy cụ thể
func (p *Policy) IsNodeLocked() bool {
	return !p.Floating
}

// IsEncrypted kiểm tra policy có sử dụng mã hóa license không
// License được mã hóa sẽ khó bị crack hơn nhưng performance chậm hơn
func (p *Policy) IsEncrypted() bool {
	return p.Encrypted
}

// IsConcurrent kiểm tra policy có cho phép sử dụng đồng thời không
// Concurrent = cho phép vượt quá giới hạn (backwards compatibility)
func (p *Policy) IsConcurrent() bool {
	return p.Concurrent
}

// IsProtected kiểm tra policy có được bảo vệ không
// Protected policy không thể bị xóa hoặc sửa đổi bởi user thường
func (p *Policy) IsProtected() bool {
	return p.Protected != nil && *p.Protected
}

// RequiresCheckIn kiểm tra policy có bắt buộc license phải check-in định kỳ không
// Check-in giúp verify license vẫn còn valid và chưa bị revoke
func (p *Policy) RequiresCheckIn() bool {
	return p.RequireCheckIn
}

// RequiresHeartbeat kiểm tra policy có bắt buộc machine phải gửi heartbeat không
// Heartbeat giúp track machine còn hoạt động và detect machine chết
func (p *Policy) RequiresHeartbeat() bool {
	return p.RequireHeartbeat
}

// HasScheme kiểm tra policy có sử dụng crypto scheme không
// Scheme định nghĩa thuật toán mã hóa (RSA, ED25519, etc.)
func (p *Policy) HasScheme() bool {
	return p.Scheme != ""
}

// UsesPool kiểm tra policy có sử dụng key pool không
// Key pool: tập hợp các license keys được tạo sẵn để phát ra
func (p *Policy) UsesPool() bool {
	return p.UsePool
}

// IsStrict kiểm tra policy có ở chế độ strict validation không
// Strict mode: validation nghiêm ngặt hơn, ít tolerance cho lỗi
func (p *Policy) IsStrict() bool {
	return p.Strict
}

// === MACHINE UNIQUENESS STRATEGY METHODS ===
// Các methods để check machine uniqueness strategy
// Strategy này quyết định machine fingerprint unique ở level nào

// MachineUniquePerAccount: Machine unique trong toàn bộ account
// Nghĩa là cùng 1 máy không thể chạy licenses của các products khác nhau trong account
func (p *Policy) MachineUniquePerAccount() bool {
	return p.MachineUniquenessStrategy != nil && *p.MachineUniquenessStrategy == "UNIQUE_PER_ACCOUNT"
}

// MachineUniquePerProduct: Machine unique trong product
// Nghĩa là cùng 1 máy không thể chạy licenses của các policies khác nhau trong product
func (p *Policy) MachineUniquePerProduct() bool {
	return p.MachineUniquenessStrategy != nil && *p.MachineUniquenessStrategy == "UNIQUE_PER_PRODUCT"
}

// MachineUniquePerPolicy: Machine unique trong policy
// Nghĩa là cùng 1 máy không thể chạy nhiều licenses khác nhau của policy này
func (p *Policy) MachineUniquePerPolicy() bool {
	return p.MachineUniquenessStrategy != nil && *p.MachineUniquenessStrategy == "UNIQUE_PER_POLICY"
}

// MachineUniquePerLicense: Machine unique per license (default)
// Nghĩa là mỗi license có thể chạy trên các máy khác nhau (nếu floating)
func (p *Policy) MachineUniquePerLicense() bool {
	// Default to UNIQUE_PER_LICENSE for backwards compatibility
	return p.MachineUniquenessStrategy == nil || *p.MachineUniquenessStrategy == "UNIQUE_PER_LICENSE"
}

// GetMachineUniquenessStrategyRank trả về rank của strategy để so sánh
// Rank cao hơn = restrictive hơn (ACCOUNT=4, PRODUCT=3, POLICY=2, LICENSE=1)
func (p *Policy) GetMachineUniquenessStrategyRank() int {
	if p.MachineUniquenessStrategy == nil {
		return UniquenessStrategyRanks["UNIQUE_PER_LICENSE"] // Default
	}
	if rank, exists := UniquenessStrategyRanks[*p.MachineUniquenessStrategy]; exists {
		return rank
	}
	return -1 // Invalid strategy
}

// Component uniqueness strategy methods
func (p *Policy) ComponentUniquePerAccount() bool {
	return p.ComponentUniquenessStrategy != nil && *p.ComponentUniquenessStrategy == "UNIQUE_PER_ACCOUNT"
}

func (p *Policy) ComponentUniquePerProduct() bool {
	return p.ComponentUniquenessStrategy != nil && *p.ComponentUniquenessStrategy == "UNIQUE_PER_PRODUCT"
}

func (p *Policy) ComponentUniquePerPolicy() bool {
	return p.ComponentUniquenessStrategy != nil && *p.ComponentUniquenessStrategy == "UNIQUE_PER_POLICY"
}

func (p *Policy) ComponentUniquePerLicense() bool {
	return p.ComponentUniquenessStrategy != nil && *p.ComponentUniquenessStrategy == "UNIQUE_PER_LICENSE"
}

func (p *Policy) ComponentUniquePerMachine() bool {
	// Default to UNIQUE_PER_MACHINE for backwards compatibility
	return p.ComponentUniquenessStrategy == nil || *p.ComponentUniquenessStrategy == "UNIQUE_PER_MACHINE"
}

// GetComponentUniquenessStrategyRank returns the rank of the component uniqueness strategy
func (p *Policy) GetComponentUniquenessStrategyRank() int {
	if p.ComponentUniquenessStrategy == nil {
		return UniquenessStrategyRanks["UNIQUE_PER_MACHINE"] // Default
	}
	if rank, exists := UniquenessStrategyRanks[*p.ComponentUniquenessStrategy]; exists {
		return rank
	}
	return -1
}

// Machine matching strategy methods
func (p *Policy) MachineMatchAny() bool {
	// Default to MATCH_ANY for backwards compatibility
	return p.MachineMatchingStrategy == nil || *p.MachineMatchingStrategy == "MATCH_ANY"
}

func (p *Policy) MachineMatchTwo() bool {
	return p.MachineMatchingStrategy != nil && *p.MachineMatchingStrategy == "MATCH_TWO"
}

func (p *Policy) MachineMatchMost() bool {
	return p.MachineMatchingStrategy != nil && *p.MachineMatchingStrategy == "MATCH_MOST"
}

func (p *Policy) MachineMatchAll() bool {
	return p.MachineMatchingStrategy != nil && *p.MachineMatchingStrategy == "MATCH_ALL"
}

// Component matching strategy methods
func (p *Policy) ComponentMatchAny() bool {
	// Default to MATCH_ANY for backwards compatibility
	return p.ComponentMatchingStrategy == nil || *p.ComponentMatchingStrategy == "MATCH_ANY"
}

func (p *Policy) ComponentMatchTwo() bool {
	return p.ComponentMatchingStrategy != nil && *p.ComponentMatchingStrategy == "MATCH_TWO"
}

func (p *Policy) ComponentMatchMost() bool {
	return p.ComponentMatchingStrategy != nil && *p.ComponentMatchingStrategy == "MATCH_MOST"
}

func (p *Policy) ComponentMatchAll() bool {
	return p.ComponentMatchingStrategy != nil && *p.ComponentMatchingStrategy == "MATCH_ALL"
}

// Expiration strategy methods
func (p *Policy) RestrictAccess() bool {
	return p.ExpirationStrategy != nil && *p.ExpirationStrategy == "RESTRICT_ACCESS"
}

func (p *Policy) RevokeAccess() bool {
	// Default to REVOKE_ACCESS for backwards compatibility
	return p.ExpirationStrategy == nil || *p.ExpirationStrategy == "REVOKE_ACCESS"
}

func (p *Policy) MaintainAccess() bool {
	return p.ExpirationStrategy != nil && *p.ExpirationStrategy == "MAINTAIN_ACCESS"
}

func (p *Policy) AllowAccess() bool {
	return p.ExpirationStrategy != nil && *p.ExpirationStrategy == "ALLOW_ACCESS"
}

// Expiration basis methods
func (p *Policy) ExpireFromCreation() bool {
	// Default to FROM_CREATION for backwards compatibility
	return p.ExpirationBasis == nil || *p.ExpirationBasis == "FROM_CREATION"
}

func (p *Policy) ExpireFromFirstValidation() bool {
	return p.ExpirationBasis != nil && *p.ExpirationBasis == "FROM_FIRST_VALIDATION"
}

func (p *Policy) ExpireFromFirstActivation() bool {
	return p.ExpirationBasis != nil && *p.ExpirationBasis == "FROM_FIRST_ACTIVATION"
}

func (p *Policy) ExpireFromFirstUse() bool {
	return p.ExpirationBasis != nil && *p.ExpirationBasis == "FROM_FIRST_USE"
}

func (p *Policy) ExpireFromFirstDownload() bool {
	return p.ExpirationBasis != nil && *p.ExpirationBasis == "FROM_FIRST_DOWNLOAD"
}

// Renewal basis methods
func (p *Policy) RenewFromExpiry() bool {
	// Default to FROM_EXPIRY for backwards compatibility
	return p.RenewalBasis == nil || *p.RenewalBasis == "FROM_EXPIRY"
}

func (p *Policy) RenewFromNow() bool {
	return p.RenewalBasis != nil && *p.RenewalBasis == "FROM_NOW"
}

func (p *Policy) RenewFromNowIfExpired() bool {
	return p.RenewalBasis != nil && *p.RenewalBasis == "FROM_NOW_IF_EXPIRED"
}

// Authentication strategy methods
func (p *Policy) SupportsTokenAuth() bool {
	// Default to TOKEN for backwards compatibility
	return p.AuthenticationStrategy == nil || *p.AuthenticationStrategy == "TOKEN" || p.SupportsMixedAuth()
}

func (p *Policy) SupportsLicenseAuth() bool {
	return p.AuthenticationStrategy != nil && (*p.AuthenticationStrategy == "LICENSE" || p.SupportsMixedAuth())
}

func (p *Policy) SupportsSessionAuth() bool {
	return p.AuthenticationStrategy != nil && (*p.AuthenticationStrategy == "SESSION" || p.SupportsMixedAuth())
}

func (p *Policy) SupportsMixedAuth() bool {
	return p.AuthenticationStrategy != nil && *p.AuthenticationStrategy == "MIXED"
}

func (p *Policy) SupportsAuth() bool {
	return p.AuthenticationStrategy == nil || *p.AuthenticationStrategy != "NONE"
}

// Transfer strategy methods
func (p *Policy) ResetExpiryOnTransfer() bool {
	return p.TransferStrategy != nil && *p.TransferStrategy == "RESET_EXPIRY"
}

func (p *Policy) KeepExpiryOnTransfer() bool {
	// Default to KEEP_EXPIRY for backwards compatibility
	return p.TransferStrategy == nil || *p.TransferStrategy == "KEEP_EXPIRY"
}

// Heartbeat strategy methods
func (p *Policy) DeactivateDead() bool {
	// Default to DEACTIVATE_DEAD for backwards compatibility
	return p.HeartbeatCullStrategy == nil || *p.HeartbeatCullStrategy == "DEACTIVATE_DEAD"
}

func (p *Policy) KeepDead() bool {
	return p.HeartbeatCullStrategy != nil && *p.HeartbeatCullStrategy == "KEEP_DEAD"
}

func (p *Policy) ResurrectDead() bool {
	// Default to NO_REVIVE for backwards compatibility
	return p.HeartbeatResurrectionStrategy != nil && *p.HeartbeatResurrectionStrategy != "NO_REVIVE"
}

func (p *Policy) AlwaysResurrectDead() bool {
	return p.HeartbeatResurrectionStrategy != nil && *p.HeartbeatResurrectionStrategy == "ALWAYS_REVIVE"
}

// GetLazarusTTL returns the resurrection TTL in seconds
func (p *Policy) GetLazarusTTL() int {
	if p.HeartbeatResurrectionStrategy == nil {
		return 0
	}

	switch *p.HeartbeatResurrectionStrategy {
	case "15_MINUTE_REVIVE":
		return 15 * 60 // 15 minutes in seconds
	case "10_MINUTE_REVIVE":
		return 10 * 60 // 10 minutes in seconds
	case "5_MINUTE_REVIVE":
		return 5 * 60 // 5 minutes in seconds
	case "2_MINUTE_REVIVE":
		return 2 * 60 // 2 minutes in seconds
	case "1_MINUTE_REVIVE":
		return 1 * 60 // 1 minute in seconds
	default:
		return 0
	}
}

func (p *Policy) HeartbeatFromCreation() bool {
	return p.HeartbeatBasis != nil && *p.HeartbeatBasis == "FROM_CREATION"
}

func (p *Policy) HeartbeatFromFirstPing() bool {
	// Default to FROM_FIRST_PING for backwards compatibility
	return p.HeartbeatBasis == nil || *p.HeartbeatBasis == "FROM_FIRST_PING"
}

// Leasing strategy methods
func (p *Policy) ProcessLeasePerMachine() bool {
	// Default to PER_MACHINE
	return p.ProcessLeasingStrategy == nil || *p.ProcessLeasingStrategy == "PER_MACHINE"
}

func (p *Policy) ProcessLeasePerLicense() bool {
	return p.ProcessLeasingStrategy != nil && *p.ProcessLeasingStrategy == "PER_LICENSE"
}

func (p *Policy) ProcessLeasePerUser() bool {
	return p.ProcessLeasingStrategy != nil && *p.ProcessLeasingStrategy == "PER_USER"
}

func (p *Policy) MachineLeasePerLicense() bool {
	// Default to PER_LICENSE
	return p.MachineLeasingStrategy == nil || *p.MachineLeasingStrategy == "PER_LICENSE"
}

func (p *Policy) MachineLeasePerUser() bool {
	return p.MachineLeasingStrategy != nil && *p.MachineLeasingStrategy == "PER_USER"
}

// Overage strategy methods
func (p *Policy) AlwaysAllowOverage() bool {
	return p.OverageStrategy != nil && *p.OverageStrategy == "ALWAYS_ALLOW_OVERAGE"
}

func (p *Policy) Allow125xOverage() bool {
	return p.OverageStrategy != nil && *p.OverageStrategy == "ALLOW_1_25X_OVERAGE"
}

func (p *Policy) Allow15xOverage() bool {
	return p.OverageStrategy != nil && *p.OverageStrategy == "ALLOW_1_5X_OVERAGE"
}

func (p *Policy) Allow2xOverage() bool {
	return p.OverageStrategy != nil && *p.OverageStrategy == "ALLOW_2X_OVERAGE"
}

func (p *Policy) AllowOverage() bool {
	return p.OverageStrategy == nil || *p.OverageStrategy != "NO_OVERAGE"
}

func (p *Policy) NoOverage() bool {
	return p.OverageStrategy != nil && *p.OverageStrategy == "NO_OVERAGE"
}

// SetConcurrent sets the overage strategy based on concurrent flag (for backwards compatibility)
func (p *Policy) SetConcurrent(concurrent bool) {
	if concurrent {
		strategy := "ALWAYS_ALLOW_OVERAGE"
		p.OverageStrategy = &strategy
	} else {
		strategy := "NO_OVERAGE"
		p.OverageStrategy = &strategy
	}
}

// === VALIDATION METHODS ===
// Các methods để validate policy theo quy tắc của keygen-api

// ValidatePolicy validate toàn bộ policy theo các quy tắc business logic
// Trả về array các lỗi validation, empty array nếu valid
func (p *Policy) ValidatePolicy() []string {
	var errors []string

	// === REQUIRED FIELDS ===
	// Name là bắt buộc
	if p.Name == "" {
		errors = append(errors, "name is required")
	}

	// === DURATION VALIDATION ===
	// Duration phải hợp lệ nếu được set
	if p.Duration != nil {
		if *p.Duration <= 0 {
			errors = append(errors, "duration must be greater than 0")
		}
		// Max int32 để tránh overflow
		if *p.Duration > 2147483647 {
			errors = append(errors, "duration must be less than or equal to 2147483647")
		}
		// Minimum 1 day (86400 seconds) theo quy tắc keygen-api
		if *p.Duration < 86400 {
			errors = append(errors, "duration must be greater than or equal to 86400 (1 day)")
		}
	}

	// Heartbeat duration validation
	if p.HeartbeatDuration != nil {
		if *p.HeartbeatDuration <= 0 {
			errors = append(errors, "heartbeat_duration must be greater than 0")
		}
		if *p.HeartbeatDuration > 2147483647 {
			errors = append(errors, "heartbeat_duration must be less than or equal to 2147483647")
		}
		if *p.HeartbeatDuration < 60 { // 1 minute in seconds
			errors = append(errors, "heartbeat_duration must be greater than or equal to 60 (1 minute)")
		}
	}

	// Max machines validation
	if p.MaxMachines != nil {
		if *p.MaxMachines < 0 {
			errors = append(errors, "max_machines must be greater than or equal to 0")
		}
		if *p.MaxMachines > 2147483647 {
			errors = append(errors, "max_machines must be less than or equal to 2147483647")
		}
		if p.IsFloating() && *p.MaxMachines < 1 {
			errors = append(errors, "max_machines must be greater than or equal to 1 for floating policy")
		}
		if p.IsNodeLocked() && *p.MaxMachines != 1 {
			errors = append(errors, "max_machines must be equal to 1 for non-floating policy")
		}
	}

	// Max cores validation
	if p.MaxCores != nil {
		if *p.MaxCores < 1 {
			errors = append(errors, "max_cores must be greater than or equal to 1")
		}
		if *p.MaxCores > 2147483647 {
			errors = append(errors, "max_cores must be less than or equal to 2147483647")
		}
	}

	// Max uses validation
	if p.MaxUses != nil {
		if *p.MaxUses < 0 {
			errors = append(errors, "max_uses must be greater than or equal to 0")
		}
		if *p.MaxUses > 2147483647 {
			errors = append(errors, "max_uses must be less than or equal to 2147483647")
		}
	}

	// Max processes validation
	if p.MaxProcesses != nil {
		if *p.MaxProcesses <= 0 {
			errors = append(errors, "max_processes must be greater than 0")
		}
		if *p.MaxProcesses > 2147483647 {
			errors = append(errors, "max_processes must be less than or equal to 2147483647")
		}
	}

	// Max users validation
	if p.MaxUsers != nil {
		if *p.MaxUsers <= 0 {
			errors = append(errors, "max_users must be greater than 0")
		}
		if *p.MaxUsers > 2147483647 {
			errors = append(errors, "max_users must be less than or equal to 2147483647")
		}
	}

	// Check-in validation
	if p.RequiresCheckIn() {
		if p.CheckInInterval == nil {
			errors = append(errors, "check_in_interval is required when require_check_in is true")
		} else if !contains(CheckInIntervals, *p.CheckInInterval) {
			errors = append(errors, "check_in_interval must be one of: day, week, month, year")
		}

		if p.CheckInIntervalCount == nil {
			errors = append(errors, "check_in_interval_count is required when require_check_in is true")
		} else if *p.CheckInIntervalCount < 1 || *p.CheckInIntervalCount > 365 {
			errors = append(errors, "check_in_interval_count must be a number between 1 and 365 inclusive")
		}
	}

	// Strategy validations
	errors = append(errors, p.validateStrategies()...)

	// Overage strategy compatibility validations
	errors = append(errors, p.validateOverageCompatibility()...)

	// Pool and encryption compatibility
	if p.UsesPool() && p.IsEncrypted() {
		errors = append(errors, "cannot be encrypted and use a pool")
	}

	if p.UsesPool() && p.HasScheme() {
		errors = append(errors, "cannot use a scheme and use a pool")
	}

	return errors
}

// validateStrategies validates all strategy fields
func (p *Policy) validateStrategies() []string {
	var errors []string

	// Crypto scheme validation
	if p.Scheme != "" && !contains(CryptoSchemes, string(p.Scheme)) {
		errors = append(errors, "scheme must be one of the supported crypto schemes")
	}

	// Machine uniqueness strategy validation
	if p.MachineUniquenessStrategy != nil && !contains(MachineUniquenessStrategies, *p.MachineUniquenessStrategy) {
		errors = append(errors, "machine_uniqueness_strategy must be one of: UNIQUE_PER_ACCOUNT, UNIQUE_PER_PRODUCT, UNIQUE_PER_POLICY, UNIQUE_PER_LICENSE")
	}

	// Component uniqueness strategy validation
	if p.ComponentUniquenessStrategy != nil && !contains(ComponentUniquenessStrategies, *p.ComponentUniquenessStrategy) {
		errors = append(errors, "component_uniqueness_strategy must be one of: UNIQUE_PER_ACCOUNT, UNIQUE_PER_PRODUCT, UNIQUE_PER_POLICY, UNIQUE_PER_LICENSE, UNIQUE_PER_MACHINE")
	}

	// Machine matching strategy validation
	if p.MachineMatchingStrategy != nil && !contains(MachineMatchingStrategies, *p.MachineMatchingStrategy) {
		errors = append(errors, "machine_matching_strategy must be one of: MATCH_ANY, MATCH_TWO, MATCH_MOST, MATCH_ALL")
	}

	// Component matching strategy validation
	if p.ComponentMatchingStrategy != nil && !contains(ComponentMatchingStrategies, *p.ComponentMatchingStrategy) {
		errors = append(errors, "component_matching_strategy must be one of: MATCH_ANY, MATCH_TWO, MATCH_MOST, MATCH_ALL")
	}

	// Expiration strategy validation
	if p.ExpirationStrategy != nil && !contains(ExpirationStrategies, *p.ExpirationStrategy) {
		errors = append(errors, "expiration_strategy must be one of: RESTRICT_ACCESS, REVOKE_ACCESS, MAINTAIN_ACCESS, ALLOW_ACCESS")
	}

	// Expiration basis validation
	if p.ExpirationBasis != nil && !contains(ExpirationBases, *p.ExpirationBasis) {
		errors = append(errors, "expiration_basis must be one of: FROM_CREATION, FROM_FIRST_VALIDATION, FROM_FIRST_ACTIVATION, FROM_FIRST_DOWNLOAD, FROM_FIRST_USE")
	}

	// Renewal basis validation
	if p.RenewalBasis != nil && !contains(RenewalBases, *p.RenewalBasis) {
		errors = append(errors, "renewal_basis must be one of: FROM_EXPIRY, FROM_NOW, FROM_NOW_IF_EXPIRED")
	}

	// Transfer strategy validation
	if p.TransferStrategy != nil && !contains(TransferStrategies, *p.TransferStrategy) {
		errors = append(errors, "transfer_strategy must be one of: RESET_EXPIRY, KEEP_EXPIRY")
	}

	// Authentication strategy validation
	if p.AuthenticationStrategy != nil && !contains(AuthenticationStrategies, *p.AuthenticationStrategy) {
		errors = append(errors, "authentication_strategy must be one of: TOKEN, LICENSE, SESSION, MIXED, NONE")
	}

	// Heartbeat cull strategy validation
	if p.HeartbeatCullStrategy != nil && !contains(HeartbeatCullStrategies, *p.HeartbeatCullStrategy) {
		errors = append(errors, "heartbeat_cull_strategy must be one of: DEACTIVATE_DEAD, KEEP_DEAD")
	}

	// Heartbeat resurrection strategy validation
	if p.HeartbeatResurrectionStrategy != nil && !contains(HeartbeatResurrectionStrategies, *p.HeartbeatResurrectionStrategy) {
		errors = append(errors, "heartbeat_resurrection_strategy must be one of: ALWAYS_REVIVE, 15_MINUTE_REVIVE, 10_MINUTE_REVIVE, 5_MINUTE_REVIVE, 2_MINUTE_REVIVE, 1_MINUTE_REVIVE, NO_REVIVE")
	}

	// Heartbeat basis validation
	if p.HeartbeatBasis != nil && !contains(HeartbeatBases, *p.HeartbeatBasis) {
		errors = append(errors, "heartbeat_basis must be one of: FROM_CREATION, FROM_FIRST_PING")
	}

	// Machine leasing strategy validation
	if p.MachineLeasingStrategy != nil && !contains(MachineLeasingStrategies, *p.MachineLeasingStrategy) {
		errors = append(errors, "machine_leasing_strategy must be one of: PER_LICENSE, PER_USER")
	}

	// Process leasing strategy validation
	if p.ProcessLeasingStrategy != nil && !contains(ProcessLeasingStrategies, *p.ProcessLeasingStrategy) {
		errors = append(errors, "process_leasing_strategy must be one of: PER_LICENSE, PER_MACHINE, PER_USER")
	}

	// Overage strategy validation
	if p.OverageStrategy != nil && !contains(OverageStrategies, *p.OverageStrategy) {
		errors = append(errors, "overage_strategy must be one of: ALWAYS_ALLOW_OVERAGE, ALLOW_1_25X_OVERAGE, ALLOW_1_5X_OVERAGE, ALLOW_2X_OVERAGE, NO_OVERAGE")
	}

	return errors
}

// validateOverageCompatibility validates overage strategy compatibility
func (p *Policy) validateOverageCompatibility() []string {
	var errors []string

	// Overage strategy requires floating policy
	if p.OverageStrategy != nil && *p.OverageStrategy != "NO_OVERAGE" && p.IsNodeLocked() {
		errors = append(errors, "overage_strategy can only be used with floating policies")
	}

	// Overage strategy requires max_machines
	if p.OverageStrategy != nil && *p.OverageStrategy != "NO_OVERAGE" && (p.MaxMachines == nil || *p.MaxMachines == 0) {
		errors = append(errors, "overage_strategy requires max_machines to be set and greater than 0")
	}

	return errors
}

// contains checks if a slice contains a string
func contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}

// === DEFAULT VALUES SETUP ===
// SetDefaults set các giá trị mặc định cho policy
// Tương đương với Ruby's before_create callbacks trong keygen-api
func (p *Policy) SetDefaults() {
	// === UNIQUENESS STRATEGIES ===
	// Set default machine uniqueness strategy
	// UNIQUE_PER_LICENSE: mỗi license có thể có machines riêng
	if p.MachineUniquenessStrategy == nil {
		strategy := "UNIQUE_PER_LICENSE"
		p.MachineUniquenessStrategy = &strategy
	}

	// Set default component uniqueness strategy
	// UNIQUE_PER_MACHINE: components unique trong từng machine
	if p.ComponentUniquenessStrategy == nil {
		strategy := "UNIQUE_PER_MACHINE"
		p.ComponentUniquenessStrategy = &strategy
	}

	// === MATCHING STRATEGIES ===
	// Set default machine matching strategy
	// MATCH_ANY: chỉ cần match 1 component là đủ
	if p.MachineMatchingStrategy == nil {
		strategy := "MATCH_ANY"
		p.MachineMatchingStrategy = &strategy
	}

	// Set default component matching strategy
	// MATCH_ANY: flexible matching, dễ dàng cho user
	if p.ComponentMatchingStrategy == nil {
		strategy := "MATCH_ANY"
		p.ComponentMatchingStrategy = &strategy
	}

	// Set default expiration strategy
	if p.ExpirationStrategy == nil {
		strategy := "RESTRICT_ACCESS"
		p.ExpirationStrategy = &strategy
	}

	// Set default expiration basis
	if p.ExpirationBasis == nil {
		basis := "FROM_CREATION"
		p.ExpirationBasis = &basis
	}

	// Set default renewal basis
	if p.RenewalBasis == nil {
		basis := "FROM_EXPIRY"
		p.RenewalBasis = &basis
	}

	// Set default transfer strategy
	if p.TransferStrategy == nil {
		strategy := "KEEP_EXPIRY"
		p.TransferStrategy = &strategy
	}

	// Set default authentication strategy
	if p.AuthenticationStrategy == nil {
		strategy := "TOKEN"
		p.AuthenticationStrategy = &strategy
	}

	// Set default heartbeat cull strategy
	if p.HeartbeatCullStrategy == nil {
		strategy := "DEACTIVATE_DEAD"
		p.HeartbeatCullStrategy = &strategy
	}

	// Set default heartbeat resurrection strategy
	if p.HeartbeatResurrectionStrategy == nil {
		strategy := "NO_REVIVE"
		p.HeartbeatResurrectionStrategy = &strategy
	}

	// Set default heartbeat basis
	if p.HeartbeatBasis == nil {
		basis := "FROM_FIRST_PING"
		p.HeartbeatBasis = &basis
	}

	// Set default machine leasing strategy
	if p.MachineLeasingStrategy == nil {
		strategy := "PER_LICENSE"
		p.MachineLeasingStrategy = &strategy
	}

	// Set default process leasing strategy
	if p.ProcessLeasingStrategy == nil {
		strategy := "PER_MACHINE"
		p.ProcessLeasingStrategy = &strategy
	}

	// Set default overage strategy based on concurrent flag (backwards compatibility)
	if p.OverageStrategy == nil {
		if p.Concurrent {
			strategy := "ALWAYS_ALLOW_OVERAGE"
			p.OverageStrategy = &strategy
		} else {
			strategy := "NO_OVERAGE"
			p.OverageStrategy = &strategy
		}
	}

	// Set default max machines for floating policies
	if p.MaxMachines == nil && p.Floating {
		maxMachines := 1
		p.MaxMachines = &maxMachines
	}

	// Set default max machines for node-locked policies
	if p.MaxMachines == nil && !p.Floating {
		maxMachines := 1
		p.MaxMachines = &maxMachines
	}
}

// Pool management methods (equivalent to Ruby's pool operations)
// TODO: Implement after Key entity is created

// GetPoolSize returns the total number of licenses in the pool (using licenses as keys for now)
func (p *Policy) GetPoolSize() int {
	if !p.UsesPool() {
		return 0
	}
	return len(p.Licenses)
}

// GetAvailablePoolSize returns the number of available licenses in the pool
func (p *Policy) GetAvailablePoolSize() int {
	if !p.UsesPool() {
		return 0
	}

	count := 0
	for _, license := range p.Licenses {
		if !license.Suspended && (license.ExpiresAt == nil || license.ExpiresAt.After(time.Now())) {
			count++
		}
	}
	return count
}

// IsPoolEmpty returns true if the pool has no available licenses
func (p *Policy) IsPoolEmpty() bool {
	return p.GetAvailablePoolSize() == 0
}

// IsPoolFull returns true if all licenses in the pool are used
func (p *Policy) IsPoolFull() bool {
	return p.GetAvailablePoolSize() == 0 && p.GetPoolSize() > 0
}

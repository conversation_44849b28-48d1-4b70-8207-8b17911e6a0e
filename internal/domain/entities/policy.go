package entities

import (
	"time"

	"gorm.io/gorm"
)

type Policy struct {
	ID             string `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	OrganizationID string `json:"organization_id" gorm:"type:uuid;not null"`
	ProductID      string `json:"product_id" gorm:"type:uuid;not null"`
	Name        string  `json:"name" gorm:"size:255;not null"`
	Description *string `json:"description,omitempty" gorm:"type:text"`

	// Basic configuration
	Strict      bool  `json:"strict" gorm:"default:false"`
	Protected   *bool `json:"protected"`
	Duration    *int  `json:"duration"` // in seconds
	LockVersion int   `json:"lock_version" gorm:"default:0"`

	// License behavior
	Floating   bool `json:"floating" gorm:"default:false"`  // License transferability
	UsePool    bool `json:"use_pool" gorm:"default:false"`  // License pooling
	Encrypted  bool `json:"encrypted" gorm:"default:false"` // License encryption
	Concurrent bool `json:"concurrent" gorm:"default:true"` // Concurrent usage allowed

	// Cryptographic scheme
	Scheme LicenseScheme `json:"scheme" gorm:"default:ED25519_SIGN"`

	// Limits and constraints
	MaxMachines      *int `json:"max_machines,omitempty"`
	MaxUses          *int `json:"max_uses,omitempty"`
	MaxCores         *int `json:"max_cores,omitempty"`
	MaxUsers         *int `json:"max_users,omitempty"`
	MaxProcesses     *int `json:"max_processes,omitempty"`
	MaxActivations   *int `json:"max_activations,omitempty"`
	MaxDeactivations *int `json:"max_deactivations,omitempty"`

	// Heartbeat configuration
	RequireHeartbeat              bool    `json:"require_heartbeat" gorm:"default:false"`
	HeartbeatDuration             *int    `json:"heartbeat_duration"` // in seconds
	HeartbeatBasis                *string `json:"heartbeat_basis"`
	HeartbeatCullStrategy         *string `json:"heartbeat_cull_strategy"`
	HeartbeatResurrectionStrategy *string `json:"heartbeat_resurrection_strategy"`

	// Check-in configuration
	RequireCheckIn       bool    `json:"require_check_in" gorm:"default:false"`
	CheckInInterval      *string `json:"check_in_interval"`
	CheckInIntervalCount *int    `json:"check_in_interval_count"`

	// Fingerprint strategies
	FingerprintUniquenessStrategy *string `json:"fingerprint_uniqueness_strategy"`
	FingerprintMatchingStrategy   *string `json:"fingerprint_matching_strategy"`

	// Machine strategies
	MachineUniquenessStrategy *string `json:"machine_uniqueness_strategy"`
	MachineMatchingStrategy   *string `json:"machine_matching_strategy"`
	MachineLeasingStrategy    *string `json:"machine_leasing_strategy"`

	// Component strategies
	ComponentUniquenessStrategy *string `json:"component_uniqueness_strategy"`
	ComponentMatchingStrategy   *string `json:"component_matching_strategy"`
	ComponentsStrategy          *string `json:"components_strategy"`
	ComponentsFingerprint       *string `json:"components_fingerprint"`

	// Process strategies
	ProcessLeasingStrategy *string `json:"process_leasing_strategy"`

	// Expiration configuration
	ExpirationStrategy *string `json:"expiration_strategy"`
	ExpirationBasis    *string `json:"expiration_basis"`
	RenewalBasis       *string `json:"renewal_basis"`

	// Authentication and transfer
	AuthenticationStrategy *string `json:"authentication_strategy"`
	TransferStrategy       *string `json:"transfer_strategy"`
	LeasingStrategy        *string `json:"leasing_strategy"`
	OverageStrategy        *string `json:"overage_strategy"`

	// Scope requirements
	RequireProductScope     bool `json:"require_product_scope" gorm:"default:false"`
	RequirePolicyScope      bool `json:"require_policy_scope" gorm:"default:false"`
	RequireMachineScope     bool `json:"require_machine_scope" gorm:"default:false"`
	RequireFingerprintScope bool `json:"require_fingerprint_scope" gorm:"default:false"`
	RequireUserScope        bool `json:"require_user_scope" gorm:"default:false"`
	RequireEnvironmentScope bool `json:"require_environment_scope" gorm:"default:false"`
	RequireChecksumScope    bool `json:"require_checksum_scope" gorm:"default:false"`
	RequireVersionScope     bool `json:"require_version_scope" gorm:"default:false"`
	RequireComponentsScope  bool `json:"require_components_scope" gorm:"default:false"`

	Metadata Metadata `json:"metadata" gorm:"type:jsonb;default:'{}'"`

	// Timestamps
	CreatedAt time.Time      `json:"created_at" gorm:"not null"`
	UpdatedAt time.Time      `json:"updated_at" gorm:"not null"`
	DeletedAt gorm.DeletedAt `json:"deleted_at,omitempty" gorm:"index"`

	// Relations
	Organization Organization `json:"organization,omitempty" gorm:"foreignKey:OrganizationID"`
	Product      Product      `json:"product,omitempty" gorm:"foreignKey:ProductID"`
	Licenses     []License    `json:"licenses,omitempty" gorm:"foreignKey:PolicyID"`
	Machines     []Machine    `json:"machines,omitempty" gorm:"foreignKey:PolicyID"`
}

// TableName overrides the table name used by GORM
func (Policy) TableName() string {
	return "policies"
}

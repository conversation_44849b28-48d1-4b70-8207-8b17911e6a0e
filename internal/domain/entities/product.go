package entities

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
	"time"

	"gorm.io/gorm"
)

type Product struct {
	ID             string  `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	OrganizationID string  `json:"organization_id" gorm:"type:uuid;not null"`
	Name           string  `json:"name" gorm:"size:255;not null"`
	Code           string  `json:"code" gorm:"size:255;not null"`
	Key            string  `json:"key" gorm:"size:255;uniqueIndex;not null"` // Legacy support
	Description    *string `json:"description,omitempty" gorm:"type:text"`
	URL            *string `json:"url,omitempty" gorm:"size:512"`
	
	// Platform and distribution
	Platforms            ProductPlatforms `json:"platforms" gorm:"type:jsonb;default:'{\"supported\": [], \"metadata\": {}}'"`
	DistributionStrategy *string          `json:"distribution_strategy,omitempty" gorm:"size:255"`
	
	Metadata Metadata `json:"metadata" gorm:"type:jsonb;default:'{}'"`
	
	// Timestamps
	CreatedAt time.Time      `json:"created_at" gorm:"not null"`
	UpdatedAt time.Time      `json:"updated_at" gorm:"not null"`
	DeletedAt gorm.DeletedAt `json:"deleted_at,omitempty" gorm:"index"`
	
	// Relations
	Organization Organization `json:"organization,omitempty" gorm:"foreignKey:OrganizationID"`
	Policies     []Policy     `json:"policies,omitempty" gorm:"foreignKey:ProductID"`
	Licenses     []License    `json:"licenses,omitempty" gorm:"foreignKey:ProductID"`
}

type ProductPlatforms struct {
	Supported []string               `json:"supported,omitempty"`
	Metadata  map[string]any `json:"metadata,omitempty"`
}

// Scan implements the sql.Scanner interface for JSONB scanning
func (p *ProductPlatforms) Scan(value interface{}) error {
	if value == nil {
		*p = ProductPlatforms{
			Supported: []string{},
			Metadata:  make(map[string]any),
		}
		return nil
	}

	var data []byte
	switch v := value.(type) {
	case []byte:
		data = v
	case string:
		data = []byte(v)
	default:
		return fmt.Errorf("cannot scan %T into ProductPlatforms", value)
	}

	if len(data) == 0 {
		*p = ProductPlatforms{
			Supported: []string{},
			Metadata:  make(map[string]any),
		}
		return nil
	}

	return json.Unmarshal(data, p)
}

// Value implements the driver.Valuer interface for JSONB storage
func (p ProductPlatforms) Value() (driver.Value, error) {
	return json.Marshal(p)
}

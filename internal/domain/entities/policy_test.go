package entities

import (
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
)

// TestPolicySetDefaults test SetDefaults functionality
func TestPolicySetDefaults(t *testing.T) {
	// Test policy without any strategies set
	policy := &Policy{
		ID:             uuid.New().String(),
		OrganizationID: uuid.New().String(),
		ProductID:      uuid.New().String(),
		Name:           "Test Policy",
		Floating:       false,
		Duration:       &[]int{86400 * 7}[0], // 7 days
	}

	// Before SetDefaults - strategies should be nil
	assert.Nil(t, policy.MachineUniquenessStrategy)
	assert.<PERSON>l(t, policy.ComponentUniquenessStrategy)
	assert.Nil(t, policy.MachineMatchingStrategy)
	assert.Nil(t, policy.ComponentMatchingStrategy)

	// Call SetDefaults
	policy.SetDefaults()

	// After SetDefaults - strategies should have default values
	assert.NotNil(t, policy.MachineUniquenessStrategy)
	assert.Equal(t, "UNIQUE_PER_LICENSE", *policy.MachineUniquenessStrategy)

	assert.NotNil(t, policy.ComponentUniquenessStrategy)
	assert.Equal(t, "UNIQUE_PER_MACHINE", *policy.ComponentUniquenessStrategy)

	assert.NotNil(t, policy.MachineMatchingStrategy)
	assert.Equal(t, "MATCH_ANY", *policy.MachineMatchingStrategy)

	assert.NotNil(t, policy.ComponentMatchingStrategy)
	assert.Equal(t, "MATCH_ANY", *policy.ComponentMatchingStrategy)

	assert.NotNil(t, policy.ExpirationStrategy)
	assert.Equal(t, "RESTRICT_ACCESS", *policy.ExpirationStrategy)

	assert.NotNil(t, policy.AuthenticationStrategy)
	assert.Equal(t, "TOKEN", *policy.AuthenticationStrategy)

	assert.NotNil(t, policy.OverageStrategy)
	assert.Equal(t, "NO_OVERAGE", *policy.OverageStrategy)

	// Test default max machines based on floating
	assert.NotNil(t, policy.MaxMachines)
	assert.Equal(t, 1, *policy.MaxMachines) // Node-locked default
}

// TestPolicyFloatingDefaults test floating policy defaults
func TestPolicyFloatingDefaults(t *testing.T) {
	// Test floating policy defaults
	floatingPolicy := &Policy{
		ID:             uuid.New().String(),
		OrganizationID: uuid.New().String(),
		ProductID:      uuid.New().String(),
		Name:           "Floating Policy",
		Floating:       true,
		Duration:       &[]int{86400 * 7}[0],
	}

	floatingPolicy.SetDefaults()
	assert.NotNil(t, floatingPolicy.MaxMachines)
	assert.Equal(t, 1, *floatingPolicy.MaxMachines) // Both floating and node-locked default to 1
}

// TestPolicyBusinessLogicMethods test business logic methods
func TestPolicyBusinessLogicMethods(t *testing.T) {
	// Test policy with specific strategies
	policy := &Policy{
		ID:             uuid.New().String(),
		OrganizationID: uuid.New().String(),
		ProductID:      uuid.New().String(),
		Name:           "Business Logic Policy",
		Floating:       true,
		MaxMachines:    &[]int{10}[0],
		Duration:       &[]int{86400 * 30}[0], // 30 days
	}

	// Set specific strategies
	machineStrategy := "UNIQUE_PER_ACCOUNT"
	componentStrategy := "UNIQUE_PER_PRODUCT"
	machineMatching := "MATCH_ALL"
	componentMatching := "MATCH_MOST"
	expirationStrategy := "RESTRICT_ACCESS"
	authStrategy := "TOKEN"
	processLeasing := "PER_MACHINE"
	machineLeasing := "PER_USER"
	overageStrategy := "ALLOW_1_25X_OVERAGE"

	policy.MachineUniquenessStrategy = &machineStrategy
	policy.ComponentUniquenessStrategy = &componentStrategy
	policy.MachineMatchingStrategy = &machineMatching
	policy.ComponentMatchingStrategy = &componentMatching
	policy.ExpirationStrategy = &expirationStrategy
	policy.AuthenticationStrategy = &authStrategy
	policy.ProcessLeasingStrategy = &processLeasing
	policy.MachineLeasingStrategy = &machineLeasing
	policy.OverageStrategy = &overageStrategy

	// Test business logic methods
	assert.True(t, policy.IsFloating())
	assert.False(t, policy.IsNodeLocked())
	assert.True(t, policy.MachineUniquePerAccount())
	assert.False(t, policy.MachineUniquePerLicense())
	assert.True(t, policy.ComponentUniquePerProduct())
	assert.False(t, policy.ComponentUniquePerMachine())
	assert.True(t, policy.MachineMatchAll())
	assert.False(t, policy.MachineMatchAny())
	assert.True(t, policy.ComponentMatchMost())
	assert.False(t, policy.ComponentMatchAny())
	assert.True(t, policy.RestrictAccess())
	assert.False(t, policy.AllowAccess())
	assert.True(t, policy.SupportsTokenAuth())
	assert.False(t, policy.SupportsLicenseAuth())
	assert.True(t, policy.ProcessLeasePerMachine())
	assert.False(t, policy.ProcessLeasePerLicense())
	assert.True(t, policy.MachineLeasePerUser())
	assert.False(t, policy.MachineLeasePerLicense())
	assert.True(t, policy.Allow125xOverage())
	assert.False(t, policy.NoOverage())
}

// TestPolicyValidation test validation rules
func TestPolicyValidation(t *testing.T) {
	// Test valid policy
	validPolicy := &Policy{
		ID:                uuid.New().String(),
		OrganizationID:    uuid.New().String(),
		ProductID:         uuid.New().String(),
		Name:              "Valid Policy",
		Floating:          true,
		Duration:          &[]int{86400 * 30}[0], // 30 days
		MaxMachines:       &[]int{10}[0],
		HeartbeatDuration: &[]int{600}[0], // 10 minutes
	}

	validPolicy.SetDefaults()
	validationErrors := validPolicy.ValidatePolicy()
	assert.Empty(t, validationErrors, "Valid policy should have no validation errors")

	// Test invalid policy - missing name
	invalidPolicy1 := &Policy{
		Name:     "", // Empty name
		Duration: &[]int{86400 * 7}[0],
	}

	validationErrors = invalidPolicy1.ValidatePolicy()
	assert.NotEmpty(t, validationErrors)
	assert.Contains(t, validationErrors[0], "name is required")

	// Test invalid policy - duration too short
	invalidPolicy2 := &Policy{
		Name:     "Invalid Duration Policy",
		Duration: &[]int{3600}[0], // 1 hour - less than 1 day
	}

	validationErrors = invalidPolicy2.ValidatePolicy()
	assert.NotEmpty(t, validationErrors)
	assert.Contains(t, validationErrors[0], "duration must be greater than or equal to 86400")

	// Test invalid policy - heartbeat duration too short
	invalidPolicy3 := &Policy{
		Name:              "Invalid Heartbeat Policy",
		Duration:          &[]int{86400 * 7}[0],
		HeartbeatDuration: &[]int{30}[0], // 30 seconds - less than 1 minute
	}

	validationErrors = invalidPolicy3.ValidatePolicy()
	assert.NotEmpty(t, validationErrors)
	assert.Contains(t, validationErrors[0], "heartbeat_duration must be greater than or equal to 60")

	// Test invalid policy - negative max machines
	invalidPolicy4 := &Policy{
		Name:        "Invalid Max Machines Policy",
		Duration:    &[]int{86400 * 7}[0],
		MaxMachines: &[]int{-1}[0],
	}

	validationErrors = invalidPolicy4.ValidatePolicy()
	assert.NotEmpty(t, validationErrors)
	assert.Contains(t, validationErrors[0], "max_machines must be greater than or equal to 0")
}

// TestPolicyStrategyRanks test strategy ranking system
func TestPolicyStrategyRanks(t *testing.T) {
	policy := &Policy{
		ID:             uuid.New().String(),
		OrganizationID: uuid.New().String(),
		ProductID:      uuid.New().String(),
		Name:           "Rank Test Policy",
	}

	// Test machine uniqueness strategy ranks
	accountStrategy := "UNIQUE_PER_ACCOUNT"
	policy.MachineUniquenessStrategy = &accountStrategy
	assert.Equal(t, 4, policy.GetMachineUniquenessStrategyRank()) // Highest rank

	productStrategy := "UNIQUE_PER_PRODUCT"
	policy.MachineUniquenessStrategy = &productStrategy
	assert.Equal(t, 3, policy.GetMachineUniquenessStrategyRank())

	policyStrategy := "UNIQUE_PER_POLICY"
	policy.MachineUniquenessStrategy = &policyStrategy
	assert.Equal(t, 2, policy.GetMachineUniquenessStrategyRank())

	licenseStrategy := "UNIQUE_PER_LICENSE"
	policy.MachineUniquenessStrategy = &licenseStrategy
	assert.Equal(t, 1, policy.GetMachineUniquenessStrategyRank()) // Lowest rank

	// Test invalid strategy
	invalidStrategy := "INVALID_STRATEGY"
	policy.MachineUniquenessStrategy = &invalidStrategy
	assert.Equal(t, -1, policy.GetMachineUniquenessStrategyRank())
}

// TestPolicyRequiresHeartbeat test heartbeat requirement logic
func TestPolicyRequiresHeartbeat(t *testing.T) {
	// Policy with RequireHeartbeat = true
	policy1 := &Policy{
		RequireHeartbeat: true,
	}
	assert.True(t, policy1.RequiresHeartbeat())

	// Policy with RequireHeartbeat = false but has HeartbeatDuration
	policy2 := &Policy{
		RequireHeartbeat:  false,
		HeartbeatDuration: &[]int{600}[0],
	}
	assert.False(t, policy2.RequiresHeartbeat()) // Only checks RequireHeartbeat field

	// Policy with RequireHeartbeat = false and no HeartbeatDuration
	policy3 := &Policy{
		RequireHeartbeat: false,
	}
	assert.False(t, policy3.RequiresHeartbeat())
}

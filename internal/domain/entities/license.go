package entities

import (
	"time"

	"gorm.io/gorm"
)

type License struct {
	ID             string `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	OrganizationID string `json:"organization_id" gorm:"type:uuid;not null"`
	ProductID      string `json:"product_id" gorm:"type:uuid;not null"`
	PolicyID       string `json:"policy_id" gorm:"type:uuid;not null"`
	Key            string `json:"key" gorm:"size:255;not null"`
	Name           *string `json:"name,omitempty" gorm:"size:255"`
	
	// License ownership (polymorphic)
	OwnerType string `json:"owner_type" gorm:"size:50;not null;check:owner_type IN ('user', 'organization')"`
	OwnerID   string `json:"owner_id" gorm:"type:uuid;not null"`

	// License state
	Status    string `json:"status" gorm:"size:50;default:'active';check:status IN ('active', 'expired', 'suspended', 'banned')"`
	Suspended bool   `json:"suspended" gorm:"default:false"`
	Protected bool   `json:"protected" gorm:"default:false"`

	// Usage tracking
	Uses      int        `json:"uses" gorm:"default:0"`
	ExpiresAt *time.Time `json:"expires_at,omitempty"`
	LastUsed  *time.Time `json:"last_used,omitempty"`

	// Policy overrides (allow per-license customization)
	MaxUsesOverride      *int `json:"max_uses_override,omitempty"`
	MaxMachinesOverride  *int `json:"max_machines_override,omitempty"`
	MaxCoresOverride     *int `json:"max_cores_override,omitempty"`
	MaxUsersOverride     *int `json:"max_users_override,omitempty"`
	MaxProcessesOverride *int `json:"max_processes_override,omitempty"`

	// Cached counts for performance
	MachinesCount     int `json:"machines_count" gorm:"default:0"`
	MachinesCoreCount int `json:"machines_core_count" gorm:"default:0"`
	LicenseUsersCount int `json:"license_users_count" gorm:"default:0"`

	// Heartbeat and validation tracking
	LastCheckInAt         *time.Time `json:"last_check_in_at"`
	LastValidatedAt       *time.Time `json:"last_validated_at"`
	LastValidatedChecksum *string    `json:"last_validated_checksum"`
	LastValidatedVersion  *string    `json:"last_validated_version"`
	LastCheckOutAt        *time.Time `json:"last_check_out_at"`

	// Event tracking (for notification management)
	LastExpirationEventSentAt   *time.Time `json:"last_expiration_event_sent_at"`
	LastCheckInEventSentAt      *time.Time `json:"last_check_in_event_sent_at"`
	LastExpiringSoonEventSentAt *time.Time `json:"last_expiring_soon_event_sent_at"`
	LastCheckInSoonEventSentAt  *time.Time `json:"last_check_in_soon_event_sent_at"`

	Metadata Metadata `json:"metadata" gorm:"type:jsonb;default:'{}'"`

	// Timestamps
	CreatedAt time.Time      `json:"created_at" gorm:"not null"`
	UpdatedAt time.Time      `json:"updated_at" gorm:"not null"`
	DeletedAt gorm.DeletedAt `json:"deleted_at,omitempty" gorm:"index"`

	// Relations
	Organization Organization `json:"organization,omitempty" gorm:"foreignKey:OrganizationID"`
	Product      Product      `json:"product,omitempty" gorm:"foreignKey:ProductID"`
	Policy       Policy       `json:"policy,omitempty" gorm:"foreignKey:PolicyID"`
	Machines     []Machine    `json:"machines,omitempty" gorm:"foreignKey:LicenseID"`
}

// TableName overrides the table name used by GORM
func (License) TableName() string {
	return "licenses"
}

// License statuses
const (
	LicenseStatusActive    = "active"
	LicenseStatusExpired   = "expired"
	LicenseStatusSuspended = "suspended"
	LicenseStatusBanned    = "banned"
)

// License owner types
const (
	LicenseOwnerTypeUser         = "user"
	LicenseOwnerTypeOrganization = "organization"
)

// IsOwnedByUser checks if the license is owned by a user
func (l *License) IsOwnedByUser() bool {
	return l.OwnerType == LicenseOwnerTypeUser
}

// IsOwnedByOrganization checks if the license is owned by an organization
func (l *License) IsOwnedByOrganization() bool {
	return l.OwnerType == LicenseOwnerTypeOrganization
}

// IsExpired checks if the license has expired
func (l *License) IsExpired() bool {
	if l.ExpiresAt == nil {
		return false
	}
	return time.Now().After(*l.ExpiresAt) || l.Status == LicenseStatusExpired
}

// IsActive checks if the license is active and not expired or suspended
func (l *License) IsActive() bool {
	return l.Status == LicenseStatusActive && !l.Suspended && !l.IsExpired()
}

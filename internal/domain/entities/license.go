package entities

import (
	"time"

	"gorm.io/gorm"
)

// License đại diện cho một license key với tất cả metadata và rules
// Trong keygen-api, license là core entity chứa license key và inherit rules từ policy
// License có thể override policy limits và track usage/validation
// Mapping từ Ruby License model với đầy đủ relationships và business logic
type License struct {
	// === CORE IDENTIFIERS ===
	// Các trường định danh cơ bản
	ID             string  `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"` // UUID của license
	OrganizationID string  `json:"organization_id" gorm:"type:uuid;not null"`                 // Organization sở hữu license
	ProductID      string  `json:"product_id" gorm:"type:uuid;not null"`                      // Product mà license thuộc về
	PolicyID       string  `json:"policy_id" gorm:"type:uuid;not null"`                       // Policy chứa rules cho license
	Key            string  `json:"key" gorm:"size:255;not null"`                              // License key (unique identifier)
	Name           *string `json:"name,omitempty" gorm:"size:255"`                            // Tên license do user đặt (optional)

	// === LICENSE OWNERSHIP ===
	// Ownership polymorphic - license có thể thuộc về user hoặc organization
	OwnerType string `json:"owner_type" gorm:"size:50;not null;check:owner_type IN ('user', 'organization')"` // Loại owner (user/organization)
	OwnerID   string `json:"owner_id" gorm:"type:uuid;not null"`                                              // ID của owner

	// === LICENSE STATE ===
	// Trạng thái và lifecycle của license
	Status    string `json:"status" gorm:"size:50;default:'active';check:status IN ('active', 'expired', 'suspended', 'banned')"` // Trạng thái license
	Suspended bool   `json:"suspended" gorm:"default:false"`                                                                      // License có bị suspend không
	Protected bool   `json:"protected" gorm:"default:false"`                                                                      // License có được bảo vệ không

	// === USAGE TRACKING ===
	// Theo dõi việc sử dụng license
	Uses      int        `json:"uses" gorm:"default:0"` // Số lần đã sử dụng license
	ExpiresAt *time.Time `json:"expires_at,omitempty"`  // Thời điểm hết hạn (từ policy duration)
	LastUsed  *time.Time `json:"last_used,omitempty"`   // Lần cuối sử dụng license

	// === POLICY OVERRIDES ===
	// Cho phép override policy limits per license (flexibility)
	MaxUsesOverride      *int `json:"max_uses_override,omitempty"`      // Override số lần sử dụng tối đa
	MaxMachinesOverride  *int `json:"max_machines_override,omitempty"`  // Override số máy tối đa
	MaxCoresOverride     *int `json:"max_cores_override,omitempty"`     // Override số cores tối đa
	MaxUsersOverride     *int `json:"max_users_override,omitempty"`     // Override số users tối đa
	MaxProcessesOverride *int `json:"max_processes_override,omitempty"` // Override số processes tối đa

	// === CACHED COUNTS ===
	// Cache các counts để performance (tránh expensive queries)
	MachinesCount     int `json:"machines_count" gorm:"default:0"`      // Số machines hiện tại
	MachinesCoreCount int `json:"machines_core_count" gorm:"default:0"` // Tổng số cores đang sử dụng
	LicenseUsersCount int `json:"license_users_count" gorm:"default:0"` // Số users đang sử dụng

	// === HEARTBEAT & VALIDATION TRACKING ===
	// Theo dõi check-in, validation và checkout activities
	LastCheckInAt         *time.Time `json:"last_check_in_at"`        // Lần cuối check-in
	LastValidatedAt       *time.Time `json:"last_validated_at"`       // Lần cuối validate license
	LastValidatedChecksum *string    `json:"last_validated_checksum"` // Checksum của lần validate cuối
	LastValidatedVersion  *string    `json:"last_validated_version"`  // Version của lần validate cuối
	LastCheckOutAt        *time.Time `json:"last_check_out_at"`       // Lần cuối check-out (floating licenses)

	// === EVENT TRACKING ===
	// Theo dõi các events đã gửi để tránh spam notifications
	LastExpirationEventSentAt   *time.Time `json:"last_expiration_event_sent_at"`    // Lần cuối gửi expiration event
	LastCheckInEventSentAt      *time.Time `json:"last_check_in_event_sent_at"`      // Lần cuối gửi check-in event
	LastExpiringSoonEventSentAt *time.Time `json:"last_expiring_soon_event_sent_at"` // Lần cuối gửi expiring soon event
	LastCheckInSoonEventSentAt  *time.Time `json:"last_check_in_soon_event_sent_at"` // Lần cuối gửi check-in soon event

	// === FLEXIBLE DATA ===
	// Custom metadata key-value cho license
	Metadata Metadata `json:"metadata" gorm:"type:jsonb;default:'{}'"` // Custom metadata JSONB

	// === AUDIT FIELDS ===
	// Các trường audit chuẩn cho tracking changes
	CreatedAt time.Time      `json:"created_at" gorm:"not null"`        // Thời điểm tạo license
	UpdatedAt time.Time      `json:"updated_at" gorm:"not null"`        // Thời điểm cập nhật cuối
	DeletedAt gorm.DeletedAt `json:"deleted_at,omitempty" gorm:"index"` // Soft delete timestamp

	// === RELATIONSHIPS ===
	// Các mối quan hệ với entities khác (lazy loading)
	Organization Organization `json:"organization,omitempty" gorm:"foreignKey:OrganizationID"` // Organization sở hữu license
	Product      Product      `json:"product,omitempty" gorm:"foreignKey:ProductID"`           // Product mà license thuộc về
	Policy       Policy       `json:"policy,omitempty" gorm:"foreignKey:PolicyID"`             // Policy chứa rules cho license
	Machines     []Machine    `json:"machines,omitempty" gorm:"foreignKey:LicenseID"`          // Danh sách machines sử dụng license
}

// TableName override tên table cho GORM
// Đảm bảo GORM sử dụng đúng tên table "licenses" trong database
func (License) TableName() string {
	return "licenses"
}

// License statuses
const (
	LicenseStatusActive    = "active"
	LicenseStatusExpired   = "expired"
	LicenseStatusSuspended = "suspended"
	LicenseStatusBanned    = "banned"
)

// License owner types
const (
	LicenseOwnerTypeUser         = "user"
	LicenseOwnerTypeOrganization = "organization"
)

// IsOwnedByUser checks if the license is owned by a user
func (l *License) IsOwnedByUser() bool {
	return l.OwnerType == LicenseOwnerTypeUser
}

// IsOwnedByOrganization checks if the license is owned by an organization
func (l *License) IsOwnedByOrganization() bool {
	return l.OwnerType == LicenseOwnerTypeOrganization
}

// IsExpired checks if the license has expired
func (l *License) IsExpired() bool {
	if l.ExpiresAt == nil {
		return false
	}
	return time.Now().After(*l.ExpiresAt) || l.Status == LicenseStatusExpired
}

// IsActive checks if the license is active and not expired or suspended
func (l *License) IsActive() bool {
	return l.Status == LicenseStatusActive && !l.Suspended && !l.IsExpired()
}

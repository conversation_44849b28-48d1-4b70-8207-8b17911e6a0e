package entities

import (
	"time"
)

// LicenseFile represents a signed license certificate (maps to Ruby LicenseFile)
type LicenseFile struct {
	ID             string    `json:"id"`
	OrganizationID string    `json:"organization_id"`
	LicenseID      string    `json:"license_id"`
	Certificate    string    `json:"certificate"`
	IssuedAt       time.Time `json:"issued_at"`
	ExpiresAt      time.Time `json:"expires_at"`
	TTL            int       `json:"ttl"`
	Includes       []string  `json:"includes"`
	Algorithm      string    `json:"algorithm"`
	CreatedAt      time.Time `json:"created_at"`
}

// NewLicenseFile creates a new license file
func NewLicenseFile(
	organizationID string,
	environmentID *string, // Kept for compatibility but unused
	licenseID string,
	certificate string,
	issuedAt time.Time,
	expiresAt *time.Time,
	ttl *int,
	includes []string,
	algorithm string,
) *LicenseFile {
	now := time.Now()
	
	return &LicenseFile{
		ID:             licenseID + "-" + now.Format("20060102150405"), // Simple ID generation
		OrganizationID: organizationID,
		LicenseID:      licenseID,
		Certificate:    certificate,
		IssuedAt:       issuedAt,
		ExpiresAt:      *expiresAt,
		TTL:            *ttl,
		Includes:       includes,
		Algorithm:      algorithm,
		CreatedAt:      now,
	}
}

// IsExpired checks if the license file has expired
func (lf *LicenseFile) IsExpired() bool {
	return time.Now().After(lf.ExpiresAt)
}

// IsValid checks if the license file is still valid
func (lf *LicenseFile) IsValid() bool {
	return !lf.IsExpired()
}

// GetTTLRemaining returns the remaining TTL in seconds
func (lf *LicenseFile) GetTTLRemaining() int {
	if lf.IsExpired() {
		return 0
	}
	remaining := time.Until(lf.ExpiresAt).Seconds()
	if remaining < 0 {
		return 0
	}
	return int(remaining)
}
package entities

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
	"time"

	"gorm.io/gorm"
)

// Machine đại diện cho một máy tính/thiết bị có thể chạy licenses
// Trong keygen-api, machine là đơn vị cơ bản để track việc sử dụng license
// Machine có fingerprint để nhận dạng duy nhất và heartbeat để monitor trạng thái
// Mapping từ Ruby Machine model với đầy đủ relationships và business logic
type Machine struct {
	// === CORE IDENTIFIERS ===
	// Các trường định danh cơ bản
	ID        string  `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"` // UUID của machine
	LicenseID string  `json:"license_id" gorm:"type:uuid;not null"`                      // License mà machine này đang sử dụng
	PolicyID  string  `json:"policy_id" gorm:"type:uuid;not null"`                       // Policy áp dụng cho machine (từ license)
	OwnerID   *string `json:"owner_id,omitempty" gorm:"type:uuid"`                       // User sở hữu machine (optional)

	// === MACHINE IDENTIFICATION ===
	// Thông tin nhận dạng machine
	Fingerprint string  `json:"fingerprint" gorm:"size:255;not null"` // Dấu vân tay duy nhất của machine (từ hardware)
	Name        *string `json:"name,omitempty" gorm:"size:255"`       // Tên machine do user đặt
	Hostname    *string `json:"hostname,omitempty" gorm:"size:255"`   // Hostname của machine
	Platform    *string `json:"platform,omitempty" gorm:"size:255"`   // Platform/OS (Windows, macOS, Linux, etc.)

	// === NETWORK INFORMATION ===
	// Thông tin mạng
	IP *string `json:"ip,omitempty" gorm:"size:45"` // Địa chỉ IP hiện tại (IPv4/IPv6)

	// === MACHINE STATE ===
	// Trạng thái và vòng đời của machine
	Status        string     `json:"status" gorm:"size:50;default:'active';check:status IN ('active', 'inactive')"` // Trạng thái: active/inactive
	ActivatedAt   *time.Time `json:"activated_at,omitempty"`                                                        // Thời điểm machine được kích hoạt
	DeactivatedAt *time.Time `json:"deactivated_at,omitempty"`                                                      // Thời điểm machine bị vô hiệu hóa
	LastSeen      *time.Time `json:"last_seen,omitempty"`                                                           // Lần cuối thấy machine hoạt động

	// === HARDWARE TRACKING ===
	// Thông số kỹ thuật phần cứng
	Cores int `json:"cores" gorm:"default:0"` // Số CPU cores (dùng cho core-based licensing)

	// === COMPONENT FINGERPRINTING ===
	// Hardware components cho fingerprinting và matching
	Components MachineComponents `json:"components" gorm:"type:jsonb;default:'{}'"` // Map các hardware components

	// === HEARTBEAT TRACKING ===
	// Hệ thống theo dõi machine còn hoạt động không
	LastHeartbeatAt      *time.Time `json:"last_heartbeat_at"`                       // Lần heartbeat cuối cùng
	NextHeartbeatAt      *time.Time `json:"next_heartbeat_at"`                       // Thời điểm heartbeat tiếp theo phải gửi
	LastDeathEventSentAt *time.Time `json:"last_death_event_sent_at"`                // Lần cuối gửi death event
	HeartbeatJID         *string    `json:"heartbeat_jid,omitempty" gorm:"size:255"` // Job ID cho heartbeat processing

	// === CHECK-OUT TRACKING ===
	// Theo dõi check-out cho floating licenses
	LastCheckOutAt *time.Time `json:"last_check_out_at"` // Lần cuối check-out license

	// === FLEXIBLE DATA ===
	// Dữ liệu linh hoạt
	Metadata Metadata `json:"metadata" gorm:"type:jsonb;default:'{}'"` // Custom metadata key-value

	// === AUDIT FIELDS ===
	// Các trường audit chuẩn cho tracking changes
	CreatedAt time.Time      `json:"created_at" gorm:"not null"`        // Thời điểm tạo machine
	UpdatedAt time.Time      `json:"updated_at" gorm:"not null"`        // Thời điểm cập nhật cuối
	DeletedAt gorm.DeletedAt `json:"deleted_at,omitempty" gorm:"index"` // Soft delete timestamp

	// === RELATIONSHIPS ===
	// Các mối quan hệ với entities khác (lazy loading)
	License License `json:"license,omitempty" gorm:"foreignKey:LicenseID"` // License mà machine đang sử dụng
	Policy  Policy  `json:"policy,omitempty" gorm:"foreignKey:PolicyID"`   // Policy chứa rules cho machine
	Owner   *User   `json:"owner,omitempty" gorm:"foreignKey:OwnerID"`     // User sở hữu machine (optional)
}

// TableName override tên table cho GORM
// Đảm bảo GORM sử dụng đúng tên table "machines" trong database
func (Machine) TableName() string {
	return "machines"
}

// === MACHINE STATUS CONSTANTS ===
// Các trạng thái cơ bản của machine
const (
	MachineStatusActive   = "active"   // Machine đang hoạt động và có thể sử dụng license
	MachineStatusInactive = "inactive" // Machine bị vô hiệu hóa, không thể sử dụng license
)

// === HEARTBEAT STATUS CONSTANTS ===
// Các trạng thái heartbeat của machine (mapping từ Ruby keygen-api)
const (
	HeartbeatStatusNotStarted  = "NOT_STARTED" // Chưa bao giờ gửi heartbeat
	HeartbeatStatusAlive       = "ALIVE"       // Heartbeat còn trong thời hạn
	HeartbeatStatusDead        = "DEAD"        // Heartbeat đã quá hạn
	HeartbeatStatusResurrected = "RESURRECTED" // Được hồi sinh sau khi chết
)

// === HEARTBEAT CONFIGURATION CONSTANTS ===
// Các cấu hình mặc định cho heartbeat system
const (
	DefaultHeartbeatTTL = 10 * time.Minute // 10 phút - thời gian mặc định giữa các heartbeat
	HeartbeatDrift      = 30 * time.Second // 30 giây - thời gian drift cho phép
)

// === BASIC MACHINE STATUS METHODS ===

// IsActive kiểm tra machine có đang active không
func (m *Machine) IsActive() bool {
	return m.Status == MachineStatusActive
}

// IsInactive kiểm tra machine có inactive không
func (m *Machine) IsInactive() bool {
	return m.Status == MachineStatusInactive
}

// Activate kích hoạt machine
func (m *Machine) Activate() {
	m.Status = MachineStatusActive
	if m.ActivatedAt == nil {
		now := time.Now()
		m.ActivatedAt = &now
	}
	m.DeactivatedAt = nil
}

// Deactivate vô hiệu hóa machine
func (m *Machine) Deactivate() {
	m.Status = MachineStatusInactive
	now := time.Now()
	m.DeactivatedAt = &now
}

// === HEARTBEAT MANAGEMENT METHODS ===

// GetHeartbeatDuration trả về heartbeat duration từ policy hoặc default
func (m *Machine) GetHeartbeatDuration() time.Duration {
	if m.Policy.HeartbeatDuration != nil {
		return time.Duration(*m.Policy.HeartbeatDuration) * time.Second
	}
	return DefaultHeartbeatTTL
}

// RequiresHeartbeat kiểm tra machine có cần heartbeat không
func (m *Machine) RequiresHeartbeat() bool {
	// Nếu policy require heartbeat hoặc đã từng có heartbeat
	return m.Policy.RequiresHeartbeat() || m.LastHeartbeatAt != nil
}

// UpdateHeartbeat cập nhật heartbeat timestamp
func (m *Machine) UpdateHeartbeat() {
	now := time.Now()
	m.LastHeartbeatAt = &now
	m.LastSeen = &now

	// Tính next heartbeat time
	nextHeartbeat := now.Add(m.GetHeartbeatDuration())
	m.NextHeartbeatAt = &nextHeartbeat
}

// GetHeartbeatStatus tính toán heartbeat status hiện tại
func (m *Machine) GetHeartbeatStatus() string {
	now := time.Now()

	// Nếu không require heartbeat và chưa bao giờ heartbeat
	if !m.RequiresHeartbeat() && m.LastHeartbeatAt == nil {
		return HeartbeatStatusNotStarted
	}

	// Nếu chưa có heartbeat lần nào nhưng machine mới tạo (trong heartbeat duration)
	if m.LastHeartbeatAt == nil && now.Sub(m.CreatedAt) < m.GetHeartbeatDuration() {
		return HeartbeatStatusNotStarted
	}

	// Nếu có next heartbeat time và chưa quá hạn
	if m.NextHeartbeatAt != nil && now.Before(*m.NextHeartbeatAt) {
		return HeartbeatStatusAlive
	}

	// Nếu quá hạn heartbeat
	return HeartbeatStatusDead
}

// IsHeartbeatAlive kiểm tra heartbeat có alive không
func (m *Machine) IsHeartbeatAlive() bool {
	status := m.GetHeartbeatStatus()
	return status == HeartbeatStatusAlive || status == HeartbeatStatusResurrected
}

// IsHeartbeatDead kiểm tra heartbeat có dead không
func (m *Machine) IsHeartbeatDead() bool {
	return m.GetHeartbeatStatus() == HeartbeatStatusDead
}

// IsHeartbeatNotStarted kiểm tra heartbeat chưa bắt đầu
func (m *Machine) IsHeartbeatNotStarted() bool {
	return m.GetHeartbeatStatus() == HeartbeatStatusNotStarted
}

// IsHeartbeatOK kiểm tra heartbeat có OK không (not started hoặc alive)
func (m *Machine) IsHeartbeatOK() bool {
	return m.IsHeartbeatNotStarted() || m.IsHeartbeatAlive()
}

// IsHeartbeatDue kiểm tra heartbeat có đến hạn không
func (m *Machine) IsHeartbeatDue() bool {
	if m.NextHeartbeatAt == nil {
		return false
	}
	return time.Now().After(*m.NextHeartbeatAt)
}

// CanResurrect kiểm tra machine có thể resurrect không
func (m *Machine) CanResurrect() bool {
	if !m.IsHeartbeatDead() {
		return false
	}

	// Nếu policy cho phép always resurrect
	if m.Policy.AlwaysResurrectDead() {
		return true
	}

	// Nếu trong thời gian lazarus TTL
	if m.NextHeartbeatAt != nil {
		lazarusTTL := time.Duration(m.Policy.GetLazarusTTL()) * time.Second
		return time.Now().Before(m.NextHeartbeatAt.Add(lazarusTTL))
	}

	return false
}

// === MACHINE COMPONENTS TYPE ===
// MachineComponents đại diện cho các hardware components của machine
// Dùng để fingerprinting và component matching theo policy strategies
// Lưu trữ dưới dạng JSONB trong database để flexibility và performance
type MachineComponents map[string]string

// === JSONB DATABASE INTERFACE ===

// Scan implement sql.Scanner interface để đọc JSONB từ database
// GORM sẽ gọi method này khi load data từ database
func (mc *MachineComponents) Scan(value interface{}) error {
	// Nếu value là nil, khởi tạo empty map
	if value == nil {
		*mc = make(map[string]string)
		return nil
	}

	// Convert value thành byte array
	var data []byte
	switch v := value.(type) {
	case []byte:
		data = v
	case string:
		data = []byte(v)
	default:
		return fmt.Errorf("cannot scan %T into MachineComponents", value)
	}

	// Nếu data rỗng, khởi tạo empty map
	if len(data) == 0 {
		*mc = make(map[string]string)
		return nil
	}

	// Unmarshal JSON data vào map
	return json.Unmarshal(data, mc)
}

// Value implement driver.Valuer interface để lưu JSONB vào database
// GORM sẽ gọi method này khi save data vào database
func (mc MachineComponents) Value() (driver.Value, error) {
	// Nếu map là nil, trả về empty JSON object
	if mc == nil {
		return "{}", nil
	}
	// Marshal map thành JSON string
	return json.Marshal(mc)
}

// === COMPONENT KEY CONSTANTS ===
// Các key chuẩn cho hardware components (mapping từ Ruby keygen-api)
const (
	ComponentCPUID         = "cpu_id"         // CPU identifier (serial number, model)
	ComponentMotherboardID = "motherboard_id" // Motherboard identifier
	ComponentDiskID        = "disk_id"        // Primary disk identifier (serial number)
	ComponentMACAddress    = "mac_address"    // Primary network interface MAC address
	ComponentBIOSID        = "bios_id"        // BIOS/UEFI identifier
	ComponentRAMSize       = "ram_size"       // Total RAM size (for capacity-based licensing)
	ComponentGPUID         = "gpu_id"         // GPU identifier (for GPU-based licensing)
)

// === COMPONENT UTILITY METHODS ===

// GetFingerprint tạo composite fingerprint từ các components quan trọng
// Sử dụng concatenation của các key components để tạo unique identifier
// Thứ tự ưu tiên: CPU -> Motherboard -> Disk -> MAC Address
func (mc MachineComponents) GetFingerprint() string {
	// Concatenation đơn giản của các components quan trọng
	fingerprint := ""
	// Các components theo thứ tự ưu tiên cho fingerprinting
	criticalKeys := []string{ComponentCPUID, ComponentMotherboardID, ComponentDiskID, ComponentMACAddress}

	for _, key := range criticalKeys {
		if value, exists := mc[key]; exists {
			fingerprint += value + "|" // Sử dụng "|" làm separator
		}
	}
	return fingerprint
}

// HasComponent kiểm tra một component cụ thể có tồn tại không
// Dùng để validate component trước khi access value
func (mc MachineComponents) HasComponent(key string) bool {
	_, exists := mc[key]
	return exists
}

// GetComponent lấy giá trị component một cách an toàn
// Trả về empty string nếu component không tồn tại thay vì panic
func (mc MachineComponents) GetComponent(key string) string {
	if value, exists := mc[key]; exists {
		return value
	}
	return "" // Safe default value
}

// === UNIQUENESS STRATEGY METHODS ===
// Các methods để check machine uniqueness theo policy

// UniquePerAccount kiểm tra machine có unique per account không
func (m *Machine) UniquePerAccount() bool {
	return m.Policy.MachineUniquePerAccount()
}

// UniquePerProduct kiểm tra machine có unique per product không
func (m *Machine) UniquePerProduct() bool {
	return m.Policy.MachineUniquePerProduct()
}

// UniquePerPolicy kiểm tra machine có unique per policy không
func (m *Machine) UniquePerPolicy() bool {
	return m.Policy.MachineUniquePerPolicy()
}

// UniquePerLicense kiểm tra machine có unique per license không (default)
func (m *Machine) UniquePerLicense() bool {
	return m.Policy.MachineUniquePerLicense()
}

// === LEASING STRATEGY METHODS ===
// Các methods để check machine leasing strategy

// LeasePerLicense kiểm tra machine có lease per license không
func (m *Machine) LeasePerLicense() bool {
	return m.Policy.MachineLeasePerLicense()
}

// LeasePerUser kiểm tra machine có lease per user không
func (m *Machine) LeasePerUser() bool {
	return m.Policy.MachineLeasePerUser()
}

// === COMPONENT MATCHING METHODS ===
// Các methods để xử lý component matching

// MatchesComponents kiểm tra machine có match với components không
func (m *Machine) MatchesComponents(otherComponents MachineComponents) bool {
	if len(m.Components) == 0 || len(otherComponents) == 0 {
		return false
	}

	// Đếm số components match
	matchCount := 0
	totalComponents := len(m.Components)

	for key, value := range m.Components {
		if otherValue, exists := otherComponents[key]; exists && value == otherValue {
			matchCount++
		}
	}

	// Áp dụng matching strategy từ policy
	switch {
	case m.Policy.ComponentMatchAny():
		return matchCount >= 1
	case m.Policy.ComponentMatchTwo():
		return matchCount >= 2
	case m.Policy.ComponentMatchMost():
		return matchCount >= (totalComponents+1)/2 // Majority
	case m.Policy.ComponentMatchAll():
		return matchCount == totalComponents
	default:
		return matchCount >= 1 // Default to MATCH_ANY
	}
}

// GetCriticalComponents trả về các components quan trọng cho fingerprinting
func (m *Machine) GetCriticalComponents() MachineComponents {
	critical := make(MachineComponents)

	// Các components quan trọng theo thứ tự ưu tiên
	criticalKeys := []string{
		ComponentCPUID,
		ComponentMotherboardID,
		ComponentDiskID,
		ComponentMACAddress,
		ComponentBIOSID,
	}

	for _, key := range criticalKeys {
		if value := m.Components.GetComponent(key); value != "" {
			critical[key] = value
		}
	}

	return critical
}

// UpdateComponents cập nhật components và regenerate fingerprint
func (m *Machine) UpdateComponents(newComponents MachineComponents) {
	m.Components = newComponents

	// Regenerate fingerprint từ components
	if fingerprint := newComponents.GetFingerprint(); fingerprint != "" {
		m.Fingerprint = fingerprint
	}
}

// === VALIDATION METHODS ===

// ValidateMachine validate machine theo business rules
func (m *Machine) ValidateMachine() []string {
	var errors []string

	// Fingerprint là bắt buộc
	if m.Fingerprint == "" {
		errors = append(errors, "fingerprint is required")
	}

	// Cores phải >= 1
	if m.Cores < 1 {
		errors = append(errors, "cores must be greater than or equal to 1")
	}

	// Cores không được vượt quá max int32
	if m.Cores > 2147483647 {
		errors = append(errors, "cores must be less than or equal to 2147483647")
	}

	return errors
}

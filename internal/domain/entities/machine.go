package entities

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
	"time"

	"gorm.io/gorm"
)

type Machine struct {
	ID        string  `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	LicenseID string  `json:"license_id" gorm:"type:uuid;not null"`
	PolicyID  string  `json:"policy_id" gorm:"type:uuid;not null"`
	OwnerID   *string `json:"owner_id,omitempty" gorm:"type:uuid"` // User who owns this machine

	// Machine identification
	Fingerprint string  `json:"fingerprint" gorm:"size:255;not null"`
	Name        *string `json:"name,omitempty" gorm:"size:255"`
	Hostname    *string `json:"hostname,omitempty" gorm:"size:255"`
	Platform    *string `json:"platform,omitempty" gorm:"size:255"`

	// Network information
	IP *string `json:"ip,omitempty" gorm:"size:45"` // IPv4/IPv6

	// Machine state
	Status        string     `json:"status" gorm:"size:50;default:'active';check:status IN ('active', 'inactive')"`
	ActivatedAt   *time.Time `json:"activated_at,omitempty"`
	DeactivatedAt *time.Time `json:"deactivated_at,omitempty"`
	LastSeen      *time.Time `json:"last_seen,omitempty"`

	// Hardware tracking
	Cores int `json:"cores" gorm:"default:0"`

	// Component fingerprinting
	Components MachineComponents `json:"components" gorm:"type:jsonb;default:'{}'"`

	// Heartbeat tracking
	LastHeartbeatAt      *time.Time `json:"last_heartbeat_at"`
	NextHeartbeatAt      *time.Time `json:"next_heartbeat_at"`
	LastDeathEventSentAt *time.Time `json:"last_death_event_sent_at"`
	HeartbeatJID         *string    `json:"heartbeat_jid,omitempty" gorm:"size:255"` // Job ID for heartbeat processing

	// Check-out tracking (for floating licenses)
	LastCheckOutAt *time.Time `json:"last_check_out_at"`

	Metadata Metadata `json:"metadata" gorm:"type:jsonb;default:'{}'"`

	// Timestamps
	CreatedAt time.Time      `json:"created_at" gorm:"not null"`
	UpdatedAt time.Time      `json:"updated_at" gorm:"not null"`
	DeletedAt gorm.DeletedAt `json:"deleted_at,omitempty" gorm:"index"`

	// Relations
	License License `json:"license,omitempty" gorm:"foreignKey:LicenseID"`
	Policy  Policy  `json:"policy,omitempty" gorm:"foreignKey:PolicyID"`
	Owner   *User   `json:"owner,omitempty" gorm:"foreignKey:OwnerID"`
}

// TableName overrides the table name used by GORM
func (Machine) TableName() string {
	return "machines"
}

// Machine statuses
const (
	MachineStatusActive   = "active"
	MachineStatusInactive = "inactive"
)

// Heartbeat statuses
const (
	HeartbeatStatusNotStarted  = "NOT_STARTED"
	HeartbeatStatusAlive       = "ALIVE"
	HeartbeatStatusDead        = "DEAD"
	HeartbeatStatusResurrected = "RESURRECTED"
)

// Default heartbeat settings
const (
	DefaultHeartbeatTTL = 10 * time.Minute // 10 minutes default
	HeartbeatDrift      = 30 * time.Second // 30 seconds drift allowance
)

// === BASIC MACHINE STATUS METHODS ===

// IsActive kiểm tra machine có đang active không
func (m *Machine) IsActive() bool {
	return m.Status == MachineStatusActive
}

// IsInactive kiểm tra machine có inactive không
func (m *Machine) IsInactive() bool {
	return m.Status == MachineStatusInactive
}

// Activate kích hoạt machine
func (m *Machine) Activate() {
	m.Status = MachineStatusActive
	if m.ActivatedAt == nil {
		now := time.Now()
		m.ActivatedAt = &now
	}
	m.DeactivatedAt = nil
}

// Deactivate vô hiệu hóa machine
func (m *Machine) Deactivate() {
	m.Status = MachineStatusInactive
	now := time.Now()
	m.DeactivatedAt = &now
}

// === HEARTBEAT MANAGEMENT METHODS ===

// GetHeartbeatDuration trả về heartbeat duration từ policy hoặc default
func (m *Machine) GetHeartbeatDuration() time.Duration {
	if m.Policy.HeartbeatDuration != nil {
		return time.Duration(*m.Policy.HeartbeatDuration) * time.Second
	}
	return DefaultHeartbeatTTL
}

// RequiresHeartbeat kiểm tra machine có cần heartbeat không
func (m *Machine) RequiresHeartbeat() bool {
	// Nếu policy require heartbeat hoặc đã từng có heartbeat
	return m.Policy.RequiresHeartbeat() || m.LastHeartbeatAt != nil
}

// UpdateHeartbeat cập nhật heartbeat timestamp
func (m *Machine) UpdateHeartbeat() {
	now := time.Now()
	m.LastHeartbeatAt = &now
	m.LastSeen = &now

	// Tính next heartbeat time
	nextHeartbeat := now.Add(m.GetHeartbeatDuration())
	m.NextHeartbeatAt = &nextHeartbeat
}

// GetHeartbeatStatus tính toán heartbeat status hiện tại
func (m *Machine) GetHeartbeatStatus() string {
	now := time.Now()

	// Nếu không require heartbeat và chưa bao giờ heartbeat
	if !m.RequiresHeartbeat() && m.LastHeartbeatAt == nil {
		return HeartbeatStatusNotStarted
	}

	// Nếu chưa có heartbeat lần nào nhưng machine mới tạo (trong heartbeat duration)
	if m.LastHeartbeatAt == nil && now.Sub(m.CreatedAt) < m.GetHeartbeatDuration() {
		return HeartbeatStatusNotStarted
	}

	// Nếu có next heartbeat time và chưa quá hạn
	if m.NextHeartbeatAt != nil && now.Before(*m.NextHeartbeatAt) {
		return HeartbeatStatusAlive
	}

	// Nếu quá hạn heartbeat
	return HeartbeatStatusDead
}

// IsHeartbeatAlive kiểm tra heartbeat có alive không
func (m *Machine) IsHeartbeatAlive() bool {
	status := m.GetHeartbeatStatus()
	return status == HeartbeatStatusAlive || status == HeartbeatStatusResurrected
}

// IsHeartbeatDead kiểm tra heartbeat có dead không
func (m *Machine) IsHeartbeatDead() bool {
	return m.GetHeartbeatStatus() == HeartbeatStatusDead
}

// IsHeartbeatNotStarted kiểm tra heartbeat chưa bắt đầu
func (m *Machine) IsHeartbeatNotStarted() bool {
	return m.GetHeartbeatStatus() == HeartbeatStatusNotStarted
}

// IsHeartbeatOK kiểm tra heartbeat có OK không (not started hoặc alive)
func (m *Machine) IsHeartbeatOK() bool {
	return m.IsHeartbeatNotStarted() || m.IsHeartbeatAlive()
}

// IsHeartbeatDue kiểm tra heartbeat có đến hạn không
func (m *Machine) IsHeartbeatDue() bool {
	if m.NextHeartbeatAt == nil {
		return false
	}
	return time.Now().After(*m.NextHeartbeatAt)
}

// CanResurrect kiểm tra machine có thể resurrect không
func (m *Machine) CanResurrect() bool {
	if !m.IsHeartbeatDead() {
		return false
	}

	// Nếu policy cho phép always resurrect
	if m.Policy.AlwaysResurrectDead() {
		return true
	}

	// Nếu trong thời gian lazarus TTL
	if m.NextHeartbeatAt != nil {
		lazarusTTL := time.Duration(m.Policy.GetLazarusTTL()) * time.Second
		return time.Now().Before(m.NextHeartbeatAt.Add(lazarusTTL))
	}

	return false
}

// MachineComponents represents machine hardware components for fingerprinting
type MachineComponents map[string]string

// Scan implements the sql.Scanner interface for JSONB scanning
func (mc *MachineComponents) Scan(value interface{}) error {
	if value == nil {
		*mc = make(map[string]string)
		return nil
	}

	var data []byte
	switch v := value.(type) {
	case []byte:
		data = v
	case string:
		data = []byte(v)
	default:
		return fmt.Errorf("cannot scan %T into MachineComponents", value)
	}

	if len(data) == 0 {
		*mc = make(map[string]string)
		return nil
	}

	return json.Unmarshal(data, mc)
}

// Value implements the driver.Valuer interface for JSONB storage
func (mc MachineComponents) Value() (driver.Value, error) {
	if mc == nil {
		return "{}", nil
	}
	return json.Marshal(mc)
}

// Common component keys for fingerprinting
const (
	ComponentCPUID         = "cpu_id"
	ComponentMotherboardID = "motherboard_id"
	ComponentDiskID        = "disk_id"
	ComponentMACAddress    = "mac_address"
	ComponentBIOSID        = "bios_id"
	ComponentRAMSize       = "ram_size"
	ComponentGPUID         = "gpu_id"
)

// GetFingerprint generates a composite fingerprint from components
func (mc MachineComponents) GetFingerprint() string {
	// Simple concatenation of key components for basic fingerprinting
	fingerprint := ""
	for _, key := range []string{ComponentCPUID, ComponentMotherboardID, ComponentDiskID, ComponentMACAddress} {
		if value, exists := mc[key]; exists {
			fingerprint += value + "|"
		}
	}
	return fingerprint
}

// HasComponent checks if a specific component exists
func (mc MachineComponents) HasComponent(key string) bool {
	_, exists := mc[key]
	return exists
}

// GetComponent gets a component value safely
func (mc MachineComponents) GetComponent(key string) string {
	if value, exists := mc[key]; exists {
		return value
	}
	return ""
}

// === UNIQUENESS STRATEGY METHODS ===
// Các methods để check machine uniqueness theo policy

// UniquePerAccount kiểm tra machine có unique per account không
func (m *Machine) UniquePerAccount() bool {
	return m.Policy.MachineUniquePerAccount()
}

// UniquePerProduct kiểm tra machine có unique per product không
func (m *Machine) UniquePerProduct() bool {
	return m.Policy.MachineUniquePerProduct()
}

// UniquePerPolicy kiểm tra machine có unique per policy không
func (m *Machine) UniquePerPolicy() bool {
	return m.Policy.MachineUniquePerPolicy()
}

// UniquePerLicense kiểm tra machine có unique per license không (default)
func (m *Machine) UniquePerLicense() bool {
	return m.Policy.MachineUniquePerLicense()
}

// === LEASING STRATEGY METHODS ===
// Các methods để check machine leasing strategy

// LeasePerLicense kiểm tra machine có lease per license không
func (m *Machine) LeasePerLicense() bool {
	return m.Policy.MachineLeasePerLicense()
}

// LeasePerUser kiểm tra machine có lease per user không
func (m *Machine) LeasePerUser() bool {
	return m.Policy.MachineLeasePerUser()
}

// === COMPONENT MATCHING METHODS ===
// Các methods để xử lý component matching

// MatchesComponents kiểm tra machine có match với components không
func (m *Machine) MatchesComponents(otherComponents MachineComponents) bool {
	if len(m.Components) == 0 || len(otherComponents) == 0 {
		return false
	}

	// Đếm số components match
	matchCount := 0
	totalComponents := len(m.Components)

	for key, value := range m.Components {
		if otherValue, exists := otherComponents[key]; exists && value == otherValue {
			matchCount++
		}
	}

	// Áp dụng matching strategy từ policy
	switch {
	case m.Policy.ComponentMatchAny():
		return matchCount >= 1
	case m.Policy.ComponentMatchTwo():
		return matchCount >= 2
	case m.Policy.ComponentMatchMost():
		return matchCount >= (totalComponents+1)/2 // Majority
	case m.Policy.ComponentMatchAll():
		return matchCount == totalComponents
	default:
		return matchCount >= 1 // Default to MATCH_ANY
	}
}

// GetCriticalComponents trả về các components quan trọng cho fingerprinting
func (m *Machine) GetCriticalComponents() MachineComponents {
	critical := make(MachineComponents)

	// Các components quan trọng theo thứ tự ưu tiên
	criticalKeys := []string{
		ComponentCPUID,
		ComponentMotherboardID,
		ComponentDiskID,
		ComponentMACAddress,
		ComponentBIOSID,
	}

	for _, key := range criticalKeys {
		if value := m.Components.GetComponent(key); value != "" {
			critical[key] = value
		}
	}

	return critical
}

// UpdateComponents cập nhật components và regenerate fingerprint
func (m *Machine) UpdateComponents(newComponents MachineComponents) {
	m.Components = newComponents

	// Regenerate fingerprint từ components
	if fingerprint := newComponents.GetFingerprint(); fingerprint != "" {
		m.Fingerprint = fingerprint
	}
}

// === VALIDATION METHODS ===

// ValidateMachine validate machine theo business rules
func (m *Machine) ValidateMachine() []string {
	var errors []string

	// Fingerprint là bắt buộc
	if m.Fingerprint == "" {
		errors = append(errors, "fingerprint is required")
	}

	// Cores phải >= 1
	if m.Cores < 1 {
		errors = append(errors, "cores must be greater than or equal to 1")
	}

	// Cores không được vượt quá max int32
	if m.Cores > 2147483647 {
		errors = append(errors, "cores must be less than or equal to 2147483647")
	}

	return errors
}

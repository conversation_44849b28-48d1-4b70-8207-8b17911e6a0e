package entities

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
	"time"

	"gorm.io/gorm"
)

type Machine struct {
	ID        string  `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	LicenseID string  `json:"license_id" gorm:"type:uuid;not null"`
	PolicyID  string  `json:"policy_id" gorm:"type:uuid;not null"`
	OwnerID   *string `json:"owner_id,omitempty" gorm:"type:uuid"` // User who owns this machine
	
	// Machine identification
	Fingerprint string  `json:"fingerprint" gorm:"size:255;not null"`
	Name        *string `json:"name,omitempty" gorm:"size:255"`
	Hostname    *string `json:"hostname,omitempty" gorm:"size:255"`
	Platform    *string `json:"platform,omitempty" gorm:"size:255"`
	
	// Network information
	IP *string `json:"ip,omitempty" gorm:"size:45"` // IPv4/IPv6

	// Machine state
	Status        string     `json:"status" gorm:"size:50;default:'active';check:status IN ('active', 'inactive')"`
	ActivatedAt   *time.Time `json:"activated_at,omitempty"`
	DeactivatedAt *time.Time `json:"deactivated_at,omitempty"`
	LastSeen      *time.Time `json:"last_seen,omitempty"`

	// Hardware tracking
	Cores int `json:"cores" gorm:"default:0"`
	
	// Component fingerprinting
	Components MachineComponents `json:"components" gorm:"type:jsonb;default:'{}'"`

	// Heartbeat tracking
	LastHeartbeatAt      *time.Time `json:"last_heartbeat_at"`
	NextHeartbeatAt      *time.Time `json:"next_heartbeat_at"`
	LastDeathEventSentAt *time.Time `json:"last_death_event_sent_at"`
	HeartbeatJID         *string    `json:"heartbeat_jid,omitempty" gorm:"size:255"` // Job ID for heartbeat processing

	// Check-out tracking (for floating licenses)
	LastCheckOutAt *time.Time `json:"last_check_out_at"`

	Metadata Metadata `json:"metadata" gorm:"type:jsonb;default:'{}'"`

	// Timestamps
	CreatedAt time.Time      `json:"created_at" gorm:"not null"`
	UpdatedAt time.Time      `json:"updated_at" gorm:"not null"`
	DeletedAt gorm.DeletedAt `json:"deleted_at,omitempty" gorm:"index"`

	// Relations
	License License `json:"license,omitempty" gorm:"foreignKey:LicenseID"`
	Policy  Policy  `json:"policy,omitempty" gorm:"foreignKey:PolicyID"`
	Owner   *User   `json:"owner,omitempty" gorm:"foreignKey:OwnerID"`
}

// TableName overrides the table name used by GORM
func (Machine) TableName() string {
	return "machines"
}

// Machine statuses
const (
	MachineStatusActive   = "active"
	MachineStatusInactive = "inactive"
)

// IsActive checks if the machine is active
func (m *Machine) IsActive() bool {
	return m.Status == MachineStatusActive
}

// IsHeartbeatDue checks if the machine's heartbeat is due
func (m *Machine) IsHeartbeatDue() bool {
	if m.NextHeartbeatAt == nil {
		return false
	}
	return time.Now().After(*m.NextHeartbeatAt)
}

// MachineComponents represents machine hardware components for fingerprinting
type MachineComponents map[string]string

// Scan implements the sql.Scanner interface for JSONB scanning
func (mc *MachineComponents) Scan(value interface{}) error {
	if value == nil {
		*mc = make(map[string]string)
		return nil
	}

	var data []byte
	switch v := value.(type) {
	case []byte:
		data = v
	case string:
		data = []byte(v)
	default:
		return fmt.Errorf("cannot scan %T into MachineComponents", value)
	}

	if len(data) == 0 {
		*mc = make(map[string]string)
		return nil
	}

	return json.Unmarshal(data, mc)
}

// Value implements the driver.Valuer interface for JSONB storage
func (mc MachineComponents) Value() (driver.Value, error) {
	if mc == nil {
		return "{}", nil
	}
	return json.Marshal(mc)
}

// Common component keys for fingerprinting
const (
	ComponentCPUID         = "cpu_id"
	ComponentMotherboardID = "motherboard_id" 
	ComponentDiskID        = "disk_id"
	ComponentMACAddress    = "mac_address"
	ComponentBIOSID        = "bios_id"
	ComponentRAMSize       = "ram_size"
	ComponentGPUID         = "gpu_id"
)

// GetFingerprint generates a composite fingerprint from components
func (mc MachineComponents) GetFingerprint() string {
	// Simple concatenation of key components for basic fingerprinting
	fingerprint := ""
	for _, key := range []string{ComponentCPUID, ComponentMotherboardID, ComponentDiskID, ComponentMACAddress} {
		if value, exists := mc[key]; exists {
			fingerprint += value + "|"
		}
	}
	return fingerprint
}

// HasComponent checks if a specific component exists
func (mc MachineComponents) HasComponent(key string) bool {
	_, exists := mc[key]
	return exists
}

// GetComponent gets a component value safely
func (mc MachineComponents) GetComponent(key string) string {
	if value, exists := mc[key]; exists {
		return value
	}
	return ""
}

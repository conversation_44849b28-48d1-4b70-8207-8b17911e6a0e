package entities

// LicenseScheme defines the cryptographic scheme used for license generation and validation
type LicenseScheme string

const (
	// Legacy encryption scheme for backwards compatibility
	LegacyEncrypt LicenseScheme = "LEGACY_ENCRYPT"
	
	// RSA-based schemes
	RSA2048PKCS1Encrypt LicenseScheme = "RSA_2048_PKCS1_ENCRYPT" // RSA encryption using PKCS#1 padding
	RSA2048PKCS1Sign    LicenseScheme = "RSA_2048_PKCS1_SIGN"    // RSA signing using PKCS#1 with SHA-256
	RSA2048PSS          LicenseScheme = "RSA_2048_PKCS1_PSS_SIGN" // RSA-PSS signing for enhanced security
	RSA2048JWTRS256     LicenseScheme = "RSA_2048_JWT_RS256"     // JWT-based licenses signed with RS256
	
	// Ed25519-based schemes (modern, recommended)
	Ed25519Sign      LicenseScheme = "ED25519_SIGN"        // Ed25519 digital signatures  
	Ed25519JWTES256  LicenseScheme = "ED25519_JWT_ES256"   // JWT with ES256 (Ed25519)
)

// <PERSON><PERSON>ali<PERSON> checks if the license scheme is supported
func (ls LicenseScheme) IsValid() bool {
	switch ls {
	case LegacyEncrypt, RSA2048PKCS1Encrypt, RSA2048PKCS1Sign, 
		 RSA2048PSS, RSA2048JWTRS256, Ed25519Sign, Ed25519JWTES256:
		return true
	default:
		return false
	}
}

// IsJWT returns true if the scheme uses JWT format
func (ls LicenseScheme) IsJWT() bool {
	return ls == RSA2048JWTRS256 || ls == Ed25519JWTES256
}

// IsEncrypted returns true if the scheme encrypts license data
func (ls LicenseScheme) IsEncrypted() bool {
	return ls == LegacyEncrypt || ls == RSA2048PKCS1Encrypt
}

// IsSigned returns true if the scheme uses digital signatures
func (ls LicenseScheme) IsSigned() bool {
	return ls == RSA2048PKCS1Sign || ls == RSA2048PSS || 
		   ls == RSA2048JWTRS256 || ls == Ed25519Sign || ls == Ed25519JWTES256
}

// UsesRSA returns true if the scheme uses RSA cryptography
func (ls LicenseScheme) UsesRSA() bool {
	return ls == RSA2048PKCS1Encrypt || ls == RSA2048PKCS1Sign || 
		   ls == RSA2048PSS || ls == RSA2048JWTRS256
}

// UsesEd25519 returns true if the scheme uses Ed25519 cryptography
func (ls LicenseScheme) UsesEd25519() bool {
	return ls == Ed25519Sign || ls == Ed25519JWTES256
}
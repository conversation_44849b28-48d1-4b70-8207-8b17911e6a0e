package repositories

import (
	"context"

	"github.com/google/uuid"
	"gorm.io/gorm"
	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/gokeys/gokeys/internal/domain/repositories"
)

// UserOrganizationRepositoryImpl implements UserOrganizationRepository interface
type UserOrganizationRepositoryImpl struct {
	*repositories.BaseRepositoryImpl[entities.UserOrganization]
}

// NewUserOrganizationRepository creates a new user organization repository
func NewUserOrganizationRepository(db *gorm.DB) repositories.UserOrganizationRepository {
	return &UserOrganizationRepositoryImpl{
		BaseRepositoryImpl: repositories.NewBaseRepository[entities.UserOrganization](db),
	}
}

// GetByUserID retrieves user organization relationships by user ID
func (r *UserOrganizationRepositoryImpl) GetByUserID(ctx context.Context, userID uuid.UUID) ([]*entities.UserOrganization, error) {
	var userOrgs []*entities.UserOrganization
	err := r.GetDB().WithContext(ctx).
		Where("user_id = ? AND active = ?", userID, true).
		Preload("Organization").
		Find(&userOrgs).Error
	return userOrgs, err
}

// GetByOrganizationID retrieves user organization relationships by organization ID
func (r *UserOrganizationRepositoryImpl) GetByOrganizationID(ctx context.Context, organizationID uuid.UUID) ([]*entities.UserOrganization, error) {
	var userOrgs []*entities.UserOrganization
	err := r.GetDB().WithContext(ctx).
		Where("organization_id = ? AND active = ?", organizationID, true).
		Preload("User").
		Order("created_at DESC").
		Find(&userOrgs).Error
	return userOrgs, err
}

// GetByUserAndOrganization retrieves a specific user organization relationship
func (r *UserOrganizationRepositoryImpl) GetByUserAndOrganization(ctx context.Context, userID, organizationID uuid.UUID) (*entities.UserOrganization, error) {
	var userOrg entities.UserOrganization
	err := r.GetDB().WithContext(ctx).
		Where("user_id = ? AND organization_id = ? AND active = ?", userID, organizationID, true).
		Preload("User").
		Preload("Organization").
		First(&userOrg).Error
	if err != nil {
		return nil, err
	}
	return &userOrg, nil
}

// DeleteByUserAndOrganization removes a user organization relationship
func (r *UserOrganizationRepositoryImpl) DeleteByUserAndOrganization(ctx context.Context, userID, organizationID uuid.UUID) error {
	return r.GetDB().WithContext(ctx).
		Where("user_id = ? AND organization_id = ?", userID, organizationID).
		Delete(&entities.UserOrganization{}).Error
}

// GetActiveByUserID retrieves all active user organization relationships for OPA
func (r *UserOrganizationRepositoryImpl) GetActiveByUserID(ctx context.Context, userID uuid.UUID) ([]*entities.UserOrganization, error) {
	var userOrgs []*entities.UserOrganization
	err := r.GetDB().WithContext(ctx).
		Where("user_id = ? AND active = ?", userID, true).
		Where("expires_at IS NULL OR expires_at > NOW()").
		Find(&userOrgs).Error
	return userOrgs, err
}

// GetAllActiveForOPA retrieves all active user organization relationships for OPA data loading
func (r *UserOrganizationRepositoryImpl) GetAllActiveForOPA(ctx context.Context) ([]*entities.UserOrganization, error) {
	var userOrgs []*entities.UserOrganization
	err := r.GetDB().WithContext(ctx).
		Where("active = ?", true).
		Where("expires_at IS NULL OR expires_at > NOW()").
		Find(&userOrgs).Error
	return userOrgs, err
}

// GetByResourceConstraint retrieves relationships matching resource constraints
func (r *UserOrganizationRepositoryImpl) GetByResourceConstraint(ctx context.Context, resourceType string, resourceID *uuid.UUID) ([]*entities.UserOrganization, error) {
	query := r.GetDB().WithContext(ctx).
		Where("active = ? AND (expires_at IS NULL OR expires_at > NOW())", true)
	
	// Match resource type (exact or wildcard)
	query = query.Where("resource_type = ? OR resource_type = ?", resourceType, "*")
	
	// Match resource ID if provided
	if resourceID != nil {
		query = query.Where("resource_id = ? OR resource_id IS NULL", *resourceID)
	}
	
	var userOrgs []*entities.UserOrganization
	err := query.Find(&userOrgs).Error
	return userOrgs, err
}

// GetAdminsByOrganization retrieves users with admin-level constraints in an organization
func (r *UserOrganizationRepositoryImpl) GetAdminsByOrganization(ctx context.Context, organizationID uuid.UUID) ([]*entities.UserOrganization, error) {
	var userOrgs []*entities.UserOrganization
	err := r.GetDB().WithContext(ctx).
		Where("organization_id = ? AND active = ?", organizationID, true).
		Where("expires_at IS NULL OR expires_at > NOW()").
		Where("(resource_scope = ? AND resource_type = ?) OR (resource_scope = ? AND resource_type = ? AND allowed_actions @> ?)",
			"global", "*",
			"type_wildcard", "user", `["update","delete"]`).
		Preload("User").
		Find(&userOrgs).Error
	return userOrgs, err
}

// CreateConstraint creates a new user organization constraint
func (r *UserOrganizationRepositoryImpl) CreateConstraint(ctx context.Context, userOrg *entities.UserOrganization) error {
	// Validate constraints before creating
	if err := userOrg.ValidateConstraints(); err != nil {
		return err
	}
	
	return r.Create(ctx, userOrg)
}
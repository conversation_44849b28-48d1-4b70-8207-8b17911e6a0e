-- Rollback optimized indexes for gokeys license management system
-- Created: 2025-07-23

-- ==================================================
-- DROP PERFORMANCE OPTIMIZATION INDEXES
-- ==================================================
DROP INDEX IF EXISTS idx_machines_active_partial;
DROP INDEX IF EXISTS idx_licenses_active_partial;
DROP INDEX IF EXISTS idx_policies_active_partial;
DROP INDEX IF EXISTS idx_products_active_partial;
DROP INDEX IF EXISTS idx_users_active_partial;
DROP INDEX IF EXISTS idx_organizations_active_partial;

-- ==================================================
-- DROP JSONB INDEXES
-- ==================================================
-- DROP INDEX IF EXISTS idx_machines_components;
-- DROP INDEX IF EXISTS idx_licenses_metadata;
-- DROP INDEX IF EXISTS idx_policies_metadata;
-- DROP INDEX IF EXISTS idx_products_platforms;
-- DROP INDEX IF EXISTS idx_users_metadata;
-- DROP INDEX IF EXISTS idx_organizations_metadata;

-- ==================================================
-- DROP API TOKENS INDEXES
-- ==================================================
DROP INDEX IF EXISTS idx_api_tokens_last_used;
DROP INDEX IF EXISTS idx_api_tokens_expires;
DROP INDEX IF EXISTS idx_api_tokens_active;
DROP INDEX IF EXISTS idx_api_tokens_token_hash;
DROP INDEX IF EXISTS idx_api_tokens_organization_id;
DROP INDEX IF EXISTS idx_api_tokens_user_id;

-- ==================================================
-- DROP SESSIONS INDEXES
-- ==================================================
DROP INDEX IF EXISTS idx_sessions_last_used;
DROP INDEX IF EXISTS idx_sessions_expires;
DROP INDEX IF EXISTS idx_sessions_token_hash;
DROP INDEX IF EXISTS idx_sessions_user_id;

-- ==================================================
-- DROP MACHINES INDEXES
-- ==================================================
DROP INDEX IF EXISTS idx_machines_active;
DROP INDEX IF EXISTS idx_machines_heartbeat_status;
DROP INDEX IF EXISTS idx_machines_license_status;
DROP INDEX IF EXISTS idx_machines_last_seen;
DROP INDEX IF EXISTS idx_machines_last_heartbeat;
DROP INDEX IF EXISTS idx_machines_heartbeat;
DROP INDEX IF EXISTS idx_machines_ip;
DROP INDEX IF EXISTS idx_machines_status;
DROP INDEX IF EXISTS idx_machines_fingerprint;
DROP INDEX IF EXISTS idx_machines_owner_id;
DROP INDEX IF EXISTS idx_machines_policy_id;
DROP INDEX IF EXISTS idx_machines_license_id;

-- ==================================================
-- DROP LICENSES INDEXES
-- ==================================================
DROP INDEX IF EXISTS idx_licenses_active;
DROP INDEX IF EXISTS idx_licenses_owner_status;
DROP INDEX IF EXISTS idx_licenses_org_status;
DROP INDEX IF EXISTS idx_licenses_validation;
DROP INDEX IF EXISTS idx_licenses_last_used;
DROP INDEX IF EXISTS idx_licenses_suspended;
DROP INDEX IF EXISTS idx_licenses_expires;
DROP INDEX IF EXISTS idx_licenses_status;
DROP INDEX IF EXISTS idx_licenses_owner;
DROP INDEX IF EXISTS idx_licenses_org_key;
DROP INDEX IF EXISTS idx_licenses_policy_id;
DROP INDEX IF EXISTS idx_licenses_product_id;
DROP INDEX IF EXISTS idx_licenses_organization_id;

-- ==================================================
-- DROP POLICIES INDEXES
-- ==================================================
DROP INDEX IF EXISTS idx_policies_active;
DROP INDEX IF EXISTS idx_policies_protected;
DROP INDEX IF EXISTS idx_policies_scheme;
DROP INDEX IF EXISTS idx_policies_name;
DROP INDEX IF EXISTS idx_policies_org_product;
DROP INDEX IF EXISTS idx_policies_product_id;
DROP INDEX IF EXISTS idx_policies_organization_id;

-- ==================================================
-- DROP PRODUCTS INDEXES
-- ==================================================
DROP INDEX IF EXISTS idx_products_active;
DROP INDEX IF EXISTS idx_products_key;
DROP INDEX IF EXISTS idx_products_name;
DROP INDEX IF EXISTS idx_products_org_code;
DROP INDEX IF EXISTS idx_products_organization_id;

-- ==================================================
-- DROP USER ORGANIZATIONS INDEXES
-- ==================================================
DROP INDEX IF EXISTS idx_user_organizations_expires;
DROP INDEX IF EXISTS idx_user_organizations_active;
DROP INDEX IF EXISTS idx_user_organizations_resource;
DROP INDEX IF EXISTS idx_user_organizations_role;
DROP INDEX IF EXISTS idx_user_organizations_user_org;
DROP INDEX IF EXISTS idx_user_organizations_organization_id;
DROP INDEX IF EXISTS idx_user_organizations_user_id;

-- ==================================================
-- DROP USERS INDEXES
-- ==================================================
DROP INDEX IF EXISTS idx_users_active;
DROP INDEX IF EXISTS idx_users_last_login;
DROP INDEX IF EXISTS idx_users_banned_at;
DROP INDEX IF EXISTS idx_users_password_reset_token;
DROP INDEX IF EXISTS idx_users_status;
DROP INDEX IF EXISTS idx_users_email;

-- ==================================================
-- DROP ORGANIZATIONS INDEXES
-- ==================================================
DROP INDEX IF EXISTS idx_organizations_active;
DROP INDEX IF EXISTS idx_organizations_status;
DROP INDEX IF EXISTS idx_organizations_type;
DROP INDEX IF EXISTS idx_organizations_email;
DROP INDEX IF EXISTS idx_organizations_slug;

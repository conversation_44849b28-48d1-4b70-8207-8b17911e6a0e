-- Optimized indexes for gokeys license management system
-- Performance-focused indexes for OPA and application queries
-- Created: 2025-07-23

-- ==================================================
-- ORGANIZATIONS INDEXES
-- ==================================================
CREATE INDEX idx_organizations_slug ON organizations(slug);
CREATE INDEX idx_organizations_email ON organizations(email);
CREATE INDEX idx_organizations_type ON organizations(type);
CREATE INDEX idx_organizations_status ON organizations(status);
CREATE INDEX idx_organizations_active ON organizations(id) WHERE deleted_at IS NULL;

-- ==================================================
-- USERS INDEXES
-- ==================================================
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_status ON users(status);
CREATE INDEX idx_users_password_reset_token ON users(password_reset_token) WHERE password_reset_token IS NOT NULL;
CREATE INDEX idx_users_banned_at ON users(banned_at) WHERE banned_at IS NOT NULL;
CREATE INDEX idx_users_last_login ON users(last_login) WHERE last_login IS NOT NULL;
CREATE INDEX idx_users_active ON users(id) WHERE deleted_at IS NULL;

-- ==================================================
-- USERS ORGANIZATIONS INDEXES (Membership tracking)
-- ==================================================
CREATE INDEX idx_users_organizations_user_id ON users_organizations(user_id);
CREATE INDEX idx_users_organizations_org_id ON users_organizations(organization_id);
CREATE INDEX idx_users_organizations_invited_by ON users_organizations(invited_by) WHERE invited_by IS NOT NULL;

-- ==================================================
-- PERMISSIONS INDEXES (Critical for authorization)
-- ==================================================
CREATE INDEX idx_permissions_user_id ON permissions(user_id);
CREATE INDEX idx_permissions_scope ON permissions(scope);
CREATE INDEX idx_permissions_resource_type ON permissions(resource_type);
CREATE INDEX idx_permissions_expires_at ON permissions(expires_at) WHERE expires_at IS NOT NULL;
-- Composite index for permission lookups
CREATE INDEX idx_permissions_lookup ON permissions(user_id, scope, resource_type);
-- Partial index for active permissions (no expiration)
CREATE INDEX idx_permissions_active ON permissions(user_id, scope, resource_type) WHERE expires_at IS NULL;

-- ==================================================
-- PRODUCTS INDEXES
-- ==================================================
CREATE INDEX idx_products_organization_id ON products(organization_id);
CREATE INDEX idx_products_org_code ON products(organization_id, code);
CREATE INDEX idx_products_name ON products(name);
CREATE INDEX idx_products_key ON products(key);
CREATE INDEX idx_products_active ON products(organization_id, id) WHERE deleted_at IS NULL;

-- ==================================================
-- POLICIES INDEXES
-- ==================================================
CREATE INDEX idx_policies_organization_id ON policies(organization_id);
CREATE INDEX idx_policies_product_id ON policies(product_id);
CREATE INDEX idx_policies_org_product ON policies(organization_id, product_id);
CREATE INDEX idx_policies_name ON policies(name);
CREATE INDEX idx_policies_scheme ON policies(scheme);
CREATE INDEX idx_policies_protected ON policies(protected);
CREATE INDEX idx_policies_active ON policies(organization_id, id) WHERE deleted_at IS NULL;

-- ==================================================
-- LICENSES INDEXES (Critical for license validation)
-- ==================================================
CREATE INDEX idx_licenses_organization_id ON licenses(organization_id);
CREATE INDEX idx_licenses_product_id ON licenses(product_id);
CREATE INDEX idx_licenses_policy_id ON licenses(policy_id);
CREATE INDEX idx_licenses_org_key ON licenses(organization_id, key);
CREATE INDEX idx_licenses_owner ON licenses(owner_type, owner_id);
CREATE INDEX idx_licenses_status ON licenses(status);
CREATE INDEX idx_licenses_expires ON licenses(expires_at) WHERE expires_at IS NOT NULL;
CREATE INDEX idx_licenses_suspended ON licenses(suspended);
CREATE INDEX idx_licenses_last_used ON licenses(last_used) WHERE last_used IS NOT NULL;
CREATE INDEX idx_licenses_validation ON licenses(last_validated_at, last_validated_checksum) WHERE last_validated_at IS NOT NULL;

-- Composite indexes for common query patterns
CREATE INDEX idx_licenses_org_status ON licenses(organization_id, status);
CREATE INDEX idx_licenses_owner_status ON licenses(owner_type, owner_id, status);
CREATE INDEX idx_licenses_active ON licenses(organization_id, id) WHERE deleted_at IS NULL;

-- ==================================================
-- MACHINES INDEXES (Critical for heartbeat and machine management)
-- ==================================================
CREATE INDEX idx_machines_license_id ON machines(license_id);
CREATE INDEX idx_machines_policy_id ON machines(policy_id);
CREATE INDEX idx_machines_owner_id ON machines(owner_id) WHERE owner_id IS NOT NULL;
CREATE INDEX idx_machines_fingerprint ON machines(license_id, fingerprint);
CREATE INDEX idx_machines_status ON machines(status);
CREATE INDEX idx_machines_ip ON machines(ip) WHERE ip IS NOT NULL;

-- Heartbeat and lifecycle indexes
CREATE INDEX idx_machines_heartbeat ON machines(next_heartbeat_at) WHERE next_heartbeat_at IS NOT NULL;
CREATE INDEX idx_machines_last_heartbeat ON machines(last_heartbeat_at) WHERE last_heartbeat_at IS NOT NULL;
CREATE INDEX idx_machines_last_seen ON machines(last_seen) WHERE last_seen IS NOT NULL;

-- Composite indexes for common patterns
CREATE INDEX idx_machines_license_status ON machines(license_id, status);
CREATE INDEX idx_machines_heartbeat_status ON machines(status, next_heartbeat_at) WHERE next_heartbeat_at IS NOT NULL;
CREATE INDEX idx_machines_active ON machines(license_id, id) WHERE deleted_at IS NULL;

-- ==================================================
-- SESSIONS INDEXES (Critical for authentication)
-- ==================================================
CREATE INDEX idx_sessions_user_id ON sessions(user_id);
CREATE INDEX idx_sessions_token_hash ON sessions(token_hash);
CREATE INDEX idx_sessions_expires ON sessions(expires_at);
CREATE INDEX idx_sessions_last_used ON sessions(last_used_at) WHERE last_used_at IS NOT NULL;

-- ==================================================
-- API TOKENS INDEXES
-- ==================================================
CREATE INDEX idx_api_tokens_user_id ON api_tokens(user_id) WHERE user_id IS NOT NULL;
CREATE INDEX idx_api_tokens_organization_id ON api_tokens(organization_id) WHERE organization_id IS NOT NULL;
CREATE INDEX idx_api_tokens_token_hash ON api_tokens(token_hash);
CREATE INDEX idx_api_tokens_active ON api_tokens(active);
CREATE INDEX idx_api_tokens_expires ON api_tokens(expires_at) WHERE expires_at IS NOT NULL;
CREATE INDEX idx_api_tokens_last_used ON api_tokens(last_used_at) WHERE last_used_at IS NOT NULL;

-- ==================================================
-- JSONB INDEXES for metadata queries
-- ==================================================
-- Only create these if you plan to query JSONB fields frequently
-- CREATE INDEX idx_organizations_metadata ON organizations USING GIN(metadata);
-- CREATE INDEX idx_users_metadata ON users USING GIN(metadata);
-- CREATE INDEX idx_products_platforms ON products USING GIN(platforms);
-- CREATE INDEX idx_policies_metadata ON policies USING GIN(metadata);
-- CREATE INDEX idx_licenses_metadata ON licenses USING GIN(metadata);
-- CREATE INDEX idx_machines_components ON machines USING GIN(components);

-- ==================================================
-- PERFORMANCE OPTIMIZATION INDEXES
-- ==================================================
-- Partial indexes for active records (exclude soft deleted)
CREATE INDEX idx_organizations_active_partial ON organizations(id, name) WHERE deleted_at IS NULL;
CREATE INDEX idx_users_active_partial ON users(id, email) WHERE deleted_at IS NULL;
CREATE INDEX idx_products_active_partial ON products(organization_id, id, code) WHERE deleted_at IS NULL;
CREATE INDEX idx_policies_active_partial ON policies(organization_id, id, product_id) WHERE deleted_at IS NULL;
CREATE INDEX idx_licenses_active_partial ON licenses(organization_id, id, key) WHERE deleted_at IS NULL;
CREATE INDEX idx_machines_active_partial ON machines(license_id, id, fingerprint) WHERE deleted_at IS NULL;

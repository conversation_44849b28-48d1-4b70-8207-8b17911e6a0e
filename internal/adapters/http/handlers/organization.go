package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/gokeys/gokeys/internal/domain/repositories"
	"github.com/gokeys/gokeys/internal/domain/services/auth"
)

// OrganizationHandler handles organization-related HTTP requests
type OrganizationHandler struct {
	organizationRepo repositories.OrganizationRepository
	userRepo         repositories.UserRepository
	usersOrgRepo     repositories.UsersOrganizationRepository
	permissionRepo   repositories.PermissionRepository
	authService      *auth.AuthService
}

// NewOrganizationHandler creates a new organization handler
func NewOrganizationHandler(
	organizationRepo repositories.OrganizationRepository,
	userRepo repositories.UserRepository,
	usersOrgRepo repositories.UsersOrganizationRepository,
	permissionRepo repositories.PermissionRepository,
	authService *auth.AuthService,
) *OrganizationHandler {
	return &OrganizationHandler{
		organizationRepo: organizationRepo,
		userRepo:         userRepo,
		usersOrgRepo:     usersOrgRepo,
		permissionRepo:   permissionRepo,
		authService:      authService,
	}
}

// ListAllOrganizations lists all organizations (system admin only)
func (h *OrganizationHandler) ListAllOrganizations(c *gin.Context) {
	// Parse pagination parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))
	search := c.Query("search")

	filter := repositories.DefaultListFilter()
	filter.Page = page
	filter.PageSize = pageSize
	
	if page > 0 {
		filter.Page = page
	}
	if pageSize > 0 && pageSize <= 100 {
		filter.PageSize = pageSize
	}
	if search != "" {
		filter.Search = search
	}

	organizations, total, err := h.organizationRepo.List(c.Request.Context(), filter)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to list organizations", "reason": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"organizations": organizations,
		"pagination": gin.H{
			"page":       page,
			"page_size":  pageSize,
			"total":      total,
			"total_pages": (total + int64(pageSize) - 1) / int64(pageSize),
		},
	})
}

// CreateOrganization creates a new organization (system admin only)
func (h *OrganizationHandler) CreateOrganization(c *gin.Context) {
	currentUserID := c.GetString("user_id")

	var requestData struct {
		Name     string                        `json:"name" binding:"required"`
		Slug     string                        `json:"slug" binding:"required"`
		Email    string                        `json:"email" binding:"required,email"`
		Type     string                        `json:"type"`
		Settings entities.OrganizationSettings `json:"settings"`
		Metadata entities.Metadata             `json:"metadata"`
	}

	if err := c.ShouldBindJSON(&requestData); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid request", "reason": err.Error()})
		return
	}

	// Check if slug already exists
	existing, err := h.organizationRepo.GetBySlug(c.Request.Context(), requestData.Slug)
	if err == nil && existing != nil {
		c.JSON(http.StatusConflict, gin.H{"error": "organization slug already exists"})
		return
	}

	// Set default type if not provided
	if requestData.Type == "" {
		requestData.Type = entities.OrganizationTypeVendor
	}

	// Create organization
	organization := &entities.Organization{
		Name:     requestData.Name,
		Slug:     requestData.Slug,
		Email:    requestData.Email,
		Type:     requestData.Type,
		Status:   entities.OrganizationStatusActive,
		Settings: requestData.Settings,
		Metadata: requestData.Metadata,
	}

	if err := h.organizationRepo.Create(c.Request.Context(), organization); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to create organization", "reason": err.Error()})
		return
	}

	// Grant the creator admin permissions for the new organization
	orgScope := "org:" + organization.ID
	err = h.authService.GrantPermission(c.Request.Context(), currentUserID, orgScope, "*", []string{"*"}, currentUserID)
	if err != nil {
		// Log error but don't fail the request
		// Organization was created successfully
	}

	c.JSON(http.StatusCreated, gin.H{"organization": organization})
}

// GetOrganization gets organization information
func (h *OrganizationHandler) GetOrganization(c *gin.Context) {
	organizationID := c.Param("organization_id")
	if organizationID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "organization_id required"})
		return
	}

	organization, err := h.organizationRepo.GetByID(c.Request.Context(), parseUUID(organizationID))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "organization not found", "reason": err.Error()})
		return
	}

	// Remove sensitive information
	organization.PrivateKey = nil
	organization.SecretKey = nil
	organization.Ed25519PrivateKey = nil

	c.JSON(http.StatusOK, gin.H{"organization": organization})
}

// UpdateOrganization updates organization information
func (h *OrganizationHandler) UpdateOrganization(c *gin.Context) {
	organizationID := c.Param("organization_id")
	if organizationID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "organization_id required"})
		return
	}

	var requestData struct {
		Name        *string                        `json:"name"`
		Email       *string                        `json:"email"`
		Type        *string                        `json:"type"`
		Status      *string                        `json:"status"`
		MaxUsers    *int                           `json:"max_users"`
		MaxLicenses *int                           `json:"max_licenses"`
		MaxMachines *int                           `json:"max_machines"`
		Settings    *entities.OrganizationSettings `json:"settings"`
		Metadata    *entities.Metadata             `json:"metadata"`
	}

	if err := c.ShouldBindJSON(&requestData); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid request", "reason": err.Error()})
		return
	}

	organization, err := h.organizationRepo.GetByID(c.Request.Context(), parseUUID(organizationID))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "organization not found", "reason": err.Error()})
		return
	}

	// Update fields if provided
	if requestData.Name != nil {
		organization.Name = *requestData.Name
	}
	if requestData.Email != nil {
		organization.Email = *requestData.Email
	}
	if requestData.Type != nil {
		organization.Type = *requestData.Type
	}
	if requestData.Status != nil {
		organization.Status = *requestData.Status
	}
	if requestData.MaxUsers != nil {
		organization.MaxUsers = requestData.MaxUsers
	}
	if requestData.MaxLicenses != nil {
		organization.MaxLicenses = requestData.MaxLicenses
	}
	if requestData.MaxMachines != nil {
		organization.MaxMachines = requestData.MaxMachines
	}
	if requestData.Settings != nil {
		organization.Settings = *requestData.Settings
	}
	if requestData.Metadata != nil {
		organization.Metadata = *requestData.Metadata
	}

	if err := h.organizationRepo.Update(c.Request.Context(), organization); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to update organization", "reason": err.Error()})
		return
	}

	// Remove sensitive information
	organization.PrivateKey = nil
	organization.SecretKey = nil
	organization.Ed25519PrivateKey = nil

	c.JSON(http.StatusOK, gin.H{"organization": organization})
}

// DeleteOrganization deletes an organization (system admin only)
func (h *OrganizationHandler) DeleteOrganization(c *gin.Context) {
	organizationID := c.Param("organization_id")
	if organizationID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "organization_id required"})
		return
	}

	// Check if organization exists
	organization, err := h.organizationRepo.GetByID(c.Request.Context(), parseUUID(organizationID))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "organization not found", "reason": err.Error()})
		return
	}

	// Check if organization is protected
	if organization.Protected {
		c.JSON(http.StatusForbidden, gin.H{"error": "cannot delete protected organization"})
		return
	}

	// Soft delete organization
	if err := h.organizationRepo.SoftDelete(c.Request.Context(), parseUUID(organizationID)); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to delete organization", "reason": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "organization deleted successfully"})
}

// GetOrganizationStats gets organization statistics
func (h *OrganizationHandler) GetOrganizationStats(c *gin.Context) {
	organizationID := c.Param("organization_id")
	if organizationID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "organization_id required"})
		return
	}

	// Get organization users count
	userOrgs, err := h.usersOrgRepo.GetOrganizationUsers(c.Request.Context(), organizationID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to get organization stats", "reason": err.Error()})
		return
	}

	stats := gin.H{
		"users_count": len(userOrgs),
		// Add more stats as needed (products, licenses, machines, etc.)
	}

	c.JSON(http.StatusOK, gin.H{"stats": stats})
}

// ListUserOrganizations lists organizations for the current user
func (h *OrganizationHandler) ListUserOrganizations(c *gin.Context) {
	userID := c.GetString("user_id")
	if userID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "unauthorized", "reason": "no user context"})
		return
	}

	userOrgs, err := h.authService.GetUserOrganizations(c.Request.Context(), userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to get user organizations", "reason": err.Error()})
		return
	}

	// Get full organization details
	var organizations []interface{}
	for _, userOrg := range userOrgs {
		org, err := h.organizationRepo.GetByID(c.Request.Context(), parseUUID(userOrg.OrganizationID))
		if err != nil {
			continue // Skip organizations that can't be loaded
		}

		// Remove sensitive information
		org.PrivateKey = nil
		org.SecretKey = nil
		org.Ed25519PrivateKey = nil

		organizations = append(organizations, gin.H{
			"organization": org,
			"membership": gin.H{
				"joined_at":  userOrg.JoinedAt,
				"invited_by": userOrg.InvitedBy,
			},
		})
	}

	c.JSON(http.StatusOK, gin.H{
		"organizations": organizations,
		"total":         len(organizations),
	})
}

// GetSystemStats gets system-wide statistics (system admin only)
func (h *OrganizationHandler) GetSystemStats(c *gin.Context) {
	// Get all organizations count
	filter := repositories.DefaultListFilter()
	filter.PageSize = 1 // We only need the count
	
	_, totalOrgs, err := h.organizationRepo.List(c.Request.Context(), filter)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to get system stats", "reason": err.Error()})
		return
	}

	// Get all users count
	_, totalUsers, err := h.userRepo.List(c.Request.Context(), filter)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to get system stats", "reason": err.Error()})
		return
	}

	stats := gin.H{
		"organizations_count": totalOrgs,
		"users_count":         totalUsers,
		// Add more system-wide stats as needed
	}

	c.JSON(http.StatusOK, gin.H{"stats": stats})
}
package handlers

import (
	"crypto/sha256"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gokeys/gokeys/internal/adapters/http/middleware"
	"github.com/gokeys/gokeys/internal/adapters/http/responses"
	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/gokeys/gokeys/internal/domain/repositories"
	"github.com/gokeys/gokeys/internal/domain/services/auth"
	"github.com/gokeys/gokeys/internal/domain/services/crypto"
	"github.com/google/uuid"
	"github.com/lib/pq"
)

// AuthHandler xử lý các endpoint liên quan đến authentication và authorization
// Sử dụng constraint-based authorization system mới
type AuthHandler struct {
	authService      *auth.AuthService
	userRepo         repositories.UserRepository
	userOrgRepo      repositories.UserOrganizationRepository
	organizationRepo repositories.OrganizationRepository
	sessionRepo      repositories.SessionRepository
	cryptoService    *crypto.CryptoService
	crudResponse     *responses.CRUDResponse
}

// NewAuthHandler tạo auth handler mới với constraint-based auth service
func NewAuthHandler(
	authService *auth.AuthService,
	userRepo repositories.UserRepository,
	userOrgRepo repositories.UserOrganizationRepository,
	organizationRepo repositories.OrganizationRepository,
	sessionRepo repositories.SessionRepository,
	cryptoService *crypto.CryptoService,
) *AuthHandler {
	return &AuthHandler{
		authService:      authService,
		userRepo:         userRepo,
		userOrgRepo:      userOrgRepo,
		organizationRepo: organizationRepo,
		sessionRepo:      sessionRepo,
		cryptoService:    cryptoService,
		crudResponse:     responses.NewCRUDResponse(),
	}
}

// LoginRequest đại diện cho request đăng nhập
type LoginRequest struct {
	Email    string `json:"email" binding:"required,email" example:"<EMAIL>"`
	Password string `json:"password" binding:"required,min=6" example:"password123"`
}

// LoginResponse đại diện cho response sau khi đăng nhập thành công
type LoginResponse struct {
	Token     string        `json:"token" example:"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."`
	TokenType string        `json:"token_type" example:"Bearer"`
	ExpiresIn int           `json:"expires_in" example:"3600"`
	User      *UserInfo     `json:"user"`
	Subject   *auth.Subject `json:"subject"`
}

// UserInfo đại diện cho thông tin user trong response
type UserInfo struct {
	ID             string   `json:"id" example:"550e8400-e29b-41d4-a716-446655440000"`
	Email          string   `json:"email" example:"<EMAIL>"`
	FirstName      *string  `json:"first_name,omitempty" example:"John"`
	LastName       *string  `json:"last_name,omitempty" example:"Doe"`
	OrganizationID *string  `json:"organization_id,omitempty" example:"550e8400-e29b-41d4-a716-************"`
	Roles          []string `json:"roles" example:"[\"admin\"]"`
	Permissions    []string `json:"permissions" example:"[\"license.read\", \"machine.read\"]"`
}

// RegisterRequest đại diện cho request đăng ký user mới
type RegisterRequest struct {
	Email             string                 `json:"email" binding:"required,email" example:"<EMAIL>"`
	Password          string                 `json:"password" binding:"required,min=6" example:"password123"`
	PasswordConfirm   string                 `json:"password_confirm" binding:"required,eqfield=Password" example:"password123"`
	FirstName         *string                `json:"first_name,omitempty" example:"John"`
	LastName          *string                `json:"last_name,omitempty" example:"Doe"`
	OrganizationName  *string                `json:"organization_name,omitempty" example:"Acme Corp"`
	Metadata          map[string]interface{} `json:"metadata,omitempty"`
}

// CreateUserRequest đại diện cho request tạo user mới (admin only)
type CreateUserRequest struct {
	Email             string                 `json:"email" binding:"required,email" example:"<EMAIL>"`
	Password          string                 `json:"password" binding:"required,min=6" example:"password123"`
	FirstName         *string                `json:"first_name,omitempty" example:"John"`
	LastName          *string                `json:"last_name,omitempty" example:"Doe"`
	OrganizationID    *string                `json:"organization_id,omitempty" binding:"omitempty,uuid" example:"550e8400-e29b-41d4-a716-************"`
	Role              string                 `json:"role" binding:"required" example:"member"`
	ResourceType      string                 `json:"resource_type" binding:"required" example:"license"`
	ResourceScope     string                 `json:"resource_scope" binding:"required,oneof=specific type_wildcard global owner" example:"type_wildcard"`
	ResourceID        *string                `json:"resource_id,omitempty" binding:"omitempty,uuid" example:"550e8400-e29b-41d4-a716-************"`
	AllowedActions    []string               `json:"allowed_actions" binding:"required" example:"[\"read\", \"create\", \"update\"]"`
	Metadata          map[string]interface{} `json:"metadata,omitempty"`
}

// UpdateUserRequest đại diện cho request cập nhật user
type UpdateUserRequest struct {
	FirstName *string                `json:"first_name,omitempty" example:"John"`
	LastName  *string                `json:"last_name,omitempty" example:"Doe"`
	Status    *string                `json:"status,omitempty" binding:"omitempty,oneof=active inactive banned" example:"active"`
	Metadata  map[string]interface{} `json:"metadata,omitempty"`
}

// LoginHandler xử lý đăng nhập người dùng
// @Summary User login với constraint-based permissions
// @Description Authenticate user và trả về subject với permissions từ user_organizations
// @Tags auth
// @Accept json
// @Produce json
// @Param request body LoginRequest true "Login credentials"
// @Success 200 {object} LoginResponse
// @Failure 400 {object} responses.ErrorResponse
// @Failure 401 {object} responses.ErrorResponse
// @Failure 500 {object} responses.ErrorResponse
// @Router /auth/login [post]
func (h *AuthHandler) LoginHandler(c *gin.Context) {
	var req LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		responses.RenderSimpleError(c, http.StatusBadRequest, responses.ErrorCodeValidationFailed, "Invalid request payload", err.Error())
		return
	}

	// Find user by email
	user, err := h.userRepo.GetByEmail(c.Request.Context(), req.Email)
	if err != nil {
		responses.RenderSimpleError(c, http.StatusUnauthorized, responses.ErrorCodeUnauthorized, "Invalid credentials", "User not found")
		return
	}

	// Verify password
	if err := h.cryptoService.Password.VerifyPassword(req.Password, user.Password); err != nil {
		responses.RenderSimpleError(c, http.StatusUnauthorized, responses.ErrorCodeUnauthorized, "Invalid credentials", "Password mismatch")
		return
	}

	// Check if user is active
	if !user.IsActive() {
		responses.RenderSimpleError(c, http.StatusForbidden, responses.ErrorCodeForbidden, "Account inactive", "Your account is not active")
		return
	}

	// Authenticate user with auth service to get subject with permissions
	userUUID, err := uuid.Parse(user.ID)
	if err != nil {
		responses.RenderSimpleError(c, http.StatusInternalServerError, responses.ErrorCodeInternalError, "Invalid user ID format", err.Error())
		return
	}

	subject, err := h.authService.AuthenticateUser(c.Request.Context(), userUUID)
	if err != nil {
		responses.RenderSimpleError(c, http.StatusInternalServerError, responses.ErrorCodeInternalError, "Failed to authenticate user", err.Error())
		return
	}

	// Update last login
	h.userRepo.UpdateLastLogin(c.Request.Context(), userUUID)

	// Create session
	sessionID := uuid.New()
	expiresAt := time.Now().Add(24 * time.Hour) // 24 hour session
	
	// Create JWT token using auth service (with configured keys)
	token, err := h.authService.CreateJWTToken(c.Request.Context(), userUUID, sessionID)
	if err != nil {
		responses.RenderSimpleError(c, http.StatusInternalServerError, responses.ErrorCodeInternalError, "Failed to create session token", err.Error())
		return
	}

	// Create session entity
	userAgent := c.GetHeader("User-Agent")
	ipAddress := c.ClientIP()
	now := time.Now()
	session := &entities.Session{
		ID:         sessionID.String(),
		UserID:     userUUID.String(),
		TokenHash:  hashToken(token),
		ExpiresAt:  expiresAt,
		LastUsedAt: &now,
		UserAgent:  &userAgent,
		IP:         &ipAddress,
	}

	if err := h.sessionRepo.Create(c.Request.Context(), session); err != nil {
		responses.RenderSimpleError(c, http.StatusInternalServerError, responses.ErrorCodeInternalError, "Failed to create session", err.Error())
		return
	}

	// Return login response
	response := LoginResponse{
		Token:     token,
		TokenType: "Bearer",
		ExpiresIn: int(time.Until(expiresAt).Seconds()),
		User: &UserInfo{
			ID:             user.ID,
			Email:          user.Email,
			FirstName:      user.FirstName,
			LastName:       user.LastName,
			OrganizationID: subject.OrganizationID,
			Roles:          subject.Roles,
			Permissions:    subject.Permissions,
		},
		Subject: subject,
	}

	c.JSON(http.StatusOK, response)
}

// CreateUser tạo user mới với constraint-based permissions
// @Summary Tạo user mới (Admin only)
// @Description Tạo user mới và thiết lập user_organization constraints
// @Tags auth
// @Accept json
// @Produce json
// @Security Bearer
// @Param request body CreateUserRequest true "User creation data"
// @Success 201 {object} UserInfo
// @Failure 400 {object} responses.ErrorResponse
// @Failure 403 {object} responses.ErrorResponse
// @Failure 500 {object} responses.ErrorResponse
// @Router /admin/users [post]
func (h *AuthHandler) CreateUser(c *gin.Context) {
	var req CreateUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		responses.RenderSimpleError(c, http.StatusBadRequest, responses.ErrorCodeValidationFailed, "Invalid request payload", err.Error())
		return
	}

	// Tạo user entity
	userID := uuid.New().String()
	user := &entities.User{
		ID:        userID,
		Email:     req.Email,
		FirstName: req.FirstName,
		LastName:  req.LastName,
		Status:    "active",
		Metadata:  req.Metadata,
	}

	// Tạo user trong database
	if err := h.userRepo.Create(c.Request.Context(), user); err != nil {
		responses.RenderSimpleError(c, http.StatusInternalServerError, responses.ErrorCodeInternalError, "Failed to create user", err.Error())
		return
	}

	// Tạo user organization constraint
	userOrg := &entities.UserOrganization{
		ID:             uuid.New().String(),
		UserID:         userID, // UserID là string trong entity
		OrganizationID: nil, // Có thể null cho end-user customers
		Role:           req.Role,
		ResourceType:   req.ResourceType,
		ResourceScope:  req.ResourceScope,
		ResourceID:     req.ResourceID,
		AllowedActions: req.AllowedActions,
		Active:         true,
	}

	// Set organization ID nếu được cung cấp
	if req.OrganizationID != nil {
		userOrg.OrganizationID = req.OrganizationID // OrganizationID là *string trong entity
	}

	// Tạo constraint
	if err := h.authService.CreateUserConstraint(c.Request.Context(), userOrg); err != nil {
		// Rollback user creation nếu constraint creation thất bại
		h.userRepo.Delete(c.Request.Context(), uuid.MustParse(userID))
		responses.RenderSimpleError(c, http.StatusInternalServerError, responses.ErrorCodeInternalError, "Failed to create user constraints", err.Error())
		return
	}

	// Trả về user response
	response := &UserInfo{
		ID:             userID,
		Email:          user.Email,
		FirstName:      user.FirstName,
		LastName:       user.LastName,
		OrganizationID: req.OrganizationID,
		Roles:          []string{req.Role},
		Permissions:    req.AllowedActions, // Simplified - trong thực tế sẽ được tính từ constraints
	}

	c.JSON(http.StatusCreated, response)
}

// ListUsers liệt kê users dựa trên constraint-based permissions
// @Summary Liệt kê users
// @Description Lấy danh sách users mà user hiện tại có quyền truy cập
// @Tags auth
// @Accept json
// @Produce json
// @Security Bearer
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(20)
// @Success 200 {array} UserInfo
// @Failure 400 {object} responses.ErrorResponse
// @Failure 403 {object} responses.ErrorResponse
// @Failure 500 {object} responses.ErrorResponse
// @Router /users [get]
func (h *AuthHandler) ListUsers(c *gin.Context) {
	// Parse pagination parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))

	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 20
	}

	// Lấy subject từ context
	subject := middleware.GetSubject(c)
	if subject == nil {
		responses.RenderSimpleError(c, http.StatusUnauthorized, responses.ErrorCodeUnauthorized, "Authentication required", "No subject in context")
		return
	}

	// Lấy users với filtering dựa trên permissions của subject
	filter := repositories.ListFilter{
		Page:     page,
		PageSize: limit,
	}
	users, total, err := h.userRepo.List(c.Request.Context(), filter)
	if err != nil {
		responses.RenderSimpleError(c, http.StatusInternalServerError, responses.ErrorCodeInternalError, "Failed to fetch users", err.Error())
		return
	}

	// Convert sang response format và filter dựa trên permissions
	var usersList []*UserInfo
	for _, user := range users {
		// Check authorization cho từng user
		resource := &auth.Resource{
			Type: middleware.ResourceTypeUser,
			ID:   user.ID,
		}

		authReq := &auth.AuthorizationRequest{
			Subject:  subject,
			Resource: resource,
			Action:   middleware.ActionRead,
		}

		result, err := h.authService.Authorize(c.Request.Context(), authReq)
		if err != nil || !result.Allowed {
			continue // Skip users mà không có quyền xem
		}

		// Lấy permissions của user này
		userUUID, err := uuid.Parse(user.ID)
		if err != nil {
			continue
		}

		permissions, _ := h.authService.GetUserPermissions(c.Request.Context(), userUUID)

		userInfo := &UserInfo{
			ID:          user.ID,
			Email:       user.Email,
			FirstName:   user.FirstName,
			LastName:    user.LastName,
			Permissions: permissions,
		}

		usersList = append(usersList, userInfo)
	}

	c.JSON(http.StatusOK, gin.H{
		"data": usersList,
		"pagination": gin.H{
			"page":  page,
			"limit": limit,
			"total": total,
		},
	})
}

// GetUser lấy thông tin user theo ID
// @Summary Lấy thông tin user
// @Description Lấy thông tin chi tiết user theo ID
// @Tags auth
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path string true "User ID" format(uuid)
// @Success 200 {object} UserInfo
// @Failure 400 {object} responses.ErrorResponse
// @Failure 403 {object} responses.ErrorResponse
// @Failure 404 {object} responses.ErrorResponse
// @Failure 500 {object} responses.ErrorResponse
// @Router /users/{id} [get]
func (h *AuthHandler) GetUser(c *gin.Context) {
	userID := c.Param("id")
	if userID == "" {
		responses.RenderSimpleError(c, http.StatusBadRequest, responses.ErrorCodeValidationFailed, "User ID is required", "Missing user ID parameter")
		return
	}

	// Validate UUID format
	userUUID, err := uuid.Parse(userID)
	if err != nil {
		responses.RenderSimpleError(c, http.StatusBadRequest, responses.ErrorCodeValidationFailed, "Invalid user ID format", err.Error())
		return
	}

	// Lấy user từ database
	user, err := h.userRepo.GetByID(c.Request.Context(), userUUID)
	if err != nil {
		responses.RenderSimpleError(c, http.StatusNotFound, responses.ErrorCodeResourceNotFound, "User not found", err.Error())
		return
	}

	// Lấy permissions của user
	permissions, err := h.authService.GetUserPermissions(c.Request.Context(), userUUID)
	if err != nil {
		permissions = []string{} // Fallback to empty permissions
	}

	// Lấy user organizations để xác định roles
	userOrgs, err := h.userOrgRepo.GetActiveByUserID(c.Request.Context(), userUUID)
	if err != nil {
		userOrgs = []*entities.UserOrganization{} // Fallback to empty
	}

	roles := make([]string, len(userOrgs))
	var organizationID *string
	for i, userOrg := range userOrgs {
		roles[i] = userOrg.Role
		if userOrg.OrganizationID != nil && organizationID == nil {
			organizationID = userOrg.OrganizationID // OrganizationID đã là *string
		}
	}

	response := &UserInfo{
		ID:             user.ID,
		Email:          user.Email,
		FirstName:      user.FirstName,
		LastName:       user.LastName,
		OrganizationID: organizationID,
		Roles:          roles,
		Permissions:    permissions,
	}

	c.JSON(http.StatusOK, response)
}

// UpdateUser cập nhật thông tin user
// @Summary Cập nhật user
// @Description Cập nhật thông tin user theo ID
// @Tags auth
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path string true "User ID" format(uuid)
// @Param request body UpdateUserRequest true "User update data"
// @Success 200 {object} UserInfo
// @Failure 400 {object} responses.ErrorResponse
// @Failure 403 {object} responses.ErrorResponse
// @Failure 404 {object} responses.ErrorResponse
// @Failure 500 {object} responses.ErrorResponse
// @Router /users/{id} [put]
func (h *AuthHandler) UpdateUser(c *gin.Context) {
	userID := c.Param("id")
	if userID == "" {
		responses.RenderSimpleError(c, http.StatusBadRequest, responses.ErrorCodeValidationFailed, "User ID is required", "Missing user ID parameter")
		return
	}

	userUUID, err := uuid.Parse(userID)
	if err != nil {
		responses.RenderSimpleError(c, http.StatusBadRequest, responses.ErrorCodeValidationFailed, "Invalid user ID format", err.Error())
		return
	}

	var req UpdateUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		responses.RenderSimpleError(c, http.StatusBadRequest, responses.ErrorCodeValidationFailed, "Invalid request payload", err.Error())
		return
	}

	// Lấy user hiện tại
	user, err := h.userRepo.GetByID(c.Request.Context(), userUUID)
	if err != nil {
		responses.RenderSimpleError(c, http.StatusNotFound, responses.ErrorCodeResourceNotFound, "User not found", err.Error())
		return
	}

	// Cập nhật các field được cung cấp
	if req.FirstName != nil {
		user.FirstName = req.FirstName
	}
	if req.LastName != nil {
		user.LastName = req.LastName
	}
	if req.Status != nil {
		user.Status = *req.Status
	}
	if req.Metadata != nil {
		user.Metadata = req.Metadata
	}

	// Lưu cập nhật
	if err := h.userRepo.Update(c.Request.Context(), user); err != nil {
		responses.RenderSimpleError(c, http.StatusInternalServerError, responses.ErrorCodeInternalError, "Failed to update user", err.Error())
		return
	}

	// Trả về updated user
	permissions, _ := h.authService.GetUserPermissions(c.Request.Context(), userUUID)

	response := &UserInfo{
		ID:          user.ID,
		Email:       user.Email,
		FirstName:   user.FirstName,
		LastName:    user.LastName,
		Permissions: permissions,
	}

	c.JSON(http.StatusOK, response)
}

// DeleteUser xóa user (Admin only)
// @Summary Xóa user
// @Description Xóa user và tất cả constraints liên quan
// @Tags auth
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path string true "User ID" format(uuid)
// @Success 204 "No Content"
// @Failure 400 {object} responses.ErrorResponse
// @Failure 403 {object} responses.ErrorResponse
// @Failure 404 {object} responses.ErrorResponse
// @Failure 500 {object} responses.ErrorResponse
// @Router /admin/users/{id} [delete]
func (h *AuthHandler) DeleteUser(c *gin.Context) {
	userID := c.Param("id")
	if userID == "" {
		responses.RenderSimpleError(c, http.StatusBadRequest, responses.ErrorCodeValidationFailed, "User ID is required", "Missing user ID parameter")
		return
	}

	userUUID, err := uuid.Parse(userID)
	if err != nil {
		responses.RenderSimpleError(c, http.StatusBadRequest, responses.ErrorCodeValidationFailed, "Invalid user ID format", err.Error())
		return
	}

	// Kiểm tra user có tồn tại không
	_, err = h.userRepo.GetByID(c.Request.Context(), userUUID)
	if err != nil {
		responses.RenderSimpleError(c, http.StatusNotFound, responses.ErrorCodeResourceNotFound, "User not found", err.Error())
		return
	}

	// TODO: Implement xóa user organization constraints
	// Hiện tại skip bước này vì repository chưa có method DeleteByUserID

	// Xóa user
	if err := h.userRepo.Delete(c.Request.Context(), userUUID); err != nil {
		responses.RenderSimpleError(c, http.StatusInternalServerError, responses.ErrorCodeInternalError, "Failed to delete user", err.Error())
		return
	}

	c.Status(http.StatusNoContent)
}

// RegisterHandler xử lý đăng ký user mới
// @Summary Register new user
// @Description Register a new user and optionally create an organization
// @Tags auth
// @Accept json
// @Produce json
// @Param request body RegisterRequest true "Registration data"
// @Success 201 {object} LoginResponse
// @Failure 400 {object} responses.ErrorResponse
// @Failure 409 {object} responses.ErrorResponse
// @Failure 500 {object} responses.ErrorResponse
// @Router /auth/register [post]
func (h *AuthHandler) RegisterHandler(c *gin.Context) {
	var req RegisterRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		responses.RenderSimpleError(c, http.StatusBadRequest, responses.ErrorCodeValidationFailed, "Invalid request payload", err.Error())
		return
	}

	// Check if user already exists
	existingUser, _ := h.userRepo.GetByEmail(c.Request.Context(), req.Email)
	if existingUser != nil {
		responses.RenderSimpleError(c, http.StatusConflict, responses.ErrorCodeResourceExists, "User already exists", "Email is already registered")
		return
	}

	// Hash password
	hashedPassword, err := h.cryptoService.Password.HashPassword(req.Password)
	if err != nil {
		responses.RenderSimpleError(c, http.StatusInternalServerError, responses.ErrorCodeInternalError, "Failed to process password", err.Error())
		return
	}

	// Create user entity
	userID := uuid.New()
	user := &entities.User{
		ID:        userID.String(),
		Email:     req.Email,
		Password:  hashedPassword,
		FirstName: req.FirstName,
		LastName:  req.LastName,
		Status:    entities.UserStatusActive,
		Metadata:  req.Metadata,
	}

	// Create user in database
	if err := h.userRepo.Create(c.Request.Context(), user); err != nil {
		responses.RenderSimpleError(c, http.StatusInternalServerError, responses.ErrorCodeInternalError, "Failed to create user", err.Error())
		return
	}

	// If organization name is provided, create organization
	var organizationID *string
	if req.OrganizationName != nil && *req.OrganizationName != "" {
		orgID := uuid.New()
		organization := &entities.Organization{
			ID:   orgID.String(),
			Name: *req.OrganizationName,
			Slug: generateSlug(*req.OrganizationName),
		}

		if err := h.organizationRepo.Create(c.Request.Context(), organization); err != nil {
			// Rollback user creation
			h.userRepo.Delete(c.Request.Context(), userID)
			responses.RenderSimpleError(c, http.StatusInternalServerError, responses.ErrorCodeInternalError, "Failed to create organization", err.Error())
			return
		}
		organizationID = &organization.ID
	}

	// Only create user organization constraint if organization was created
	if organizationID != nil {
		userOrg := &entities.UserOrganization{
			ID:             uuid.New().String(),
			UserID:         userID.String(),
			OrganizationID: organizationID,
			Role:           "admin",
			ResourceType:   "*", // Full access to all resource types
			ResourceScope:  "global",
			AllowedActions: pq.StringArray{"*"}, // All actions allowed
			Active:         true,
		}

		if err := h.userOrgRepo.Create(c.Request.Context(), userOrg); err != nil {
			// Rollback
			h.userRepo.Delete(c.Request.Context(), userID)
			orgUUID, _ := uuid.Parse(*organizationID)
			h.organizationRepo.Delete(c.Request.Context(), orgUUID)
			responses.RenderSimpleError(c, http.StatusInternalServerError, responses.ErrorCodeInternalError, "Failed to create user constraints", err.Error())
			return
		}
	} else {
		// For users without organization, create a basic customer constraint
		userOrg := &entities.UserOrganization{
			ID:             uuid.New().String(),
			UserID:         userID.String(),
			OrganizationID: nil, // No organization
			Role:           "customer",
			ResourceType:   "license", // Customer can only manage their own licenses
			ResourceScope:  "owner",
			AllowedActions: pq.StringArray{"read", "validate"}, // Limited actions
			Active:         true,
		}

		if err := h.userOrgRepo.Create(c.Request.Context(), userOrg); err != nil {
			// Rollback user creation
			h.userRepo.Delete(c.Request.Context(), userID)
			responses.RenderSimpleError(c, http.StatusInternalServerError, responses.ErrorCodeInternalError, "Failed to create user constraints", err.Error())
			return
		}
	}

	// Create session for auto-login
	sessionID := uuid.New()
	expiresAt := time.Now().Add(24 * time.Hour) // 24 hour session
	
	// Create JWT token using auth service (with configured keys)
	token, err := h.authService.CreateJWTToken(c.Request.Context(), userID, sessionID)
	if err != nil {
		responses.RenderSimpleError(c, http.StatusInternalServerError, responses.ErrorCodeInternalError, "Failed to create session token", err.Error())
		return
	}

	// Create session entity
	userAgent := c.GetHeader("User-Agent")
	ipAddress := c.ClientIP()
	now := time.Now()
	session := &entities.Session{
		ID:         sessionID.String(),
		UserID:     userID.String(),
		TokenHash:  hashToken(token),
		ExpiresAt:  expiresAt,
		LastUsedAt: &now,
		UserAgent:  &userAgent,
		IP:         &ipAddress,
	}

	if err := h.sessionRepo.Create(c.Request.Context(), session); err != nil {
		responses.RenderSimpleError(c, http.StatusInternalServerError, responses.ErrorCodeInternalError, "Failed to create session", err.Error())
		return
	}

	// Get user permissions for response
	subject, err := h.authService.AuthenticateUser(c.Request.Context(), userID)
	if err != nil {
		// Not critical, continue without permissions
		// Set default based on whether organization was created
		if organizationID != nil {
			subject = &auth.Subject{
				ID:             userID.String(),
				OrganizationID: organizationID,
				Roles:          []string{"admin"},
				Permissions:    []string{"*"},
			}
		} else {
			subject = &auth.Subject{
				ID:             userID.String(),
				OrganizationID: nil,
				Roles:          []string{"customer"},
				Permissions:    []string{"license.read", "license.validate"},
			}
		}
	}

	// Return login response
	response := LoginResponse{
		Token:     token,
		TokenType: "Bearer",
		ExpiresIn: int(time.Until(expiresAt).Seconds()),
		User: &UserInfo{
			ID:             user.ID,
			Email:          user.Email,
			FirstName:      user.FirstName,
			LastName:       user.LastName,
			OrganizationID: organizationID,
			Roles:          subject.Roles,
			Permissions:    subject.Permissions,
		},
		Subject: subject,
	}

	c.JSON(http.StatusCreated, response)
}

// Helper function to generate slug from name
func generateSlug(name string) string {
	// Simple slug generation - in production use a proper slug library
	slug := strings.ToLower(name)
	slug = strings.ReplaceAll(slug, " ", "-")
	slug = strings.ReplaceAll(slug, "_", "-")
	// Remove non-alphanumeric characters except hyphen
	// This is simplified - use a proper regex in production
	return slug
}

// Helper function to hash token for storage
func hashToken(token string) string {
	// Use SHA256 for token hashing
	h := sha256.New()
	h.Write([]byte(token))
	return fmt.Sprintf("%x", h.Sum(nil))
}
package handlers

import (
	"github.com/google/uuid"
)

// parseUUID parses a string to UUID, returns zero UUID on error
func parseUUID(s string) uuid.UUID {
	id, err := uuid.Parse(s)
	if err != nil {
		return uuid.Nil
	}
	return id
}

// stringPtr returns a pointer to the string value
func stringPtr(s string) *string {
	return &s
}

// intPtr returns a pointer to the int value
func intPtr(i int) *int {
	return &i
}
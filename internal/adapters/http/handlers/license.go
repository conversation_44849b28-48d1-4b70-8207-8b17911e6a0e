package handlers

import (
	"context"
	"crypto/rand"
	"encoding/hex"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/gokeys/gokeys/internal/domain/repositories"
	licenseService "github.com/gokeys/gokeys/internal/domain/services/license"
	"github.com/google/uuid"
)

// === LICENSE KEY GENERATION CONSTANTS ===
// Các hằng số cho việc tạo license key (mapping từ Ruby keygen-api)
const (
	LicenseKeyLength    = 16         // bytes cho SecureRandom.hex(16) - tạo 32 ký tự hex
	LicenseKeyGroupSize = 6          // số ký tự per group khi format key (XXXXXX-XXXXXX-...)
	LicenseKeyHexLength = 32         // tổng số ký tự hex (16 bytes * 2)
	MaxIntegerValue     = ********** // int32 max để tránh overflow trong usage tracking
)

// === HTTP ERROR MESSAGES ===
// C<PERSON><PERSON> thông báo lỗi chuẩn cho license operations
const (
	ErrInvalidLicenseID  = "invalid license ID"                                            // License ID không phải UUID hợp lệ
	ErrLicenseNotFound   = "license not found"                                             // Không tìm thấy license
	ErrInvalidRequest    = "invalid request"                                               // Request body không hợp lệ
	ErrAlreadySuspended  = "license is already suspended"                                  // License đã bị suspend rồi
	ErrNotSuspended      = "license is not suspended"                                      // License không ở trạng thái suspended
	ErrCannotRenew       = "cannot be renewed because the policy does not have a duration" // Policy không có duration nên không thể renew
	ErrIncrementTooLarge = "integer is too large"                                          // Increment value quá lớn
	ErrIncrementNegative = "increment must be positive"                                    // Increment phải là số dương
	ErrIntegerOverflow   = "increment would cause integer overflow"                        // Increment sẽ gây overflow
	ErrDecrementNegative = "decrement must be positive"                                    // Decrement phải là số dương
	ErrDecrementTooLarge = "decrement is too large"                                        // Decrement value quá lớn
)

// === REQUEST/RESPONSE TYPES ===
// Các struct định nghĩa request/response cho type safety và validation
type (
	// CreateLicenseRequest đại diện cho request payload khi tạo license mới
	// Mapping từ Ruby keygen-api typed_params với tất cả fields có thể
	CreateLicenseRequest struct {
		// === CORE ATTRIBUTES ===
		ID        *string    `json:"id"`        // Custom UUID cho license (optional)
		Name      *string    `json:"name"`      // Tên license do user đặt
		Key       *string    `json:"key"`       // Custom license key (optional, sẽ auto-generate nếu không có)
		Protected *bool      `json:"protected"` // License có được bảo vệ không
		Suspended *bool      `json:"suspended"` // License có bị suspend không
		Expiry    *time.Time `json:"expiry"`    // Thời điểm hết hạn (optional)

		// === LIMIT OVERRIDES ===
		// Các override limits từ policy (optional, sẽ dùng policy defaults nếu không set)
		MaxMachinesOverride  *int `json:"max_machines"`  // Override số máy tối đa
		MaxCoresOverride     *int `json:"max_cores"`     // Override số cores tối đa
		MaxUsesOverride      *int `json:"max_uses"`      // Override số lần sử dụng tối đa
		MaxProcessesOverride *int `json:"max_processes"` // Override số processes tối đa
		MaxUsersOverride     *int `json:"max_users"`     // Override số users tối đa

		// === FLEXIBLE DATA ===
		Metadata map[string]interface{} `json:"metadata"` // Custom metadata key-value

		// === RELATIONSHIPS ===
		PolicyID *string `json:"policy_id" binding:"required"` // Policy ID (bắt buộc)
		OwnerID  *string `json:"owner_id"`                     // User sở hữu license (optional)
		GroupID  *string `json:"group_id"`                     // Group chứa license (optional)
	}

	// UpdateLicenseRequest đại diện cho request payload khi update license
	// Tất cả fields đều optional, chỉ update những fields được cung cấp
	UpdateLicenseRequest struct {
		// === CORE ATTRIBUTES ===
		Name      *string    `json:"name"`      // Tên license mới
		Protected *bool      `json:"protected"` // Thay đổi protection status
		Suspended *bool      `json:"suspended"` // Thay đổi suspension status
		Expiry    *time.Time `json:"expiry"`    // Thay đổi thời điểm hết hạn

		// === LIMIT OVERRIDES ===
		// Update các override limits (set nil để remove override, dùng policy default)
		MaxMachinesOverride  *int `json:"max_machines"`  // Update override số máy tối đa
		MaxCoresOverride     *int `json:"max_cores"`     // Update override số cores tối đa
		MaxUsesOverride      *int `json:"max_uses"`      // Update override số lần sử dụng tối đa
		MaxProcessesOverride *int `json:"max_processes"` // Update override số processes tối đa
		MaxUsersOverride     *int `json:"max_users"`     // Update override số users tối đa

		// === FLEXIBLE DATA ===
		Metadata map[string]interface{} `json:"metadata"` // Update custom metadata
	}

	// UsageActionRequest đại diện cho request increment/decrement usage
	// Dùng cho tracking số lần sử dụng license (Ruby: increment_uses/decrement_uses)
	UsageActionRequest struct {
		Meta struct {
			Increment *int `json:"increment"` // Số lượng cần tăng (phải > 0)
			Decrement *int `json:"decrement"` // Số lượng cần giảm (phải > 0)
		} `json:"meta"`
	}

	// ValidationRequest đại diện cho license validation request
	// Dùng để validate license key và fingerprint (Ruby: license validation)
	ValidationRequest struct {
		Key         string                 `json:"key" binding:"required"` // License key cần validate (bắt buộc)
		Fingerprint *string                `json:"fingerprint"`            // Machine fingerprint (optional)
		Metadata    map[string]interface{} `json:"metadata"`               // Additional metadata cho validation
	}

	// ValidationByKeyRequest represents validation by key request
	ValidationByKeyRequest struct {
		Key   string `json:"key" binding:"required"`
		Scope *struct {
			Product      *string   `json:"product"`
			Policy       *string   `json:"policy"`
			User         *string   `json:"user"`
			Machine      *string   `json:"machine"`
			Fingerprint  *string   `json:"fingerprint"`
			Fingerprints *[]string `json:"fingerprints"`
			Components   *[]string `json:"components"`
			Entitlements *[]string `json:"entitlements"`
			Environment  *string   `json:"environment"`
		} `json:"scope"`
		Metadata map[string]interface{} `json:"metadata"`
	}
)

// LicenseHandler xử lý các HTTP requests liên quan đến license
// License trong keygen-api là core entity chứa license key và các rules
// Handler này sử dụng các services có sẵn để xử lý business logic phức tạp
type LicenseHandler struct {
	licenseRepo       repositories.LicenseRepository    // Repository để CRUD operations với license
	validationService *licenseService.ValidationService // Service để validate license keys và policies
	checkoutService   *licenseService.CheckoutService   // Service để checkout/checkin licenses
	lookupService     *licenseService.LookupService     // Service để lookup licenses theo various criteria
}

// NewLicenseHandler tạo một license handler mới
// Cần tất cả services để xử lý đầy đủ license operations
// Services này đã implement business logic phức tạp từ Ruby keygen-api
func NewLicenseHandler(
	licenseRepo repositories.LicenseRepository, // Repository cho database operations
	validationService *licenseService.ValidationService, // Service cho license validation logic
	checkoutService *licenseService.CheckoutService, // Service cho checkout/checkin operations
	lookupService *licenseService.LookupService, // Service cho license lookup operations
) *LicenseHandler {
	return &LicenseHandler{
		licenseRepo:       licenseRepo,
		validationService: validationService,
		checkoutService:   checkoutService,
		lookupService:     lookupService,
	}
}

// === HELPER FUNCTIONS ===
// Các utility functions để clean Go code và error handling

// parseUUID safely parse UUID string và trả về appropriate error response
// Dùng chung cho tất cả methods cần parse UUID parameters
func (h *LicenseHandler) parseUUID(c *gin.Context, uuidStr, fieldName string) (uuid.UUID, bool) {
	parsed, err := uuid.Parse(uuidStr)
	if err != nil {
		// Use consistent error message format for backward compatibility
		errorMsg := ErrInvalidLicenseID
		if fieldName != "license_id" {
			errorMsg = fmt.Sprintf("invalid %s", fieldName)
		}

		c.JSON(http.StatusBadRequest, gin.H{
			"error":  errorMsg,
			"source": gin.H{"pointer": fmt.Sprintf("/data/attributes/%s", fieldName)},
		})
		return uuid.Nil, false
	}
	return parsed, true
}

// getLicenseByID safely retrieves a license by ID with error handling
func (h *LicenseHandler) getLicenseByID(c *gin.Context, licenseID uuid.UUID) (*entities.License, bool) {
	license, err := h.licenseRepo.GetByID(c.Request.Context(), licenseID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error":  ErrLicenseNotFound,
			"source": gin.H{"pointer": "/data/id"},
		})
		return nil, false
	}
	return license, true
}

// updateLicense safely updates a license with error handling
func (h *LicenseHandler) updateLicense(c *gin.Context, license *entities.License) bool {
	license.UpdatedAt = time.Now()
	if err := h.licenseRepo.Update(c.Request.Context(), license); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":  "failed to update license",
			"reason": err.Error(),
		})
		return false
	}
	return true
}

// validateUsageIncrement validates increment value with Go's type safety
func (h *LicenseHandler) validateUsageIncrement(c *gin.Context, increment int, currentUsage int) bool {
	if increment < 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":  ErrIncrementNegative,
			"source": gin.H{"pointer": "/meta/increment"},
		})
		return false
	}

	if increment > MaxIntegerValue {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":  ErrIncrementTooLarge,
			"source": gin.H{"pointer": "/meta/increment"},
		})
		return false
	}

	if currentUsage > MaxIntegerValue-increment {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":  ErrIntegerOverflow,
			"source": gin.H{"pointer": "/data/attributes/uses"},
		})
		return false
	}

	return true
}

// validateUsageDecrement validates decrement value with Go's type safety
func (h *LicenseHandler) validateUsageDecrement(c *gin.Context, decrement int) bool {
	if decrement < 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":  ErrDecrementNegative,
			"source": gin.H{"pointer": "/meta/decrement"},
		})
		return false
	}

	if decrement > MaxIntegerValue {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":  ErrDecrementTooLarge,
			"source": gin.H{"pointer": "/meta/decrement"},
		})
		return false
	}

	return true
}

// generateLicenseKey generates a cryptographically secure license key
// following Ruby's SecureRandom.hex(16).upcase.scan(/.{1,6}/).join("-") pattern
func generateLicenseKey() (string, error) {
	randomBytes := make([]byte, LicenseKeyLength)

	if _, err := rand.Read(randomBytes); err != nil {
		return "", fmt.Errorf("failed to generate random bytes: %w", err)
	}

	// Convert to hex string and uppercase (like Ruby SecureRandom.hex(16).upcase)
	hexStr := strings.ToUpper(hex.EncodeToString(randomBytes))

	// Split every 6 characters and join with dash (like Ruby scan(/.{1,6}/).join("-"))
	parts := make([]string, 0, (LicenseKeyHexLength+LicenseKeyGroupSize-1)/LicenseKeyGroupSize)
	for i := 0; i < len(hexStr); i += LicenseKeyGroupSize {
		end := i + LicenseKeyGroupSize
		if end > len(hexStr) {
			end = len(hexStr)
		}
		parts = append(parts, hexStr[i:end])
	}

	return strings.Join(parts, "-"), nil
}

// LicenseOption represents a functional option for license creation
type LicenseOption func(*entities.License)

// WithLicenseName sets the license name
func WithLicenseName(name string) LicenseOption {
	return func(l *entities.License) {
		l.Name = &name
	}
}

// WithLicenseKey sets the license key
func WithLicenseKey(key string) LicenseOption {
	return func(l *entities.License) {
		l.Key = key
	}
}

// WithLicenseProtected sets the protected flag
func WithLicenseProtected(protected bool) LicenseOption {
	return func(l *entities.License) {
		l.Protected = protected
	}
}

// WithLicenseExpiry sets the expiry date
func WithLicenseExpiry(expiry time.Time) LicenseOption {
	return func(l *entities.License) {
		l.ExpiresAt = &expiry
	}
}

// WithLicenseMetadata sets the metadata
func WithLicenseMetadata(metadata map[string]interface{}) LicenseOption {
	return func(l *entities.License) {
		l.Metadata = metadata
	}
}

// createLicenseEntity creates a new license entity with functional options
func (h *LicenseHandler) createLicenseEntity(
	organizationID, productID, policyID string,
	options ...LicenseOption,
) (*entities.License, error) {
	now := time.Now()

	license := &entities.License{
		ID:             uuid.New().String(),
		OrganizationID: organizationID,
		ProductID:      productID,
		PolicyID:       policyID,
		Status:         entities.LicenseStatusActive,
		OwnerType:      entities.LicenseOwnerTypeUser,
		OwnerID:        organizationID, // Default to organization
		Protected:      false,          // Default value
		Suspended:      false,          // Default value
		Uses:           0,              // Default value
		CreatedAt:      now,
		UpdatedAt:      now,
	}

	// Apply functional options
	for _, option := range options {
		option(license)
	}

	// Auto-generate key if not provided
	if license.Key == "" {
		key, err := generateLicenseKey()
		if err != nil {
			return nil, fmt.Errorf("failed to generate license key: %w", err)
		}
		license.Key = key
	}

	return license, nil
}

// ValidationResult represents the result of a license validation
type ValidationResult struct {
	Valid          bool                   `json:"valid"`
	License        *entities.License      `json:"license,omitempty"`
	Detail         string                 `json:"detail"`
	Code           string                 `json:"code"`
	ValidationTime time.Time              `json:"ts"`
	Meta           map[string]interface{} `json:"meta,omitempty"`
}

// Validator interface for different validation strategies
type Validator interface {
	Validate(ctx context.Context, license *entities.License) ValidationResult
}

// BasicValidator implements basic license validation
type BasicValidator struct{}

// Validate performs basic license validation
func (v *BasicValidator) Validate(ctx context.Context, license *entities.License) ValidationResult {
	now := time.Now()

	result := ValidationResult{
		ValidationTime: now,
		Meta:           make(map[string]interface{}),
	}

	if license == nil {
		result.Valid = false
		result.Detail = "license not found"
		result.Code = "NOT_FOUND"
		return result
	}

	// Check if suspended
	if license.Suspended {
		result.Valid = false
		result.Detail = "license is suspended"
		result.Code = "SUSPENDED"
		result.License = license
		return result
	}

	// Check if expired
	if license.ExpiresAt != nil && license.ExpiresAt.Before(now) {
		result.Valid = false
		result.Detail = "license has expired"
		result.Code = "EXPIRED"
		result.License = license
		return result
	}

	// Check usage limits
	if license.MaxUsesOverride != nil && license.Uses >= *license.MaxUsesOverride {
		result.Valid = false
		result.Detail = "license usage limit exceeded"
		result.Code = "USAGE_EXCEEDED"
		result.License = license
		return result
	}

	// License is valid
	result.Valid = true
	result.Detail = "license is valid"
	result.Code = "VALID"
	result.License = license

	return result
}

// performValidation performs license validation using the validator pattern
func (h *LicenseHandler) performValidation(c *gin.Context, license *entities.License, validator Validator) {
	result := validator.Validate(c.Request.Context(), license)

	// Set response status based on validation result
	status := http.StatusOK
	if !result.Valid {
		status = http.StatusUnauthorized
	}

	c.JSON(status, result)
}

// ListProductLicenses lists all licenses for a specific product
func (h *LicenseHandler) ListProductLicenses(c *gin.Context) {
	organizationID := c.Param("organization_id")
	productID := c.Param("product_id")

	// Parse query parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))

	// Build filter
	filter := repositories.DefaultListFilter()
	filter.Page = page
	filter.PageSize = pageSize
	filter.Filters["organization_id = ?"] = organizationID
	filter.Filters["product_id = ?"] = productID

	// Get licenses
	licenses, total, err := h.licenseRepo.List(c.Request.Context(), filter)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to get licenses", "reason": err.Error()})
		return
	}

	// Calculate pagination
	totalPages := (int(total) + pageSize - 1) / pageSize

	c.JSON(http.StatusOK, gin.H{
		"licenses": licenses,
		"pagination": gin.H{
			"page":        page,
			"page_size":   pageSize,
			"total":       total,
			"total_pages": totalPages,
		},
	})
}

// GetLicense retrieves a specific license by ID
func (h *LicenseHandler) GetLicense(c *gin.Context) {
	licenseID := c.Param("license_id")

	licenseUUID, err := uuid.Parse(licenseID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid license ID"})
		return
	}

	license, err := h.licenseRepo.GetByID(c.Request.Context(), licenseUUID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "license not found"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"license": license})
}

// ValidateLicense validates a license using the validation service
func (h *LicenseHandler) ValidateLicense(c *gin.Context) {
	var requestData struct {
		Key         string                 `json:"key" binding:"required"`
		Fingerprint *string                `json:"fingerprint"`
		Metadata    map[string]interface{} `json:"metadata"`
	}

	if err := c.ShouldBindJSON(&requestData); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid request", "reason": err.Error()})
		return
	}

	// Use validation service
	result, err := h.validationService.ValidateLicense(c.Request.Context(), requestData.Key, requestData.Fingerprint)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "validation failed", "reason": err.Error()})
		return
	}

	// Return response based on validation result
	if result.Valid {
		c.JSON(http.StatusOK, gin.H{
			"valid":   true,
			"license": result.License,
			"detail":  string(result.Detail),
			"code":    string(result.Code),
			"meta": gin.H{
				"ts":    result.ValidationTime,
				"valid": result.Valid,
			},
		})
	} else {
		c.JSON(http.StatusUnauthorized, gin.H{
			"valid":  false,
			"error":  "validation failed",
			"detail": string(result.Detail),
			"code":   string(result.Code),
			"meta": gin.H{
				"ts":    result.ValidationTime,
				"valid": result.Valid,
			},
		})
	}
}

// ValidateLicenseByKey validates a license by key with full scope options
func (h *LicenseHandler) ValidateLicenseByKey(c *gin.Context) {
	var requestData struct {
		Key   string `json:"key" binding:"required"`
		Scope *struct {
			Product      *string  `json:"product"`
			Policy       *string  `json:"policy"`
			User         *string  `json:"user"`
			Machine      *string  `json:"machine"`
			Fingerprint  *string  `json:"fingerprint"`
			Fingerprints []string `json:"fingerprints"`
			Components   []string `json:"components"`
			Entitlements []string `json:"entitlements"`
			Checksum     *string  `json:"checksum"`
			Version      *string  `json:"version"`
		} `json:"scope"`
	}

	if err := c.ShouldBindJSON(&requestData); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid request", "reason": err.Error()})
		return
	}

	// Build validation scope
	scope := &licenseService.ValidationScope{}
	if requestData.Scope != nil {
		if requestData.Scope.Product != nil {
			scope.Product = requestData.Scope.Product
		}
		if requestData.Scope.Policy != nil {
			scope.Policy = requestData.Scope.Policy
		}
		if requestData.Scope.User != nil {
			scope.User = requestData.Scope.User
		}
		if requestData.Scope.Machine != nil {
			scope.Machine = requestData.Scope.Machine
		}
		if requestData.Scope.Fingerprint != nil {
			scope.Fingerprint = requestData.Scope.Fingerprint
		}
		if len(requestData.Scope.Fingerprints) > 0 {
			scope.Fingerprints = requestData.Scope.Fingerprints
		}
		if len(requestData.Scope.Components) > 0 {
			scope.Components = requestData.Scope.Components
		}
		if len(requestData.Scope.Entitlements) > 0 {
			scope.Entitlements = requestData.Scope.Entitlements
		}
		if requestData.Scope.Checksum != nil {
			scope.Checksum = requestData.Scope.Checksum
		}
		if requestData.Scope.Version != nil {
			scope.Version = requestData.Scope.Version
		}
	}

	// Use validation service with options
	result, err := h.validationService.ValidateLicenseWithOptions(c.Request.Context(), requestData.Key, &licenseService.ValidationOptions{
		Scope: scope,
	})
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "validation failed", "reason": err.Error()})
		return
	}

	// Return validation result
	response := gin.H{
		"valid":  result.Valid,
		"detail": string(result.Detail),
		"code":   string(result.Code),
		"meta": gin.H{
			"ts":    result.ValidationTime,
			"valid": result.Valid,
		},
	}

	if result.License != nil {
		response["license"] = result.License
	}

	c.JSON(http.StatusOK, response)
}

// QuickValidateLicenseByID performs quick validation by license ID
func (h *LicenseHandler) QuickValidateLicenseByID(c *gin.Context) {
	licenseID := c.Param("license_id")

	licenseUUID, err := uuid.Parse(licenseID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid license ID"})
		return
	}

	license, err := h.licenseRepo.GetByID(c.Request.Context(), licenseUUID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "license not found"})
		return
	}

	// Use quick validation
	valid, err := h.validationService.ValidateLicenseQuick(c.Request.Context(), license.Key)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "validation failed", "reason": err.Error()})
		return
	}

	response := gin.H{
		"valid":   valid,
		"license": license,
		"meta": gin.H{
			"ts":    time.Now(),
			"valid": valid,
		},
	}

	if valid {
		response["detail"] = "license is valid"
		response["code"] = "VALID"
	} else {
		response["detail"] = "license is invalid"
		response["code"] = "INVALID"
	}

	c.JSON(http.StatusOK, response)
}

// CheckoutLicense generates a license certificate using checkout service
func (h *LicenseHandler) CheckoutLicense(c *gin.Context) {
	licenseID := c.Param("license_id")

	licenseUUID, err := uuid.Parse(licenseID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid license ID"})
		return
	}

	license, err := h.licenseRepo.GetByID(c.Request.Context(), licenseUUID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "license not found"})
		return
	}

	// Parse checkout options from request
	var requestData struct {
		Include   []string `json:"include"`
		TTL       *int     `json:"ttl"`
		Encrypted bool     `json:"encrypted"`
		Algorithm *string  `json:"algorithm"`
	}
	c.ShouldBindJSON(&requestData)

	// Use checkout service
	options := &licenseService.CheckoutOptions{
		Include:   requestData.Include,
		TTL:       requestData.TTL,
		Encrypted: requestData.Encrypted,
		Algorithm: requestData.Algorithm,
	}

	licenseFile, err := h.checkoutService.CheckoutLicense(c.Request.Context(), license, options)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "checkout failed", "reason": err.Error()})
		return
	}

	// Update license checkout timestamp
	license.LastCheckOutAt = &licenseFile.IssuedAt
	h.licenseRepo.Update(c.Request.Context(), license)

	// Return certificate as file download
	c.Header("Content-Type", "application/x-pem-file")
	c.Header("Content-Disposition", `attachment; filename="`+license.Key+`.lic"`)
	c.String(http.StatusOK, licenseFile.Certificate)
}

// GetLicenseInfo gets license information by key
func (h *LicenseHandler) GetLicenseInfo(c *gin.Context) {
	key := c.Param("key")

	info, err := h.validationService.GetLicenseInfo(c.Request.Context(), key)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid license key", "reason": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"license": info})
}

// InvalidateLicenseCache invalidates cache for a license
func (h *LicenseHandler) InvalidateLicenseCache(c *gin.Context) {
	var requestData struct {
		Key string `json:"key" binding:"required"`
	}

	if err := c.ShouldBindJSON(&requestData); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid request", "reason": err.Error()})
		return
	}

	err := h.validationService.InvalidateCache(c.Request.Context(), requestData.Key)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to invalidate cache", "reason": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "cache invalidated successfully"})
}

// CreateLicense tạo một license mới theo patterns của keygen-api
// License là core entity chứa license key và các rules từ policy
// Endpoint: POST /organizations/{org_id}/products/{product_id}/licenses
func (h *LicenseHandler) CreateLicense(c *gin.Context) {
	// Lấy organization_id và product_id từ URL path parameters
	// License phải thuộc về organization và product cụ thể
	organizationID := c.Param("organization_id")
	productID := c.Param("product_id")

	// === REQUEST PARSING ===
	// Parse request body sử dụng typed struct để type safety
	var requestData CreateLicenseRequest
	if err := c.ShouldBindJSON(&requestData); err != nil {
		// Trả về lỗi 400 nếu request body không hợp lệ
		c.JSON(http.StatusBadRequest, gin.H{
			"error":  ErrInvalidRequest,
			"reason": err.Error(),
		})
		return
	}

	// === POLICY VALIDATION ===
	// Validate policy tồn tại và thuộc về product này
	policyUUID, err := uuid.Parse(*requestData.PolicyID)
	if err != nil {
		// Policy ID phải là UUID hợp lệ
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid policy ID"})
		return
	}

	// TODO: Validate policy belongs to product
	// policy, err := h.policyRepo.GetByID(c.Request.Context(), policyUUID)
	// Cần check policy.ProductID == productID để đảm bảo security

	// === FUNCTIONAL OPTIONS CONSTRUCTION ===
	// Xây dựng functional options từ request data
	// Pattern này cho phép flexible license creation với optional fields
	var options []LicenseOption

	// Optional license name
	if requestData.Name != nil {
		options = append(options, WithLicenseName(*requestData.Name))
	}

	// Optional custom license key (nếu không có sẽ auto-generate)
	if requestData.Key != nil && *requestData.Key != "" {
		options = append(options, WithLicenseKey(*requestData.Key))
	}

	// Optional protection flag
	if requestData.Protected != nil {
		options = append(options, WithLicenseProtected(*requestData.Protected))
	}

	// Optional expiry date
	if requestData.Expiry != nil {
		options = append(options, WithLicenseExpiry(*requestData.Expiry))
	}

	// Optional custom metadata
	if requestData.Metadata != nil {
		options = append(options, WithLicenseMetadata(requestData.Metadata))
	}

	// === LICENSE ENTITY CREATION ===
	// Tạo license entity sử dụng functional options pattern
	// Pattern này cho phép flexible construction với clean code
	license, err := h.createLicenseEntity(organizationID, productID, policyUUID.String(), options...)
	if err != nil {
		// Trả về lỗi 500 nếu có vấn đề khi tạo entity
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":  "failed to create license entity",
			"reason": err.Error(),
		})
		return
	}

	// === ADDITIONAL FIELDS APPLICATION ===
	// Apply các fields không được cover bởi functional options
	// Các fields này cần special handling hoặc validation

	// Custom license ID (nếu không có sẽ dùng auto-generated UUID)
	if requestData.ID != nil {
		license.ID = *requestData.ID
	}

	// Suspension status (license có bị suspend không)
	if requestData.Suspended != nil {
		license.Suspended = *requestData.Suspended
	}

	// Owner assignment (user sở hữu license)
	if requestData.OwnerID != nil {
		license.OwnerID = *requestData.OwnerID
		license.OwnerType = entities.LicenseOwnerTypeUser // Assume user owner (có thể là group)
	}

	// === LIMIT OVERRIDES ===
	// Apply các override limits từ request (override policy defaults)
	if requestData.MaxUsesOverride != nil {
		license.MaxUsesOverride = requestData.MaxUsesOverride
	}
	if requestData.MaxMachinesOverride != nil {
		license.MaxMachinesOverride = requestData.MaxMachinesOverride
	}
	if requestData.MaxCoresOverride != nil {
		license.MaxCoresOverride = requestData.MaxCoresOverride
	}
	if requestData.MaxProcessesOverride != nil {
		license.MaxProcessesOverride = requestData.MaxProcessesOverride
	}
	if requestData.MaxUsersOverride != nil {
		license.MaxUsersOverride = requestData.MaxUsersOverride
	}

	if err := h.licenseRepo.Create(c.Request.Context(), license); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to create license", "reason": err.Error()})
		return
	}

	// TODO: Broadcast license.created event (Ruby: BroadcastEventService.call)

	c.JSON(http.StatusCreated, gin.H{"license": license})
}

// UpdateLicense updates an existing license following keygen-api patterns
func (h *LicenseHandler) UpdateLicense(c *gin.Context) {
	licenseID := c.Param("license_id")

	licenseUUID, err := uuid.Parse(licenseID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid license ID"})
		return
	}

	license, err := h.licenseRepo.GetByID(c.Request.Context(), licenseUUID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "license not found"})
		return
	}

	var requestData struct {
		// Update fields mapping from Ruby typed_params for update action
		Name                 *string                 `json:"name"`
		Protected            *bool                   `json:"protected"`
		Suspended            *bool                   `json:"suspended"`
		Expiry               *time.Time              `json:"expiry"`
		MaxMachinesOverride  *int                    `json:"max_machines"`
		MaxCoresOverride     *int                    `json:"max_cores"`
		MaxUsesOverride      *int                    `json:"max_uses"`
		MaxProcessesOverride *int                    `json:"max_processes"`
		MaxUsersOverride     *int                    `json:"max_users"`
		Metadata             *map[string]interface{} `json:"metadata"`
	}

	if err := c.ShouldBindJSON(&requestData); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid request", "reason": err.Error()})
		return
	}

	// Update fields if provided (Ruby: license.update(license_params))
	if requestData.Name != nil {
		license.Name = requestData.Name
	}
	if requestData.Protected != nil {
		license.Protected = *requestData.Protected
	}
	if requestData.Suspended != nil {
		license.Suspended = *requestData.Suspended
	}
	if requestData.Expiry != nil {
		license.ExpiresAt = requestData.Expiry
	}
	if requestData.MaxMachinesOverride != nil {
		license.MaxMachinesOverride = requestData.MaxMachinesOverride
	}
	if requestData.MaxCoresOverride != nil {
		license.MaxCoresOverride = requestData.MaxCoresOverride
	}
	if requestData.MaxUsesOverride != nil {
		license.MaxUsesOverride = requestData.MaxUsesOverride
	}
	if requestData.MaxProcessesOverride != nil {
		license.MaxProcessesOverride = requestData.MaxProcessesOverride
	}
	if requestData.MaxUsersOverride != nil {
		license.MaxUsersOverride = requestData.MaxUsersOverride
	}
	if requestData.Metadata != nil {
		license.Metadata = entities.Metadata(*requestData.Metadata)
	}

	if err := h.licenseRepo.Update(c.Request.Context(), license); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to update license", "reason": err.Error()})
		return
	}

	// TODO: Broadcast license.updated event (Ruby: BroadcastEventService.call)

	c.JSON(http.StatusOK, gin.H{"license": license})
}

// DeleteLicense soft deletes a license following keygen-api patterns
func (h *LicenseHandler) DeleteLicense(c *gin.Context) {
	licenseID := c.Param("license_id")

	licenseUUID, err := uuid.Parse(licenseID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid license ID"})
		return
	}

	_, err = h.licenseRepo.GetByID(c.Request.Context(), licenseUUID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "license not found"})
		return
	}

	// TODO: Broadcast license.deleted event (Ruby: BroadcastEventService.call)

	if err := h.licenseRepo.Delete(c.Request.Context(), licenseUUID); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to delete license", "reason": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "license deleted successfully"})
}

// GetLicenseStats returns license statistics
func (h *LicenseHandler) GetLicenseStats(c *gin.Context) {
	licenseID := c.Param("license_id")

	licenseUUID, err := uuid.Parse(licenseID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid license ID"})
		return
	}

	license, err := h.licenseRepo.GetByID(c.Request.Context(), licenseUUID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "license not found"})
		return
	}

	// TODO: Calculate actual license statistics from related entities
	stats := gin.H{
		"license_id": license.ID,
		"machines": gin.H{
			"total":    license.MachinesCount,
			"active":   0, // TODO: count active machines
			"inactive": 0, // TODO: count inactive machines
		},
		"activations": gin.H{
			"total": license.Uses,
			"max": func() interface{} {
				if license.MaxUsesOverride != nil {
					return *license.MaxUsesOverride
				}
				return "unlimited"
			}(),
			"remaining": func() interface{} {
				if license.MaxUsesOverride != nil {
					remaining := *license.MaxUsesOverride - license.Uses
					if remaining < 0 {
						return 0
					}
					return remaining
				}
				return "unlimited"
			}(),
		},
		"usage": gin.H{
			"cores":     license.MachinesCoreCount,
			"processes": 0, // TODO: count processes
		},
		"status": func() string {
			if license.Suspended {
				return "suspended"
			}
			if license.ExpiresAt != nil && license.ExpiresAt.Before(time.Now()) {
				return "expired"
			}
			return "active"
		}(),
	}

	c.JSON(http.StatusOK, gin.H{"stats": stats})
}

// GetLicenseMachines returns all machines associated with a license
func (h *LicenseHandler) GetLicenseMachines(c *gin.Context) {
	licenseID := c.Param("license_id")

	licenseUUID, err := uuid.Parse(licenseID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid license ID"})
		return
	}

	// Parse pagination parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))

	// Build filter for machines by license
	filter := repositories.DefaultListFilter()
	filter.Page = page
	filter.PageSize = pageSize
	filter.Filters["license_id = ?"] = licenseUUID.String()

	// TODO: Get machines from machine repository when it has List method
	// machines, total, err := h.machineRepo.List(c.Request.Context(), filter)

	// Placeholder response
	totalPages := 0
	c.JSON(http.StatusOK, gin.H{
		"license_id": licenseID,
		"machines":   []interface{}{},
		"pagination": gin.H{
			"page":        page,
			"page_size":   pageSize,
			"total":       0,
			"total_pages": totalPages,
		},
	})
}

// SuspendLicense suspends a license following keygen-api permits#suspend pattern
func (h *LicenseHandler) SuspendLicense(c *gin.Context) {
	licenseID := c.Param("license_id")

	// Parse and validate license ID using helper
	licenseUUID, ok := h.parseUUID(c, licenseID, "license_id")
	if !ok {
		return
	}

	// Get license using helper
	license, ok := h.getLicenseByID(c, licenseUUID)
	if !ok {
		return
	}

	// Check if already suspended (Ruby: if license.suspended?)
	if license.Suspended {
		c.JSON(http.StatusUnprocessableEntity, gin.H{
			"error":  ErrAlreadySuspended,
			"source": gin.H{"pointer": "/data/attributes/suspended"},
		})
		return
	}

	// Suspend the license (Ruby: license.suspend!)
	license.Suspended = true

	// Update license using helper
	if !h.updateLicense(c, license) {
		return
	}

	// TODO: Broadcast license.suspended event (Ruby: BroadcastEventService.call)

	c.JSON(http.StatusOK, gin.H{"license": license})
}

// ReinstateLicense reinstates a suspended license following keygen-api permits#reinstate pattern
func (h *LicenseHandler) ReinstateLicense(c *gin.Context) {
	licenseID := c.Param("license_id")

	// Parse and validate license ID using helper
	licenseUUID, ok := h.parseUUID(c, licenseID, "license_id")
	if !ok {
		return
	}

	// Get license using helper
	license, ok := h.getLicenseByID(c, licenseUUID)
	if !ok {
		return
	}

	// Check if not suspended (Ruby: if !license.suspended?)
	if !license.Suspended {
		c.JSON(http.StatusUnprocessableEntity, gin.H{
			"error":  ErrNotSuspended,
			"source": gin.H{"pointer": "/data/attributes/suspended"},
		})
		return
	}

	// Reinstate the license (Ruby: license.reinstate!)
	license.Suspended = false

	// Update license using helper
	if !h.updateLicense(c, license) {
		return
	}

	// TODO: Broadcast license.reinstated event (Ruby: BroadcastEventService.call)

	c.JSON(http.StatusOK, gin.H{"license": license})
}

// RevokeLicense revokes a license following keygen-api permits#revoke pattern
func (h *LicenseHandler) RevokeLicense(c *gin.Context) {
	licenseID := c.Param("license_id")

	licenseUUID, err := uuid.Parse(licenseID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid license ID"})
		return
	}

	// Verify license exists before revoking
	_, err = h.licenseRepo.GetByID(c.Request.Context(), licenseUUID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "license not found"})
		return
	}

	// TODO: Broadcast license.revoked event (Ruby: BroadcastEventService.call)

	// Revoke the license by deleting it (Ruby: license.destroy)
	if err := h.licenseRepo.Delete(c.Request.Context(), licenseUUID); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to revoke license", "reason": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "license revoked successfully"})
}

// RenewLicense renews a license following keygen-api permits#renew pattern
func (h *LicenseHandler) RenewLicense(c *gin.Context) {
	licenseID := c.Param("license_id")

	licenseUUID, err := uuid.Parse(licenseID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid license ID"})
		return
	}

	license, err := h.licenseRepo.GetByID(c.Request.Context(), licenseUUID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "license not found"})
		return
	}

	// TODO: Get policy to check duration (Ruby: license.policy.duration.nil?)
	// For now, check if license has expiry (simplified version of Ruby logic)
	if license.ExpiresAt == nil {
		c.JSON(http.StatusUnprocessableEntity, gin.H{
			"error": "cannot be renewed because the policy does not have a duration",
		})
		return
	}

	// Renew the license (Ruby: license.renew!)
	// Simplified version: extend from current expiry by 1 year (Ruby: renew_from_expiry?)
	// TODO: Implement proper policy duration and renewal strategies
	now := time.Now()
	var newExpiry time.Time
	if license.ExpiresAt.After(now) {
		// Not expired: extend from current expiry (Ruby: renew_from_expiry?)
		newExpiry = license.ExpiresAt.AddDate(1, 0, 0)
	} else {
		// Expired: extend from now (Ruby: renew_from_now_if_expired?)
		newExpiry = now.AddDate(1, 0, 0)
	}
	license.ExpiresAt = &newExpiry
	license.UpdatedAt = now

	if err := h.licenseRepo.Update(c.Request.Context(), license); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to renew license", "reason": err.Error()})
		return
	}

	// TODO: Broadcast license.renewed event (Ruby: BroadcastEventService.call)

	c.JSON(http.StatusOK, gin.H{"license": license})
}

// CheckInLicense checks in a license following keygen-api permits#check_in pattern
func (h *LicenseHandler) CheckInLicense(c *gin.Context) {
	licenseID := c.Param("license_id")

	licenseUUID, err := uuid.Parse(licenseID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid license ID"})
		return
	}

	license, err := h.licenseRepo.GetByID(c.Request.Context(), licenseUUID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "license not found"})
		return
	}

	// TODO: Check if policy requires check-in (Ruby: !license.policy.requires_check_in?)
	// For now, assume all licenses can be checked in

	// Check in the license (Ruby: license.check_in!)
	now := time.Now()
	license.LastCheckInAt = &now
	license.UpdatedAt = now

	if err := h.licenseRepo.Update(c.Request.Context(), license); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to check in license", "reason": err.Error()})
		return
	}

	// TODO: Broadcast license.checked-in event (Ruby: BroadcastEventService.call)

	c.JSON(http.StatusOK, gin.H{"license": license})
}

// IncrementUsage increments license usage following keygen-api uses#increment pattern
func (h *LicenseHandler) IncrementUsage(c *gin.Context) {
	licenseID := c.Param("license_id")

	// Parse and validate license ID using helper
	licenseUUID, ok := h.parseUUID(c, licenseID, "license_id")
	if !ok {
		return
	}

	// Get license using helper
	license, ok := h.getLicenseByID(c, licenseUUID)
	if !ok {
		return
	}

	// Parse request using typed struct
	var requestData UsageActionRequest
	if err := c.ShouldBindJSON(&requestData); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":  ErrInvalidRequest,
			"reason": err.Error(),
		})
		return
	}

	// Get increment value, default to 1 (Ruby: use_meta.fetch(:increment, 1))
	increment := 1
	if requestData.Meta.Increment != nil {
		increment = *requestData.Meta.Increment
	}

	// Validate increment using helper
	if !h.validateUsageIncrement(c, increment, license.Uses) {
		return
	}

	// Increment usage with lock (Ruby: license.with_lock 'FOR UPDATE NOWAIT')
	// TODO: Implement proper database locking and conflict detection
	license.Uses += increment

	// Update license using helper
	if !h.updateLicense(c, license) {
		return
	}

	// TODO: Broadcast license.usage.incremented event (Ruby: BroadcastEventService.call)

	c.JSON(http.StatusOK, gin.H{"license": license})
}

// DecrementUsage decrements license usage following keygen-api uses#decrement pattern
func (h *LicenseHandler) DecrementUsage(c *gin.Context) {
	licenseID := c.Param("license_id")

	licenseUUID, err := uuid.Parse(licenseID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid license ID"})
		return
	}

	license, err := h.licenseRepo.GetByID(c.Request.Context(), licenseUUID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "license not found"})
		return
	}

	var requestData struct {
		Meta struct {
			Decrement *int `json:"decrement"`
		} `json:"meta"`
	}

	if err := c.ShouldBindJSON(&requestData); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid request", "reason": err.Error()})
		return
	}

	// Get decrement value, default to 1 (Ruby: use_meta.fetch(:decrement, 1))
	decrement := 1
	if requestData.Meta.Decrement != nil {
		decrement = *requestData.Meta.Decrement
	}

	// Decrement usage with lock (Ruby: license.with_lock 'FOR UPDATE NOWAIT')
	// TODO: Implement proper database locking
	newUsage := license.Uses - decrement
	if newUsage < 0 {
		newUsage = 0 // Prevent negative usage
	}
	license.Uses = newUsage
	license.UpdatedAt = time.Now()

	if err := h.licenseRepo.Update(c.Request.Context(), license); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to decrement usage", "reason": err.Error()})
		return
	}

	// TODO: Broadcast license.usage.decremented event (Ruby: BroadcastEventService.call)

	c.JSON(http.StatusOK, gin.H{"license": license})
}

// ResetUsage resets license usage to zero following keygen-api uses#reset pattern
func (h *LicenseHandler) ResetUsage(c *gin.Context) {
	licenseID := c.Param("license_id")

	licenseUUID, err := uuid.Parse(licenseID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid license ID"})
		return
	}

	license, err := h.licenseRepo.GetByID(c.Request.Context(), licenseUUID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "license not found"})
		return
	}

	// Reset usage to 0 (Ruby: license.update(uses: 0))
	license.Uses = 0
	license.UpdatedAt = time.Now()

	if err := h.licenseRepo.Update(c.Request.Context(), license); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to reset usage", "reason": err.Error()})
		return
	}

	// TODO: Broadcast license.usage.reset event (Ruby: BroadcastEventService.call)

	c.JSON(http.StatusOK, gin.H{"license": license})
}

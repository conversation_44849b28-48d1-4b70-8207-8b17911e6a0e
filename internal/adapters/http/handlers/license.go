package handlers

import (
	"crypto/rand"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/gokeys/gokeys/internal/domain/repositories"
	licenseService "github.com/gokeys/gokeys/internal/domain/services/license"
)

// LicenseHandler handles license-related HTTP requests using existing services
type LicenseHandler struct {
	licenseRepo       repositories.LicenseRepository
	validationService *licenseService.ValidationService
	checkoutService   *licenseService.CheckoutService
	lookupService     *licenseService.LookupService
}

// NewLicenseHandler creates a new license handler
func <PERSON>LicenseHandler(
	licenseRepo repositories.LicenseRepository,
	validationService *licenseService.ValidationService,
	checkoutService *licenseService.CheckoutService,
	lookupService *licenseService.LookupService,
) *LicenseHandler {
	return &LicenseHandler{
		licenseRepo:       licenseRepo,
		validationService: validationService,
		checkoutService:   checkoutService,
		lookupService:     lookupService,
	}
}

// ListProductLicenses lists all licenses for a specific product
func (h *LicenseHandler) ListProductLicenses(c *gin.Context) {
	organizationID := c.Param("organization_id")
	productID := c.Param("product_id")

	// Parse query parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))

	// Build filter
	filter := repositories.DefaultListFilter()
	filter.Page = page
	filter.PageSize = pageSize
	filter.Filters["organization_id = ?"] = organizationID
	filter.Filters["product_id = ?"] = productID

	// Get licenses
	licenses, total, err := h.licenseRepo.List(c.Request.Context(), filter)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to get licenses", "reason": err.Error()})
		return
	}

	// Calculate pagination
	totalPages := (int(total) + pageSize - 1) / pageSize

	c.JSON(http.StatusOK, gin.H{
		"licenses": licenses,
		"pagination": gin.H{
			"page":        page,
			"page_size":   pageSize,
			"total":       total,
			"total_pages": totalPages,
		},
	})
}

// GetLicense retrieves a specific license by ID
func (h *LicenseHandler) GetLicense(c *gin.Context) {
	licenseID := c.Param("license_id")

	licenseUUID, err := uuid.Parse(licenseID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid license ID"})
		return
	}

	license, err := h.licenseRepo.GetByID(c.Request.Context(), licenseUUID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "license not found"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"license": license})
}

// ValidateLicense validates a license using the validation service
func (h *LicenseHandler) ValidateLicense(c *gin.Context) {
	var requestData struct {
		Key         string                 `json:"key" binding:"required"`
		Fingerprint *string                `json:"fingerprint"`
		Metadata    map[string]interface{} `json:"metadata"`
	}

	if err := c.ShouldBindJSON(&requestData); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid request", "reason": err.Error()})
		return
	}

	// Use validation service
	result, err := h.validationService.ValidateLicense(c.Request.Context(), requestData.Key, requestData.Fingerprint)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "validation failed", "reason": err.Error()})
		return
	}

	// Return response based on validation result
	if result.Valid {
		c.JSON(http.StatusOK, gin.H{
			"valid":   true,
			"license": result.License,
			"detail":  string(result.Detail),
			"code":    string(result.Code),
			"meta": gin.H{
				"ts":    result.ValidationTime,
				"valid": result.Valid,
			},
		})
	} else {
		c.JSON(http.StatusUnauthorized, gin.H{
			"valid":  false,
			"error":  "validation failed",
			"detail": string(result.Detail),
			"code":   string(result.Code),
			"meta": gin.H{
				"ts":    result.ValidationTime,
				"valid": result.Valid,
			},
		})
	}
}

// ValidateLicenseByKey validates a license by key with full scope options
func (h *LicenseHandler) ValidateLicenseByKey(c *gin.Context) {
	var requestData struct {
		Key   string `json:"key" binding:"required"`
		Scope *struct {
			Product      *string  `json:"product"`
			Policy       *string  `json:"policy"`
			User         *string  `json:"user"`
			Machine      *string  `json:"machine"`
			Fingerprint  *string  `json:"fingerprint"`
			Fingerprints []string `json:"fingerprints"`
			Components   []string `json:"components"`
			Entitlements []string `json:"entitlements"`
			Checksum     *string  `json:"checksum"`
			Version      *string  `json:"version"`
		} `json:"scope"`
	}

	if err := c.ShouldBindJSON(&requestData); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid request", "reason": err.Error()})
		return
	}

	// Build validation scope
	scope := &licenseService.ValidationScope{}
	if requestData.Scope != nil {
		if requestData.Scope.Product != nil {
			scope.Product = requestData.Scope.Product
		}
		if requestData.Scope.Policy != nil {
			scope.Policy = requestData.Scope.Policy
		}
		if requestData.Scope.User != nil {
			scope.User = requestData.Scope.User
		}
		if requestData.Scope.Machine != nil {
			scope.Machine = requestData.Scope.Machine
		}
		if requestData.Scope.Fingerprint != nil {
			scope.Fingerprint = requestData.Scope.Fingerprint
		}
		if len(requestData.Scope.Fingerprints) > 0 {
			scope.Fingerprints = requestData.Scope.Fingerprints
		}
		if len(requestData.Scope.Components) > 0 {
			scope.Components = requestData.Scope.Components
		}
		if len(requestData.Scope.Entitlements) > 0 {
			scope.Entitlements = requestData.Scope.Entitlements
		}
		if requestData.Scope.Checksum != nil {
			scope.Checksum = requestData.Scope.Checksum
		}
		if requestData.Scope.Version != nil {
			scope.Version = requestData.Scope.Version
		}
	}

	// Use validation service with options
	result, err := h.validationService.ValidateLicenseWithOptions(c.Request.Context(), requestData.Key, &licenseService.ValidationOptions{
		Scope: scope,
	})
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "validation failed", "reason": err.Error()})
		return
	}

	// Return validation result
	response := gin.H{
		"valid":  result.Valid,
		"detail": string(result.Detail),
		"code":   string(result.Code),
		"meta": gin.H{
			"ts":    result.ValidationTime,
			"valid": result.Valid,
		},
	}

	if result.License != nil {
		response["license"] = result.License
	}

	c.JSON(http.StatusOK, response)
}

// QuickValidateLicenseByID performs quick validation by license ID
func (h *LicenseHandler) QuickValidateLicenseByID(c *gin.Context) {
	licenseID := c.Param("license_id")

	licenseUUID, err := uuid.Parse(licenseID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid license ID"})
		return
	}

	license, err := h.licenseRepo.GetByID(c.Request.Context(), licenseUUID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "license not found"})
		return
	}

	// Use quick validation
	valid, err := h.validationService.ValidateLicenseQuick(c.Request.Context(), license.Key)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "validation failed", "reason": err.Error()})
		return
	}

	response := gin.H{
		"valid":   valid,
		"license": license,
		"meta": gin.H{
			"ts":    time.Now(),
			"valid": valid,
		},
	}

	if valid {
		response["detail"] = "license is valid"
		response["code"] = "VALID"
	} else {
		response["detail"] = "license is invalid"
		response["code"] = "INVALID"
	}

	c.JSON(http.StatusOK, response)
}

// CheckoutLicense generates a license certificate using checkout service
func (h *LicenseHandler) CheckoutLicense(c *gin.Context) {
	licenseID := c.Param("license_id")

	licenseUUID, err := uuid.Parse(licenseID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid license ID"})
		return
	}

	license, err := h.licenseRepo.GetByID(c.Request.Context(), licenseUUID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "license not found"})
		return
	}

	// Parse checkout options from request
	var requestData struct {
		Include   []string `json:"include"`
		TTL       *int     `json:"ttl"`
		Encrypted bool     `json:"encrypted"`
		Algorithm *string  `json:"algorithm"`
	}
	c.ShouldBindJSON(&requestData)

	// Use checkout service  
	options := &licenseService.CheckoutOptions{
		Include:   requestData.Include,
		TTL:       requestData.TTL,
		Encrypted: requestData.Encrypted,
		Algorithm: requestData.Algorithm,
	}

	licenseFile, err := h.checkoutService.CheckoutLicense(c.Request.Context(), license, options)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "checkout failed", "reason": err.Error()})
		return
	}

	// Update license checkout timestamp
	license.LastCheckOutAt = &licenseFile.IssuedAt
	h.licenseRepo.Update(c.Request.Context(), license)

	// Return certificate as file download
	c.Header("Content-Type", "application/x-pem-file")
	c.Header("Content-Disposition", `attachment; filename="`+license.Key+`.lic"`)
	c.String(http.StatusOK, licenseFile.Certificate)
}

// GetLicenseInfo gets license information by key
func (h *LicenseHandler) GetLicenseInfo(c *gin.Context) {
	key := c.Param("key")

	info, err := h.validationService.GetLicenseInfo(c.Request.Context(), key)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid license key", "reason": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"license": info})
}

// InvalidateLicenseCache invalidates cache for a license
func (h *LicenseHandler) InvalidateLicenseCache(c *gin.Context) {
	var requestData struct {
		Key string `json:"key" binding:"required"`
	}

	if err := c.ShouldBindJSON(&requestData); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid request", "reason": err.Error()})
		return
	}

	err := h.validationService.InvalidateCache(c.Request.Context(), requestData.Key)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to invalidate cache", "reason": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "cache invalidated successfully"})
}

// CreateLicense creates a new license following keygen-api patterns
func (h *LicenseHandler) CreateLicense(c *gin.Context) {
	organizationID := c.Param("organization_id")
	productID := c.Param("product_id")

	var requestData struct {
		// Core attributes mapping from Ruby typed_params
		ID                   *string                 `json:"id"`
		Name                 *string                 `json:"name"`
		Key                  *string                 `json:"key"`
		Protected            *bool                   `json:"protected"`
		Suspended            *bool                   `json:"suspended"`
		Expiry               *time.Time              `json:"expiry"`
		MaxMachinesOverride  *int                    `json:"max_machines"`
		MaxCoresOverride     *int                    `json:"max_cores"`
		MaxUsesOverride      *int                    `json:"max_uses"`
		MaxProcessesOverride *int                    `json:"max_processes"`
		MaxUsersOverride     *int                    `json:"max_users"`
		Metadata             map[string]interface{}  `json:"metadata"`
		
		// Relationships
		PolicyID *string `json:"policy_id" binding:"required"`
		OwnerID  *string `json:"owner_id"`
		GroupID  *string `json:"group_id"`
	}

	if err := c.ShouldBindJSON(&requestData); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid request", "reason": err.Error()})
		return
	}

	// Validate policy exists and belongs to product
	policyUUID, err := uuid.Parse(*requestData.PolicyID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid policy ID"})
		return
	}

	// TODO: Validate policy belongs to product
	// policy, err := h.policyRepo.GetByID(c.Request.Context(), policyUUID)

	// Create license entity
	now := time.Now()
	license := &entities.License{
		OrganizationID:        organizationID,
		ProductID:            productID,
		PolicyID:             policyUUID.String(),
		Name:                 requestData.Name,
		Key:                  "", // Will be set below or auto-generated
		Protected:            false, // Default value
		Suspended:            false, // Default value
		Status:               entities.LicenseStatusActive,
		OwnerType:            entities.LicenseOwnerTypeUser, // Default
		OwnerID:              organizationID, // Default to organization
		ExpiresAt:            requestData.Expiry,
		MaxUsesOverride:      requestData.MaxUsesOverride,
		MaxMachinesOverride:  requestData.MaxMachinesOverride,
		MaxCoresOverride:     requestData.MaxCoresOverride,
		MaxProcessesOverride: requestData.MaxProcessesOverride,
		MaxUsersOverride:     requestData.MaxUsersOverride,
		Metadata:             entities.Metadata(requestData.Metadata),
		CreatedAt:            now,
		UpdatedAt:            now,
	}

	if requestData.ID != nil {
		license.ID = *requestData.ID
	}
	
	// Handle license key generation like Ruby: auto-generate if not provided
	if requestData.Key != nil && *requestData.Key != "" {
		license.Key = *requestData.Key
	} else {
		// Auto-generate license key (Ruby: autogenerate_key callback)
		// TODO: Implement proper key generation strategies based on policy scheme
		// For now, generate a simple unencrypted key like Ruby's generate_unencrypted_key!
		license.Key = generateLicenseKey()
	}
	if requestData.Protected != nil {
		license.Protected = *requestData.Protected
	}
	if requestData.Suspended != nil {
		license.Suspended = *requestData.Suspended
	}
	if requestData.OwnerID != nil {
		license.OwnerID = *requestData.OwnerID
		license.OwnerType = entities.LicenseOwnerTypeUser // Assume user owner
	}

	if err := h.licenseRepo.Create(c.Request.Context(), license); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to create license", "reason": err.Error()})
		return
	}

	// TODO: Broadcast license.created event (Ruby: BroadcastEventService.call)

	c.JSON(http.StatusCreated, gin.H{"license": license})
}

// UpdateLicense updates an existing license following keygen-api patterns
func (h *LicenseHandler) UpdateLicense(c *gin.Context) {
	licenseID := c.Param("license_id")

	licenseUUID, err := uuid.Parse(licenseID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid license ID"})
		return
	}

	license, err := h.licenseRepo.GetByID(c.Request.Context(), licenseUUID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "license not found"})
		return
	}

	var requestData struct {
		// Update fields mapping from Ruby typed_params for update action
		Name                 *string                 `json:"name"`
		Protected            *bool                   `json:"protected"`
		Suspended            *bool                   `json:"suspended"`
		Expiry               *time.Time              `json:"expiry"`
		MaxMachinesOverride  *int                    `json:"max_machines"`
		MaxCoresOverride     *int                    `json:"max_cores"`
		MaxUsesOverride      *int                    `json:"max_uses"`
		MaxProcessesOverride *int                    `json:"max_processes"`
		MaxUsersOverride     *int                    `json:"max_users"`
		Metadata             *map[string]interface{} `json:"metadata"`
	}

	if err := c.ShouldBindJSON(&requestData); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid request", "reason": err.Error()})
		return
	}

	// Update fields if provided (Ruby: license.update(license_params))
	if requestData.Name != nil {
		license.Name = requestData.Name
	}
	if requestData.Protected != nil {
		license.Protected = *requestData.Protected
	}
	if requestData.Suspended != nil {
		license.Suspended = *requestData.Suspended
	}
	if requestData.Expiry != nil {
		license.ExpiresAt = requestData.Expiry
	}
	if requestData.MaxMachinesOverride != nil {
		license.MaxMachinesOverride = requestData.MaxMachinesOverride
	}
	if requestData.MaxCoresOverride != nil {
		license.MaxCoresOverride = requestData.MaxCoresOverride
	}
	if requestData.MaxUsesOverride != nil {
		license.MaxUsesOverride = requestData.MaxUsesOverride
	}
	if requestData.MaxProcessesOverride != nil {
		license.MaxProcessesOverride = requestData.MaxProcessesOverride
	}
	if requestData.MaxUsersOverride != nil {
		license.MaxUsersOverride = requestData.MaxUsersOverride
	}
	if requestData.Metadata != nil {
		license.Metadata = entities.Metadata(*requestData.Metadata)
	}

	if err := h.licenseRepo.Update(c.Request.Context(), license); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to update license", "reason": err.Error()})
		return
	}

	// TODO: Broadcast license.updated event (Ruby: BroadcastEventService.call)

	c.JSON(http.StatusOK, gin.H{"license": license})
}

// DeleteLicense soft deletes a license following keygen-api patterns
func (h *LicenseHandler) DeleteLicense(c *gin.Context) {
	licenseID := c.Param("license_id")

	licenseUUID, err := uuid.Parse(licenseID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid license ID"})
		return
	}

	_, err = h.licenseRepo.GetByID(c.Request.Context(), licenseUUID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "license not found"})
		return
	}

	// TODO: Broadcast license.deleted event (Ruby: BroadcastEventService.call)

	if err := h.licenseRepo.Delete(c.Request.Context(), licenseUUID); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to delete license", "reason": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "license deleted successfully"})
}

// GetLicenseStats returns license statistics
func (h *LicenseHandler) GetLicenseStats(c *gin.Context) {
	licenseID := c.Param("license_id")

	licenseUUID, err := uuid.Parse(licenseID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid license ID"})
		return
	}

	license, err := h.licenseRepo.GetByID(c.Request.Context(), licenseUUID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "license not found"})
		return
	}

	// TODO: Calculate actual license statistics from related entities
	stats := gin.H{
		"license_id": license.ID,
		"machines": gin.H{
			"total": license.MachinesCount,
			"active": 0, // TODO: count active machines
			"inactive": 0, // TODO: count inactive machines
		},
		"activations": gin.H{
			"total": license.Uses,
			"max": func() interface{} {
				if license.MaxUsesOverride != nil {
					return *license.MaxUsesOverride
				}
				return "unlimited"
			}(),
			"remaining": func() interface{} {
				if license.MaxUsesOverride != nil {
					remaining := *license.MaxUsesOverride - license.Uses
					if remaining < 0 {
						return 0
					}
					return remaining
				}
				return "unlimited"
			}(),
		},
		"usage": gin.H{
			"cores": license.MachinesCoreCount,
			"processes": 0, // TODO: count processes
		},
		"status": func() string {
			if license.Suspended {
				return "suspended"
			}
			if license.ExpiresAt != nil && license.ExpiresAt.Before(time.Now()) {
				return "expired"
			}
			return "active"
		}(),
	}

	c.JSON(http.StatusOK, gin.H{"stats": stats})
}

// GetLicenseMachines returns all machines associated with a license
func (h *LicenseHandler) GetLicenseMachines(c *gin.Context) {
	licenseID := c.Param("license_id")

	licenseUUID, err := uuid.Parse(licenseID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid license ID"})
		return
	}

	// Parse pagination parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))

	// Build filter for machines by license
	filter := repositories.DefaultListFilter()
	filter.Page = page
	filter.PageSize = pageSize
	filter.Filters["license_id = ?"] = licenseUUID.String()

	// TODO: Get machines from machine repository when it has List method
	// machines, total, err := h.machineRepo.List(c.Request.Context(), filter)
	
	// Placeholder response
	totalPages := 0
	c.JSON(http.StatusOK, gin.H{
		"license_id": licenseID,
		"machines": []interface{}{},
		"pagination": gin.H{
			"page": page,
			"page_size": pageSize,
			"total": 0,
			"total_pages": totalPages,
		},
	})
}

// generateLicenseKey generates an unencrypted license key following Ruby's generate_unencrypted_key! pattern
// Ruby: generate_token :key, length: 16 do |token| token.scan(/.{1,6}/).join("-").upcase end
func generateLicenseKey() string {
	// Generate 16 character alphanumeric token like Ruby using crypto/rand
	const charset = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	const keyLength = 16
	
	key := make([]byte, keyLength)
	randomBytes := make([]byte, keyLength)
	
	if _, err := rand.Read(randomBytes); err != nil {
		// Fallback to uuid-based generation if crypto/rand fails
		fallbackKey := uuid.New().String()
		fallbackKey = strings.ReplaceAll(fallbackKey, "-", "")
		fallbackKey = strings.ToUpper(fallbackKey)[:keyLength]
		return fallbackKey[:6] + "-" + fallbackKey[6:12] + "-" + fallbackKey[12:]
	}
	
	for i := range key {
		key[i] = charset[int(randomBytes[i])%len(charset)]
	}
	
	// Split every 6 characters and join with dash like Ruby: scan(/.{1,6}/).join("-")
	keyStr := string(key)
	var parts []string
	for i := 0; i < len(keyStr); i += 6 {
		end := i + 6
		if end > len(keyStr) {
			end = len(keyStr)
		}
		parts = append(parts, keyStr[i:end])
	}
	
	return strings.Join(parts, "-")
}
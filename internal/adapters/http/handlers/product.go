package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/gokeys/gokeys/internal/domain/repositories"
	"github.com/gokeys/gokeys/internal/domain/services/auth"
)

// ProductHandler handles product-related HTTP requests
type ProductHandler struct {
	productRepo      repositories.ProductRepository
	organizationRepo repositories.OrganizationRepository
	policyRepo       repositories.PolicyRepository
	authService      *auth.AuthService
}

// NewProductHandler creates a new product handler
func NewProductHandler(
	productRepo repositories.ProductRepository,
	organizationRepo repositories.OrganizationRepository,
	policyRepo repositories.PolicyRepository,
	authService *auth.AuthService,
) *ProductHandler {
	return &ProductHandler{
		productRepo:      productRepo,
		organizationRepo: organizationRepo,
		policyRepo:       policyRepo,
		authService:      authService,
	}
}

// ListOrganizationProducts lists products in an organization
func (h *ProductHandler) ListOrganizationProducts(c *gin.Context) {
	organizationID := c.Param("organization_id")
	if organizationID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "organization_id required"})
		return
	}

	// Parse pagination parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))
	search := c.Query("search")

	// Get products by organization
	products, err := h.productRepo.GetByOrganizationID(c.Request.Context(), parseUUID(organizationID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to list products", "reason": err.Error()})
		return
	}

	// Apply search filter if provided
	var filteredProducts []*entities.Product
	if search != "" {
		for _, product := range products {
			if contains(product.Name, search) || contains(product.Code, search) {
				filteredProducts = append(filteredProducts, product)
			}
		}
	} else {
		filteredProducts = products
	}

	// Apply pagination
	total := len(filteredProducts)
	start := (page - 1) * pageSize
	end := start + pageSize

	if start > total {
		start = total
	}
	if end > total {
		end = total
	}

	var paginatedProducts []*entities.Product
	if start < total {
		paginatedProducts = filteredProducts[start:end]
	}

	c.JSON(http.StatusOK, gin.H{
		"products": paginatedProducts,
		"pagination": gin.H{
			"page":       page,
			"page_size":  pageSize,
			"total":      total,
			"total_pages": (total + pageSize - 1) / pageSize,
		},
	})
}

// CreateProduct creates a new product in an organization
func (h *ProductHandler) CreateProduct(c *gin.Context) {
	organizationID := c.Param("organization_id")
	if organizationID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "organization_id required"})
		return
	}

	var requestData struct {
		Name        string                 `json:"name" binding:"required"`
		Code        string                 `json:"code" binding:"required"`
		Description *string                `json:"description"`
		Platforms   entities.ProductPlatforms `json:"platforms"`
		Metadata    entities.Metadata      `json:"metadata"`
	}

	if err := c.ShouldBindJSON(&requestData); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid request", "reason": err.Error()})
		return
	}

	// Check if organization exists
	_, err := h.organizationRepo.GetByID(c.Request.Context(), parseUUID(organizationID))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "organization not found", "reason": err.Error()})
		return
	}

	// Check if product code already exists in organization
	existing, err := h.productRepo.GetByCode(c.Request.Context(), requestData.Code, parseUUID(organizationID))
	if err == nil && existing != nil {
		c.JSON(http.StatusConflict, gin.H{"error": "product code already exists in organization"})
		return
	}

	// Create product
	product := &entities.Product{
		OrganizationID: organizationID,
		Name:           requestData.Name,
		Code:           requestData.Code,
		Description:    requestData.Description,
		Platforms:      requestData.Platforms,
		Metadata:       requestData.Metadata,
	}

	if err := h.productRepo.Create(c.Request.Context(), product); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to create product", "reason": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, gin.H{"product": product})
}

// GetProduct gets a specific product
func (h *ProductHandler) GetProduct(c *gin.Context) {
	organizationID := c.Param("organization_id")
	productID := c.Param("product_id")

	if organizationID == "" || productID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "organization_id and product_id required"})
		return
	}

	product, err := h.productRepo.GetByID(c.Request.Context(), parseUUID(productID))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "product not found", "reason": err.Error()})
		return
	}

	// Verify product belongs to organization
	if product.OrganizationID != organizationID {
		c.JSON(http.StatusNotFound, gin.H{"error": "product not found in organization"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"product": product})
}

// UpdateProduct updates a product
func (h *ProductHandler) UpdateProduct(c *gin.Context) {
	organizationID := c.Param("organization_id")
	productID := c.Param("product_id")

	if organizationID == "" || productID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "organization_id and product_id required"})
		return
	}

	var requestData struct {
		Name        *string                `json:"name"`
		Description *string                `json:"description"`
		Platforms   *entities.ProductPlatforms `json:"platforms"`
		Metadata    *entities.Metadata     `json:"metadata"`
	}

	if err := c.ShouldBindJSON(&requestData); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid request", "reason": err.Error()})
		return
	}

	product, err := h.productRepo.GetByID(c.Request.Context(), parseUUID(productID))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "product not found", "reason": err.Error()})
		return
	}

	// Verify product belongs to organization
	if product.OrganizationID != organizationID {
		c.JSON(http.StatusNotFound, gin.H{"error": "product not found in organization"})
		return
	}

	// Update fields if provided
	if requestData.Name != nil {
		product.Name = *requestData.Name
	}
	if requestData.Description != nil {
		product.Description = requestData.Description
	}
	if requestData.Platforms != nil {
		product.Platforms = *requestData.Platforms
	}
	if requestData.Metadata != nil {
		product.Metadata = *requestData.Metadata
	}

	if err := h.productRepo.Update(c.Request.Context(), product); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to update product", "reason": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"product": product})
}

// DeleteProduct deletes a product
func (h *ProductHandler) DeleteProduct(c *gin.Context) {
	organizationID := c.Param("organization_id")
	productID := c.Param("product_id")

	if organizationID == "" || productID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "organization_id and product_id required"})
		return
	}

	product, err := h.productRepo.GetByID(c.Request.Context(), parseUUID(productID))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "product not found", "reason": err.Error()})
		return
	}

	// Verify product belongs to organization
	if product.OrganizationID != organizationID {
		c.JSON(http.StatusNotFound, gin.H{"error": "product not found in organization"})
		return
	}

	// Soft delete product
	if err := h.productRepo.SoftDelete(c.Request.Context(), parseUUID(productID)); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to delete product", "reason": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "product deleted successfully"})
}

// GetProductPolicies gets policies for a product
func (h *ProductHandler) GetProductPolicies(c *gin.Context) {
	organizationID := c.Param("organization_id")
	productID := c.Param("product_id")

	if organizationID == "" || productID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "organization_id and product_id required"})
		return
	}

	// Verify product exists and belongs to organization
	product, err := h.productRepo.GetByID(c.Request.Context(), parseUUID(productID))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "product not found", "reason": err.Error()})
		return
	}

	if product.OrganizationID != organizationID {
		c.JSON(http.StatusNotFound, gin.H{"error": "product not found in organization"})
		return
	}

	// Get policies for the product
	policies, err := h.policyRepo.GetByProduct(c.Request.Context(), parseUUID(productID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to get product policies", "reason": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"policies": policies})
}

// GetProductStats gets statistics for a product
func (h *ProductHandler) GetProductStats(c *gin.Context) {
	organizationID := c.Param("organization_id")
	productID := c.Param("product_id")

	if organizationID == "" || productID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "organization_id and product_id required"})
		return
	}

	// Verify product exists and belongs to organization
	product, err := h.productRepo.GetByID(c.Request.Context(), parseUUID(productID))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "product not found", "reason": err.Error()})
		return
	}

	if product.OrganizationID != organizationID {
		c.JSON(http.StatusNotFound, gin.H{"error": "product not found in organization"})
		return
	}

	// Get policies count
	policies, err := h.policyRepo.GetByProduct(c.Request.Context(), parseUUID(productID))
	if err != nil {
		policies = []*entities.Policy{} // Default to empty on error
	}

	stats := gin.H{
		"policies_count": len(policies),
		"product_id":     productID,
		// Add more stats as needed (licenses count, active machines, etc.)
	}

	c.JSON(http.StatusOK, gin.H{"stats": stats})
}

// contains checks if a string contains a substring (case-insensitive)
func contains(str, substr string) bool {
	if str == "" || substr == "" {
		return false
	}
	
	// Simple case-insensitive contains check
	strLower := ""
	substrLower := ""
	
	for _, r := range str {
		if r >= 'A' && r <= 'Z' {
			strLower += string(r + 32)
		} else {
			strLower += string(r)
		}
	}
	
	for _, r := range substr {
		if r >= 'A' && r <= 'Z' {
			substrLower += string(r + 32)
		} else {
			substrLower += string(r)
		}
	}
	
	// Simple substring search
	for i := 0; i <= len(strLower)-len(substrLower); i++ {
		if strLower[i:i+len(substrLower)] == substrLower {
			return true
		}
	}
	
	return false
}
package handlers

import (
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/gokeys/gokeys/internal/domain/repositories"
)

// MachineHandler handles machine-related HTTP requests following keygen-api patterns
type MachineHandler struct {
	machineRepo  repositories.MachineRepository
	licenseRepo  repositories.LicenseRepository
	policyRepo   repositories.PolicyRepository
	userRepo     repositories.UserRepository
}

// NewMachineHandler creates a new machine handler
func NewMachineHandler(
	machineRepo repositories.MachineRepository,
	licenseRepo repositories.LicenseRepository,
	policyRepo repositories.PolicyRepository,
	userRepo repositories.UserRepository,
) *MachineHandler {
	return &MachineHandler{
		machineRepo: machineRepo,
		licenseRepo: licenseRepo,
		policyRepo:  policyRepo,
		userRepo:    userRepo,
	}
}

// ListMachines lists all machines with optional scoping (Ruby: has_scope for multiple filters)
func (h *MachineHandler) ListMachines(c *gin.Context) {
	organizationID := c.Param("organization_id")

	// Parse pagination parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))

	// Build filter with Ruby has_scope equivalent filters
	filter := repositories.DefaultListFilter()
	filter.Page = page
	filter.PageSize = pageSize
	filter.Filters["organization_id = ?"] = organizationID

	// Apply scoping filters (Ruby: has_scope)
	if fingerprint := c.Query("fingerprint"); fingerprint != "" {
		filter.Filters["fingerprint = ?"] = fingerprint
	}
	if ip := c.Query("ip"); ip != "" {
		filter.Filters["ip = ?"] = ip
	}
	if hostname := c.Query("hostname"); hostname != "" {
		filter.Filters["hostname = ?"] = hostname
	}
	if status := c.Query("status"); status != "" {
		filter.Filters["status = ?"] = status
	}
	if productID := c.Query("product"); productID != "" {
		filter.Filters["product_id = ?"] = productID
	}
	if policyID := c.Query("policy"); policyID != "" {
		filter.Filters["policy_id = ?"] = policyID
	}
	if licenseID := c.Query("license"); licenseID != "" {
		filter.Filters["license_id = ?"] = licenseID
	}
	if licenseKey := c.Query("key"); licenseKey != "" {
		// Ruby: for_key scope - need to join with licenses table
		filter.Filters["licenses.key = ?"] = licenseKey
	}
	if ownerID := c.Query("owner"); ownerID != "" {
		filter.Filters["owner_id = ?"] = ownerID
	}
	if userID := c.Query("user"); userID != "" {
		filter.Filters["owner_id = ?"] = userID // Ruby: for_user scope maps to owner_id
	}

	// Get machines
	machines, total, err := h.machineRepo.List(c.Request.Context(), filter)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to get machines", "reason": err.Error()})
		return
	}

	// Calculate pagination
	totalPages := (int(total) + pageSize - 1) / pageSize

	c.JSON(http.StatusOK, gin.H{
		"machines": machines,
		"pagination": gin.H{
			"page":        page,
			"page_size":   pageSize,
			"total":       total,
			"total_pages": totalPages,
		},
	})
}

// GetMachine retrieves a specific machine by ID
func (h *MachineHandler) GetMachine(c *gin.Context) {
	machineID := c.Param("machine_id")

	machineUUID, err := uuid.Parse(machineID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid machine ID"})
		return
	}

	machine, err := h.machineRepo.GetByID(c.Request.Context(), machineUUID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "machine not found"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"machine": machine})
}

// CreateMachine creates a new machine following keygen-api patterns
func (h *MachineHandler) CreateMachine(c *gin.Context) {
	organizationID := c.Param("organization_id")

	var requestData struct {
		// Core attributes mapping from Ruby typed_params
		LicenseID   string             `json:"license_id" binding:"required"`
		Fingerprint string             `json:"fingerprint" binding:"required"`
		Name        *string            `json:"name"`
		IP          *string            `json:"ip"`
		Hostname    *string            `json:"hostname"`
		Platform    *string            `json:"platform"`
		Cores       *int               `json:"cores"`
		OwnerID     *string            `json:"owner_id"`
		Metadata    entities.Metadata  `json:"metadata"`
		
		// Machine components (Ruby: relationships.components)
		Components []struct {
			Fingerprint string             `json:"fingerprint" binding:"required"`
			Name        string             `json:"name" binding:"required"`
			Metadata    entities.Metadata  `json:"metadata"`
		} `json:"components"`
	}

	if err := c.ShouldBindJSON(&requestData); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid request", "reason": err.Error()})
		return
	}

	// Validate license exists and belongs to organization
	licenseUUID, err := uuid.Parse(requestData.LicenseID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid license ID"})
		return
	}

	license, err := h.licenseRepo.GetByID(c.Request.Context(), licenseUUID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "license not found"})
		return
	}

	if license.OrganizationID != organizationID {
		c.JSON(http.StatusBadRequest, gin.H{"error": "license does not belong to this organization"})
		return
	}

	// Validate owner if specified
	if requestData.OwnerID != nil {
		ownerUUID, err := uuid.Parse(*requestData.OwnerID)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "invalid owner ID"})
			return
		}

		_, err = h.userRepo.GetByID(c.Request.Context(), ownerUUID)
		if err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "owner not found"})
			return
		}
	}

	// Create machine entity
	now := time.Now()
	machine := &entities.Machine{
		LicenseID:      requestData.LicenseID,
		PolicyID:       license.PolicyID,
		Fingerprint:    requestData.Fingerprint,
		Name:           requestData.Name,
		IP:             requestData.IP,
		Hostname:       requestData.Hostname,
		Platform:       requestData.Platform,
		Cores:          1, // Default to 1 core
		ActivatedAt:    &now,
		Metadata:       requestData.Metadata,
	}

	if requestData.Cores != nil {
		machine.Cores = *requestData.Cores
	}
	if requestData.OwnerID != nil {
		machine.OwnerID = requestData.OwnerID
	}

	// Save machine
	if err := h.machineRepo.Create(c.Request.Context(), machine); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to create machine", "reason": err.Error()})
		return
	}

	// TODO: Handle machine components creation if provided
	// TODO: Handle activation token increment (Ruby: current_token.increment :activations)
	// TODO: Broadcast machine.created event (Ruby: BroadcastEventService.call)

	c.JSON(http.StatusCreated, gin.H{"machine": machine})
}

// UpdateMachine updates an existing machine following keygen-api patterns
func (h *MachineHandler) UpdateMachine(c *gin.Context) {
	machineID := c.Param("machine_id")

	machineUUID, err := uuid.Parse(machineID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid machine ID"})
		return
	}

	machine, err := h.machineRepo.GetByID(c.Request.Context(), machineUUID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "machine not found"})
		return
	}

	var requestData struct {
		// Update fields mapping from Ruby typed_params for update action
		Name     *string            `json:"name"`
		IP       *string            `json:"ip"`
		Hostname *string            `json:"hostname"`
		Platform *string            `json:"platform"`
		Cores    *int               `json:"cores"`
		Metadata *entities.Metadata `json:"metadata"`
	}

	if err := c.ShouldBindJSON(&requestData); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid request", "reason": err.Error()})
		return
	}

	// Update fields if provided (Ruby: machine.update(machine_params))
	if requestData.Name != nil {
		machine.Name = requestData.Name
	}
	if requestData.IP != nil {
		machine.IP = requestData.IP
	}
	if requestData.Hostname != nil {
		machine.Hostname = requestData.Hostname
	}
	if requestData.Platform != nil {
		machine.Platform = requestData.Platform
	}
	if requestData.Cores != nil {
		machine.Cores = *requestData.Cores
	}
	if requestData.Metadata != nil {
		machine.Metadata = *requestData.Metadata
	}

	if err := h.machineRepo.Update(c.Request.Context(), machine); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to update machine", "reason": err.Error()})
		return
	}

	// TODO: Broadcast machine.updated event (Ruby: BroadcastEventService.call)

	c.JSON(http.StatusOK, gin.H{"machine": machine})
}

// DeleteMachine deletes a machine
func (h *MachineHandler) DeleteMachine(c *gin.Context) {
	machineID := c.Param("machine_id")

	machineUUID, err := uuid.Parse(machineID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid machine ID"})
		return
	}

	_, err = h.machineRepo.GetByID(c.Request.Context(), machineUUID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "machine not found"})
		return
	}

	// TODO: Handle activation token decrement (Ruby: current_token.decrement :activations)
	// TODO: Broadcast machine.deleted event (Ruby: BroadcastEventService.call)

	if err := h.machineRepo.Delete(c.Request.Context(), machineUUID); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to delete machine", "reason": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "machine deleted successfully"})
}

// MachineHeartbeat updates machine heartbeat (Ruby: machines/actions/heartbeats_controller.rb)
func (h *MachineHandler) MachineHeartbeat(c *gin.Context) {
	machineID := c.Param("machine_id")

	machineUUID, err := uuid.Parse(machineID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid machine ID"})
		return
	}

	machine, err := h.machineRepo.GetByID(c.Request.Context(), machineUUID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "machine not found"})
		return
	}

	// Update heartbeat timestamp
	err = h.machineRepo.UpdateHeartbeat(c.Request.Context(), machineUUID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to update heartbeat", "reason": err.Error()})
		return
	}

	// TODO: Broadcast machine.heartbeat event (Ruby: BroadcastEventService.call)

	c.JSON(http.StatusOK, gin.H{
		"message":        "heartbeat updated successfully",
		"machine":        machine,
		"heartbeat_at":   time.Now(),
	})
}

// GetMachineProcesses gets all processes for a machine
func (h *MachineHandler) GetMachineProcesses(c *gin.Context) {
	machineID := c.Param("machine_id")

	_, err := uuid.Parse(machineID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid machine ID"})
		return
	}

	// TODO: Implement when MachineProcess repository is available
	// processes, err := h.machineProcessRepo.GetByMachineID(c.Request.Context(), machineID)

	c.JSON(http.StatusOK, gin.H{
		"machine_id": machineID,
		"processes":  []interface{}{}, // Placeholder
	})
}

// GetMachineComponents gets all components for a machine
func (h *MachineHandler) GetMachineComponents(c *gin.Context) {
	machineID := c.Param("machine_id")

	_, err := uuid.Parse(machineID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid machine ID"})
		return
	}

	// TODO: Implement when MachineComponent repository is available
	// components, err := h.machineComponentRepo.GetByMachineID(c.Request.Context(), machineID)

	c.JSON(http.StatusOK, gin.H{
		"machine_id": machineID,
		"components": []interface{}{}, // Placeholder
	})
}
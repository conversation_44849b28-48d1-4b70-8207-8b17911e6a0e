package handlers

import (
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/gokeys/gokeys/internal/domain/repositories"
	"github.com/google/uuid"
)

// MachineHandler xử lý các HTTP requests liên quan đến machine
// Machine trong keygen-api đại diện cho một máy tính/thiết bị chạy license
// Bao gồm fingerprinting, heartbeat monitoring, và component tracking
type MachineHandler struct {
	machineRepo repositories.MachineRepository // Repository để thao tác với machine trong database
	licenseRepo repositories.LicenseRepository // Repository để validate license tồn tại
	policyRepo  repositories.PolicyRepository  // Repository để lấy policy rules
	userRepo    repositories.UserRepository    // Repository để validate user ownership
}

// NewMachineHandler tạo một machine handler mới
// Cần tất cả repositories để validate relationships và business rules
func NewMachineHandler(
	machineRepo repositories.MachineRepository,
	licenseRepo repositories.LicenseRepository,
	policyRepo repositories.PolicyRepository,
	userRepo repositories.UserRepository,
) *MachineHandler {
	return &MachineHandler{
		machineRepo: machineRepo,
		licenseRepo: licenseRepo,
		policyRepo:  policyRepo,
		userRepo:    userRepo,
	}
}

// ListMachines lists all machines with optional scoping (Ruby: has_scope for multiple filters)
func (h *MachineHandler) ListMachines(c *gin.Context) {
	organizationID := c.Param("organization_id")

	// Parse pagination parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))

	// Build filter with Ruby has_scope equivalent filters
	filter := repositories.DefaultListFilter()
	filter.Page = page
	filter.PageSize = pageSize
	filter.Filters["organization_id = ?"] = organizationID

	// Apply scoping filters (Ruby: has_scope)
	if fingerprint := c.Query("fingerprint"); fingerprint != "" {
		filter.Filters["fingerprint = ?"] = fingerprint
	}
	if ip := c.Query("ip"); ip != "" {
		filter.Filters["ip = ?"] = ip
	}
	if hostname := c.Query("hostname"); hostname != "" {
		filter.Filters["hostname = ?"] = hostname
	}
	if status := c.Query("status"); status != "" {
		filter.Filters["status = ?"] = status
	}
	if productID := c.Query("product"); productID != "" {
		filter.Filters["product_id = ?"] = productID
	}
	if policyID := c.Query("policy"); policyID != "" {
		filter.Filters["policy_id = ?"] = policyID
	}
	if licenseID := c.Query("license"); licenseID != "" {
		filter.Filters["license_id = ?"] = licenseID
	}
	if licenseKey := c.Query("key"); licenseKey != "" {
		// Ruby: for_key scope - need to join with licenses table
		filter.Filters["licenses.key = ?"] = licenseKey
	}
	if ownerID := c.Query("owner"); ownerID != "" {
		filter.Filters["owner_id = ?"] = ownerID
	}
	if userID := c.Query("user"); userID != "" {
		filter.Filters["owner_id = ?"] = userID // Ruby: for_user scope maps to owner_id
	}

	// Get machines
	machines, total, err := h.machineRepo.List(c.Request.Context(), filter)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to get machines", "reason": err.Error()})
		return
	}

	// Calculate pagination
	totalPages := (int(total) + pageSize - 1) / pageSize

	c.JSON(http.StatusOK, gin.H{
		"machines": machines,
		"pagination": gin.H{
			"page":        page,
			"page_size":   pageSize,
			"total":       total,
			"total_pages": totalPages,
		},
	})
}

// GetMachine retrieves a specific machine by ID
func (h *MachineHandler) GetMachine(c *gin.Context) {
	machineID := c.Param("machine_id")

	machineUUID, err := uuid.Parse(machineID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid machine ID"})
		return
	}

	machine, err := h.machineRepo.GetByID(c.Request.Context(), machineUUID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "machine not found"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"machine": machine})
}

// CreateMachine creates a new machine following keygen-api patterns
func (h *MachineHandler) CreateMachine(c *gin.Context) {
	organizationID := c.Param("organization_id")

	var requestData struct {
		// Core attributes mapping from Ruby typed_params
		LicenseID   string            `json:"license_id" binding:"required"`
		Fingerprint string            `json:"fingerprint" binding:"required"`
		Name        *string           `json:"name"`
		IP          *string           `json:"ip"`
		Hostname    *string           `json:"hostname"`
		Platform    *string           `json:"platform"`
		Cores       *int              `json:"cores"`
		OwnerID     *string           `json:"owner_id"`
		Metadata    entities.Metadata `json:"metadata"`

		// Machine components (Ruby: relationships.components)
		Components []struct {
			Fingerprint string            `json:"fingerprint" binding:"required"`
			Name        string            `json:"name" binding:"required"`
			Metadata    entities.Metadata `json:"metadata"`
		} `json:"components"`
	}

	if err := c.ShouldBindJSON(&requestData); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid request", "reason": err.Error()})
		return
	}

	// Validate license exists and belongs to organization
	licenseUUID, err := uuid.Parse(requestData.LicenseID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid license ID"})
		return
	}

	license, err := h.licenseRepo.GetByID(c.Request.Context(), licenseUUID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "license not found"})
		return
	}

	if license.OrganizationID != organizationID {
		c.JSON(http.StatusBadRequest, gin.H{"error": "license does not belong to this organization"})
		return
	}

	// Validate owner if specified
	if requestData.OwnerID != nil {
		ownerUUID, err := uuid.Parse(*requestData.OwnerID)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "invalid owner ID"})
			return
		}

		_, err = h.userRepo.GetByID(c.Request.Context(), ownerUUID)
		if err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "owner not found"})
			return
		}
	}

	// === MACHINE ENTITY CREATION ===
	// Tạo machine entity với tất cả thông tin cần thiết
	now := time.Now()
	machine := &entities.Machine{
		LicenseID:   requestData.LicenseID,
		PolicyID:    license.PolicyID,
		Fingerprint: requestData.Fingerprint,
		Name:        requestData.Name,
		IP:          requestData.IP,
		Hostname:    requestData.Hostname,
		Platform:    requestData.Platform,
		Cores:       1,                            // Default to 1 core
		Status:      entities.MachineStatusActive, // Machine active khi tạo
		ActivatedAt: &now,
		Metadata:    requestData.Metadata,
		License:     *license, // Set license relation để có thể access policy
	}

	// Set optional fields
	if requestData.Cores != nil {
		machine.Cores = *requestData.Cores
	}
	if requestData.OwnerID != nil {
		machine.OwnerID = requestData.OwnerID
	}

	// === COMPONENTS PROCESSING ===
	// Xử lý machine components nếu được cung cấp
	if len(requestData.Components) > 0 {
		components := make(entities.MachineComponents)
		for i, comp := range requestData.Components {
			// Sử dụng name làm key, fingerprint làm value
			components[comp.Name] = comp.Fingerprint

			// Nếu là component đầu tiên và chưa có fingerprint, dùng làm machine fingerprint
			if i == 0 && machine.Fingerprint == "" {
				machine.Fingerprint = comp.Fingerprint
			}
		}
		machine.UpdateComponents(components)
	}

	// === BUSINESS LOGIC VALIDATION ===
	// Validate machine theo business rules
	if validationErrors := machine.ValidateMachine(); len(validationErrors) > 0 {
		c.JSON(http.StatusUnprocessableEntity, gin.H{
			"error":  "validation failed",
			"errors": validationErrors,
		})
		return
	}

	// === DATABASE SAVE ===
	// Lưu machine vào database
	if err := h.machineRepo.Create(c.Request.Context(), machine); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to create machine", "reason": err.Error()})
		return
	}

	// === POST-CREATION TASKS ===
	// TODO: Handle activation token increment (Ruby: current_token.increment :activations)
	// TODO: Broadcast machine.created event (Ruby: BroadcastEventService.call)

	c.JSON(http.StatusCreated, gin.H{"machine": machine})
}

// UpdateMachine updates an existing machine following keygen-api patterns
func (h *MachineHandler) UpdateMachine(c *gin.Context) {
	machineID := c.Param("machine_id")

	machineUUID, err := uuid.Parse(machineID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid machine ID"})
		return
	}

	machine, err := h.machineRepo.GetByID(c.Request.Context(), machineUUID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "machine not found"})
		return
	}

	var requestData struct {
		// Update fields mapping from Ruby typed_params for update action
		Name     *string            `json:"name"`
		IP       *string            `json:"ip"`
		Hostname *string            `json:"hostname"`
		Platform *string            `json:"platform"`
		Cores    *int               `json:"cores"`
		Metadata *entities.Metadata `json:"metadata"`
	}

	if err := c.ShouldBindJSON(&requestData); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid request", "reason": err.Error()})
		return
	}

	// Update fields if provided (Ruby: machine.update(machine_params))
	if requestData.Name != nil {
		machine.Name = requestData.Name
	}
	if requestData.IP != nil {
		machine.IP = requestData.IP
	}
	if requestData.Hostname != nil {
		machine.Hostname = requestData.Hostname
	}
	if requestData.Platform != nil {
		machine.Platform = requestData.Platform
	}
	if requestData.Cores != nil {
		machine.Cores = *requestData.Cores
	}
	if requestData.Metadata != nil {
		machine.Metadata = *requestData.Metadata
	}

	if err := h.machineRepo.Update(c.Request.Context(), machine); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to update machine", "reason": err.Error()})
		return
	}

	// TODO: Broadcast machine.updated event (Ruby: BroadcastEventService.call)

	c.JSON(http.StatusOK, gin.H{"machine": machine})
}

// DeleteMachine deletes a machine
func (h *MachineHandler) DeleteMachine(c *gin.Context) {
	machineID := c.Param("machine_id")

	machineUUID, err := uuid.Parse(machineID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid machine ID"})
		return
	}

	_, err = h.machineRepo.GetByID(c.Request.Context(), machineUUID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "machine not found"})
		return
	}

	// TODO: Handle activation token decrement (Ruby: current_token.decrement :activations)
	// TODO: Broadcast machine.deleted event (Ruby: BroadcastEventService.call)

	if err := h.machineRepo.Delete(c.Request.Context(), machineUUID); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to delete machine", "reason": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "machine deleted successfully"})
}

// MachineHeartbeat updates machine heartbeat (Ruby: machines/actions/heartbeats_controller.rb)
func (h *MachineHandler) MachineHeartbeat(c *gin.Context) {
	machineID := c.Param("machine_id")

	machineUUID, err := uuid.Parse(machineID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid machine ID"})
		return
	}

	// === MACHINE RETRIEVAL ===
	// Lấy machine từ database với policy relation để có thể check business rules
	machine, err := h.machineRepo.GetByID(c.Request.Context(), machineUUID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "machine not found"})
		return
	}

	// === HEARTBEAT BUSINESS LOGIC ===
	// Check xem machine có require heartbeat không
	if !machine.RequiresHeartbeat() {
		c.JSON(http.StatusUnprocessableEntity, gin.H{
			"error": "machine does not require heartbeat",
		})
		return
	}

	// Update heartbeat timestamp sử dụng business logic
	machine.UpdateHeartbeat()

	// === DATABASE UPDATE ===
	// Lưu heartbeat timestamp vào database
	if err := h.machineRepo.Update(c.Request.Context(), machine); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to update heartbeat", "reason": err.Error()})
		return
	}

	// === EVENT BROADCASTING ===
	// TODO: Broadcast machine.heartbeat event (Ruby: BroadcastEventService.call)

	// === SUCCESS RESPONSE ===
	// Trả về thông tin heartbeat với status
	c.JSON(http.StatusOK, gin.H{
		"message":          "heartbeat updated successfully",
		"machine":          machine,
		"heartbeat_at":     machine.LastHeartbeatAt,
		"next_heartbeat":   machine.NextHeartbeatAt,
		"heartbeat_status": machine.GetHeartbeatStatus(),
	})
}

// ActivateMachine kích hoạt machine (tương tự Ruby machine.activate!)
func (h *MachineHandler) ActivateMachine(c *gin.Context) {
	machineID := c.Param("machine_id")

	// === MACHINE ID VALIDATION ===
	machineUUID, err := uuid.Parse(machineID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid machine ID"})
		return
	}

	// === MACHINE RETRIEVAL ===
	machine, err := h.machineRepo.GetByID(c.Request.Context(), machineUUID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "machine not found"})
		return
	}

	// === ACTIVATION LOGIC ===
	// Check xem machine đã active chưa
	if machine.IsActive() {
		c.JSON(http.StatusUnprocessableEntity, gin.H{
			"error": "machine is already active",
		})
		return
	}

	// Activate machine sử dụng business logic
	machine.Activate()

	// === DATABASE UPDATE ===
	if err := h.machineRepo.Update(c.Request.Context(), machine); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to activate machine", "reason": err.Error()})
		return
	}

	// === SUCCESS RESPONSE ===
	c.JSON(http.StatusOK, gin.H{
		"message": "machine activated successfully",
		"machine": machine,
	})
}

// DeactivateMachine vô hiệu hóa machine (tương tự Ruby machine.deactivate!)
func (h *MachineHandler) DeactivateMachine(c *gin.Context) {
	machineID := c.Param("machine_id")

	// === MACHINE ID VALIDATION ===
	machineUUID, err := uuid.Parse(machineID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid machine ID"})
		return
	}

	// === MACHINE RETRIEVAL ===
	machine, err := h.machineRepo.GetByID(c.Request.Context(), machineUUID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "machine not found"})
		return
	}

	// === DEACTIVATION LOGIC ===
	// Check xem machine có active không
	if machine.IsInactive() {
		c.JSON(http.StatusUnprocessableEntity, gin.H{
			"error": "machine is already inactive",
		})
		return
	}

	// Deactivate machine sử dụng business logic
	machine.Deactivate()

	// === DATABASE UPDATE ===
	if err := h.machineRepo.Update(c.Request.Context(), machine); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to deactivate machine", "reason": err.Error()})
		return
	}

	// === SUCCESS RESPONSE ===
	c.JSON(http.StatusOK, gin.H{
		"message": "machine deactivated successfully",
		"machine": machine,
	})
}

// GetMachineProcesses gets all processes for a machine
func (h *MachineHandler) GetMachineProcesses(c *gin.Context) {
	machineID := c.Param("machine_id")

	_, err := uuid.Parse(machineID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid machine ID"})
		return
	}

	// TODO: Implement when MachineProcess repository is available
	// processes, err := h.machineProcessRepo.GetByMachineID(c.Request.Context(), machineID)

	c.JSON(http.StatusOK, gin.H{
		"machine_id": machineID,
		"processes":  []interface{}{}, // Placeholder
	})
}

// GetMachineComponents gets all components for a machine
func (h *MachineHandler) GetMachineComponents(c *gin.Context) {
	machineID := c.Param("machine_id")

	_, err := uuid.Parse(machineID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid machine ID"})
		return
	}

	// TODO: Implement when MachineComponent repository is available
	// components, err := h.machineComponentRepo.GetByMachineID(c.Request.Context(), machineID)

	c.JSON(http.StatusOK, gin.H{
		"machine_id": machineID,
		"components": []interface{}{}, // Placeholder
	})
}

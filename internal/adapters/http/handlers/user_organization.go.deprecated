package handlers

import (
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gokeys/gokeys/internal/adapters/http/middleware"
	"github.com/gokeys/gokeys/internal/adapters/http/responses"
	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/gokeys/gokeys/internal/domain/repositories"
	"github.com/gokeys/gokeys/internal/domain/services"
	"github.com/google/uuid"
)

// UserOrganizationHandler handles user-organization relationship management
// This is the core handler for constraint-based authorization system
type UserOrganizationHandler struct {
	serviceCoordinator *services.ServiceCoordinator
}

// NewUserOrganizationHandler creates a new user organization handler
func NewUserOrganizationHandler(serviceCoordinator *services.ServiceCoordinator) *UserOrganizationHandler {
	return &UserOrganizationHandler{
		serviceCoordinator: serviceCoordinator,
	}
}

// Go-style request structs for user organization management

// UserOrganizationCreateRequest represents request to create user-organization relationship
// @Description Request payload for creating user-organization relationship with permissions
type UserOrganizationCreateRequest struct {
	UserID         string    `json:"user_id" binding:"required,uuid" example:"550e8400-e29b-41d4-a716-************"`
	OrganizationID *string   `json:"organization_id,omitempty" binding:"omitempty,uuid" example:"550e8400-e29b-41d4-a716-************"`
	Role           string    `json:"role" binding:"required,oneof=admin sales_agent member viewer customer" example:"member"`
	ResourceScope  string    `json:"resource_scope" binding:"required,oneof=specific type_wildcard global owner" example:"specific"`
	ResourceType   string    `json:"resource_type" binding:"required" example:"product"`
	ResourceID     *string   `json:"resource_id,omitempty" binding:"omitempty,uuid" example:"550e8400-e29b-41d4-a716-************"`
	AllowedActions []string  `json:"allowed_actions" binding:"required,min=1" example:"read,update"`
	ExpiresAt      *string   `json:"expires_at,omitempty" example:"2025-12-31T23:59:59Z"`
}

// UserOrganizationUpdateRequest represents request to update user-organization relationship
// @Description Request payload for updating user-organization relationship permissions
type UserOrganizationUpdateRequest struct {
	Role           *string   `json:"role,omitempty" binding:"omitempty,oneof=admin sales_agent member viewer customer" example:"admin"`
	ResourceScope  *string   `json:"resource_scope,omitempty" binding:"omitempty,oneof=specific type_wildcard global owner" example:"type_wildcard"`
	ResourceType   *string   `json:"resource_type,omitempty" example:"license"`
	ResourceID     *string   `json:"resource_id,omitempty" binding:"omitempty,uuid" example:"550e8400-e29b-41d4-a716-************"`
	AllowedActions []string  `json:"allowed_actions,omitempty" example:"*"`
	Active         *bool     `json:"active,omitempty" example:"true"`
	ExpiresAt      *string   `json:"expires_at,omitempty" example:"2026-12-31T23:59:59Z"`
}

// Go-style response structs

// UserOrganizationResponse represents user-organization relationship in API responses
// @Description User-organization relationship with constraint-based permissions
type UserOrganizationResponse struct {
	ID             string    `json:"id" example:"550e8400-e29b-41d4-a716-************"`
	UserID         string    `json:"user_id" example:"550e8400-e29b-41d4-a716-************"`
	OrganizationID *string   `json:"organization_id,omitempty" example:"550e8400-e29b-41d4-a716-************"`
	Role           string    `json:"role" example:"member"`
	ResourceScope  string    `json:"resource_scope" example:"specific"`
	ResourceType   string    `json:"resource_type" example:"product"`
	ResourceID     *string   `json:"resource_id,omitempty" example:"550e8400-e29b-41d4-a716-************"`
	AllowedActions []string  `json:"allowed_actions" example:"read,update"`
	Active         bool      `json:"active" example:"true"`
	ExpiresAt      *string   `json:"expires_at,omitempty" example:"2025-12-31T23:59:59Z"`
	GrantedBy      *string   `json:"granted_by,omitempty" example:"550e8400-e29b-41d4-a716-446655440004"`
	Created        string    `json:"created_at" example:"2025-01-01T00:00:00Z"`
	Updated        string    `json:"updated_at" example:"2025-01-15T10:30:00Z"`
}

// UserOrganizationListResponse represents paginated list of user-organization relationships
// @Description Paginated list of user-organization relationships
type UserOrganizationListResponse struct {
	UserOrganizations []UserOrganizationResponse `json:"user_organizations"`
	Pagination        responses.PaginationMeta   `json:"pagination"`
}

// Helper function to convert entity to response
func (h *UserOrganizationHandler) toUserOrganizationResponse(uo *entities.UserOrganization) UserOrganizationResponse {
	response := UserOrganizationResponse{
		ID:             uo.ID,
		UserID:         uo.UserID,
		OrganizationID: uo.OrganizationID,
		Role:           uo.Role,
		ResourceScope:  uo.ResourceScope,
		ResourceType:   uo.ResourceType,
		ResourceID:     uo.ResourceID,
		AllowedActions: uo.AllowedActions,
		Active:         uo.Active,
		GrantedBy:      uo.GrantedBy,
		Created:        uo.CreatedAt.Format("2006-01-02T15:04:05Z"),
		Updated:        uo.UpdatedAt.Format("2006-01-02T15:04:05Z"),
	}

	// Handle optional time fields
	if uo.ExpiresAt != nil {
		expiresAt := uo.ExpiresAt.Format("2006-01-02T15:04:05Z")
		response.ExpiresAt = &expiresAt
	}

	return response
}

// ListUserOrganizations handles GET /api/v1/user-organizations
// @Summary List user-organization relationships
// @Description Get paginated list of user-organization relationships for constraint-based authorization
// @Tags UserOrganizations
// @Accept json
// @Produce json
// @Param page query int false "Page number (default: 1)" example(1)
// @Param per_page query int false "Items per page (default: 25, max: 100)" example(25)
// @Param sort_by query string false "Sort field (default: created_at)" example("created_at")
// @Param sort_order query string false "Sort order: ASC or DESC (default: DESC)" example("DESC")
// @Param user_id query string false "Filter by user ID" example("550e8400-e29b-41d4-a716-************")
// @Param organization_id query string false "Filter by organization ID" example("550e8400-e29b-41d4-a716-************")
// @Param role query string false "Filter by role" example("member")
// @Param resource_type query string false "Filter by resource type" example("product")
// @Param active query bool false "Filter by active status" example("true")
// @Success 200 {object} UserOrganizationListResponse "List retrieved successfully"
// @Failure 401 {object} map[string]interface{} "Authentication required"
// @Failure 403 {object} map[string]interface{} "Insufficient permissions"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Security BearerAuth
// @Router /user-organizations [get]
func (h *UserOrganizationHandler) ListUserOrganizations(c *gin.Context) {
	// Parse pagination parameters
	page := 1
	if pageStr := c.Query("page"); pageStr != "" {
		if p, err := strconv.Atoi(pageStr); err == nil && p > 0 {
			page = p
		}
	}

	perPage := 25
	if perPageStr := c.Query("per_page"); perPageStr != "" {
		if pp, err := strconv.Atoi(perPageStr); err == nil && pp > 0 && pp <= 100 {
			perPage = pp
		}
	}

	// Parse filters
	userID := c.Query("user_id")
	organizationID := c.Query("organization_id")
	role := c.Query("role")
	resourceType := c.Query("resource_type")
	activeStr := c.Query("active")

	// Build filter
	filter := repositories.ListFilter{
		Page:      page,
		PageSize:  perPage,
		SortBy:    c.DefaultQuery("sort_by", "created_at"),
		SortOrder: c.DefaultQuery("sort_order", "DESC"),
		Filters:   make(map[string]interface{}),
	}

	// Add specific filters
	if userID != "" {
		if uid, err := uuid.Parse(userID); err == nil {
			filter.Filters["user_id"] = uid
		}
	}
	if organizationID != "" {
		if oid, err := uuid.Parse(organizationID); err == nil {
			filter.Filters["organization_id"] = oid
		}
	}
	if role != "" {
		filter.Filters["role"] = role
	}
	if resourceType != "" {
		filter.Filters["resource_type"] = resourceType
	}
	if activeStr != "" {
		if active, err := strconv.ParseBool(activeStr); err == nil {
			filter.Filters["active"] = active
		}
	}

	// Get user organizations from repository
	userOrganizations, total, err := h.serviceCoordinator.Repositories.UserOrganization().List(c.Request.Context(), filter)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "retrieval_failed",
			"message": "Failed to retrieve user organizations",
			"details": err.Error(),
		})
		return
	}

	// Convert to response format
	userOrgResponses := make([]UserOrganizationResponse, len(userOrganizations))
	for i, uo := range userOrganizations {
		userOrgResponses[i] = h.toUserOrganizationResponse(uo)
	}

	// Calculate pagination
	totalPages := (total + int64(perPage) - 1) / int64(perPage)

	response := UserOrganizationListResponse{
		UserOrganizations: userOrgResponses,
		Pagination: responses.PaginationMeta{
			Page:       page,
			PerPage:    perPage,
			Total:      total,
			TotalPages: int64(totalPages),
		},
	}

	c.JSON(http.StatusOK, response)
}

// CreateUserOrganization handles POST /api/v1/user-organizations
// @Summary Create user-organization relationship
// @Description Create a new user-organization relationship with constraint-based permissions
// @Tags UserOrganizations
// @Accept json
// @Produce json
// @Param request body UserOrganizationCreateRequest true "User organization creation request"
// @Success 201 {object} UserOrganizationResponse "User organization relationship created successfully"
// @Failure 400 {object} map[string]interface{} "Invalid request data"
// @Failure 401 {object} map[string]interface{} "Authentication required"
// @Failure 403 {object} map[string]interface{} "Insufficient permissions"
// @Failure 409 {object} map[string]interface{} "Relationship already exists"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Security BearerAuth
// @Router /user-organizations [post]
func (h *UserOrganizationHandler) CreateUserOrganization(c *gin.Context) {
	var req UserOrganizationCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "validation_failed",
			"message": "Invalid request data",
			"details": err.Error(),
		})
		return
	}

	// Get granter user ID from auth context
	subject := middleware.GetSubject(c)
	if subject == nil {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error":   "unauthorized",
			"message": "Authentication required",
		})
		return
	}

	// Parse user ID
	userID, err := uuid.Parse(req.UserID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "validation_failed",
			"message": "Invalid user ID format",
		})
		return
	}

	// Parse organization ID if provided
	var organizationID *uuid.UUID
	if req.OrganizationID != nil {
		if oid, err := uuid.Parse(*req.OrganizationID); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"error":   "validation_failed",
				"message": "Invalid organization ID format",
			})
			return
		} else {
			organizationID = &oid
		}
	}

	// Parse resource ID if provided
	var resourceID *uuid.UUID
	if req.ResourceID != nil {
		if rid, err := uuid.Parse(*req.ResourceID); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"error":   "validation_failed",
				"message": "Invalid resource ID format",
			})
			return
		} else {
			resourceID = &rid
		}
	}

	// Parse expires_at if provided
	var expiresAt *time.Time
	if req.ExpiresAt != nil {
		if et, err := time.Parse("2006-01-02T15:04:05Z", *req.ExpiresAt); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"error":   "validation_failed",
				"message": "Invalid expires_at format, use RFC3339",
			})
			return
		} else {
			expiresAt = &et
		}
	}

	// Create user organization entity
	userOrg := &entities.UserOrganization{
		UserID:         userID.String(),
		Role:           req.Role,
		ResourceScope:  req.ResourceScope,
		ResourceType:   req.ResourceType,
		AllowedActions: req.AllowedActions,
		Active:         true,
		ExpiresAt:      expiresAt,
		GrantedBy:      &subject.ID,
		CreatedAt:      time.Now(),
		UpdatedAt:      time.Now(),
	}

	// Set optional fields
	if organizationID != nil {
		orgIDStr := organizationID.String()
		userOrg.OrganizationID = &orgIDStr
	}
	if resourceID != nil {
		resIDStr := resourceID.String()
		userOrg.ResourceID = &resIDStr
	}

	// Validate constraints
	if err := userOrg.ValidateConstraints(); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "validation_failed",
			"message": "Invalid constraint configuration",
			"details": err.Error(),
		})
		return
	}

	// Save to repository
	if err := h.serviceCoordinator.Repositories.UserOrganization().Create(c.Request.Context(), userOrg); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "creation_failed",
			"message": "Failed to create user organization relationship",
			"details": err.Error(),
		})
		return
	}

	// Return created relationship
	response := h.toUserOrganizationResponse(userOrg)
	c.Header("Location", "/api/v1/user-organizations/"+userOrg.ID)
	c.JSON(http.StatusCreated, response)
}

// GetUserOrganization handles GET /api/v1/user-organizations/:id
// @Summary Get user-organization relationship by ID
// @Description Retrieve detailed information about a specific user-organization relationship
// @Tags UserOrganizations
// @Accept json
// @Produce json
// @Param id path string true "User Organization ID" example("550e8400-e29b-41d4-a716-************")
// @Success 200 {object} UserOrganizationResponse "User organization relationship retrieved successfully"
// @Failure 400 {object} map[string]interface{} "Invalid ID format"
// @Failure 401 {object} map[string]interface{} "Authentication required"
// @Failure 404 {object} map[string]interface{} "Relationship not found"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Security BearerAuth
// @Router /user-organizations/{id} [get]
func (h *UserOrganizationHandler) GetUserOrganization(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "invalid_parameter",
			"message": "Invalid user organization ID format",
		})
		return
	}

	// Get user organization from repository
	userOrg, err := h.serviceCoordinator.Repositories.UserOrganization().GetByID(c.Request.Context(), id)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error":   "not_found",
			"message": "User organization relationship not found",
		})
		return
	}

	// Return relationship
	response := h.toUserOrganizationResponse(userOrg)
	c.JSON(http.StatusOK, response)
}

// UpdateUserOrganization handles PUT /api/v1/user-organizations/:id
// @Summary Update user-organization relationship
// @Description Update an existing user-organization relationship's permissions
// @Tags UserOrganizations
// @Accept json
// @Produce json
// @Param id path string true "User Organization ID" example("550e8400-e29b-41d4-a716-************")
// @Param request body UserOrganizationUpdateRequest true "User organization update request"
// @Success 200 {object} UserOrganizationResponse "User organization relationship updated successfully"
// @Failure 400 {object} map[string]interface{} "Invalid request data or ID"
// @Failure 401 {object} map[string]interface{} "Authentication required"
// @Failure 404 {object} map[string]interface{} "Relationship not found"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Security BearerAuth
// @Router /user-organizations/{id} [put]
func (h *UserOrganizationHandler) UpdateUserOrganization(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "invalid_parameter",
			"message": "Invalid user organization ID format",
		})
		return
	}

	var req UserOrganizationUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "validation_failed",
			"message": "Invalid request data",
			"details": err.Error(),
		})
		return
	}

	// Get existing user organization
	userOrg, err := h.serviceCoordinator.Repositories.UserOrganization().GetByID(c.Request.Context(), id)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error":   "not_found",
			"message": "User organization relationship not found",
		})
		return
	}

	// Update fields (only non-nil values)
	if req.Role != nil {
		userOrg.Role = *req.Role
	}
	if req.ResourceScope != nil {
		userOrg.ResourceScope = *req.ResourceScope
	}
	if req.ResourceType != nil {
		userOrg.ResourceType = *req.ResourceType
	}
	if req.ResourceID != nil {
		if *req.ResourceID == "" {
			userOrg.ResourceID = nil
		} else {
			if rid, err := uuid.Parse(*req.ResourceID); err == nil {
				resourceIDStr := rid.String()
				userOrg.ResourceID = &resourceIDStr
			}
		}
	}
	if req.AllowedActions != nil {
		userOrg.AllowedActions = req.AllowedActions
	}
	if req.Active != nil {
		userOrg.Active = *req.Active
	}
	if req.ExpiresAt != nil {
		if *req.ExpiresAt == "" {
			userOrg.ExpiresAt = nil
		} else {
			if et, err := time.Parse("2006-01-02T15:04:05Z", *req.ExpiresAt); err == nil {
				userOrg.ExpiresAt = &et
			}
		}
	}
	userOrg.UpdatedAt = time.Now()

	// Validate constraints
	if err := userOrg.ValidateConstraints(); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "validation_failed",
			"message": "Invalid constraint configuration",
			"details": err.Error(),
		})
		return
	}

	// Save to repository
	if err := h.serviceCoordinator.Repositories.UserOrganization().Update(c.Request.Context(), userOrg); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "update_failed",
			"message": "Failed to update user organization relationship",
			"details": err.Error(),
		})
		return
	}

	// Return updated relationship
	response := h.toUserOrganizationResponse(userOrg)
	c.JSON(http.StatusOK, response)
}

// DeleteUserOrganization handles DELETE /api/v1/user-organizations/:id
// @Summary Delete user-organization relationship
// @Description Delete a user-organization relationship (soft delete)
// @Tags UserOrganizations
// @Accept json
// @Produce json
// @Param id path string true "User Organization ID" example("550e8400-e29b-41d4-a716-************")
// @Success 204 "User organization relationship deleted successfully"
// @Failure 400 {object} map[string]interface{} "Invalid ID format"
// @Failure 401 {object} map[string]interface{} "Authentication required"
// @Failure 404 {object} map[string]interface{} "Relationship not found"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Security BearerAuth
// @Router /user-organizations/{id} [delete]
func (h *UserOrganizationHandler) DeleteUserOrganization(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		responses.RenderBadRequest(c, "Invalid user organization ID format")
		return
	}

	// Get existing user organization to verify it exists
	_, err = h.serviceCoordinator.Repositories.UserOrganization().GetByID(c.Request.Context(), id)
	if err != nil {
		responses.RenderNotFound(c, "User organization relationship not found")
		return
	}

	// Delete user organization relationship
	if err := h.serviceCoordinator.Repositories.UserOrganization().Delete(c.Request.Context(), id); err != nil {
		responses.RenderInternalError(c, "Failed to delete user organization relationship: "+err.Error())
		return
	}

	c.Status(http.StatusNoContent)
}

// GetUserPermissions handles GET /api/v1/users/:user_id/permissions
// @Summary Get user's effective permissions
// @Description Get all effective permissions for a user across all organizations and resources
// @Tags UserOrganizations
// @Accept json
// @Produce json
// @Param user_id path string true "User ID" example("550e8400-e29b-41d4-a716-************")
// @Param organization_id query string false "Filter by organization ID" example("550e8400-e29b-41d4-a716-************")
// @Success 200 {object} UserOrganizationListResponse "User permissions retrieved successfully"
// @Failure 400 {object} map[string]interface{} "Invalid user ID format"
// @Failure 401 {object} map[string]interface{} "Authentication required"
// @Failure 404 {object} map[string]interface{} "User not found"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Security BearerAuth
// @Router /users/{user_id}/permissions [get]
func (h *UserOrganizationHandler) GetUserPermissions(c *gin.Context) {
	userIDStr := c.Param("user_id")
	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "invalid_parameter",
			"message": "Invalid user ID format",
		})
		return
	}

	// Build filter for user's permissions
	filter := repositories.ListFilter{
		Filters: map[string]interface{}{
			"user_id": userID,
			"active":  true, // Only active relationships
		},
	}

	// Optional organization filter
	if orgIDStr := c.Query("organization_id"); orgIDStr != "" {
		if orgID, err := uuid.Parse(orgIDStr); err == nil {
			filter.Filters["organization_id"] = orgID
		}
	}

	// Get user organizations (permissions)
	userOrganizations, total, err := h.serviceCoordinator.Repositories.UserOrganization().List(c.Request.Context(), filter)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "retrieval_failed",
			"message": "Failed to retrieve user permissions",
			"details": err.Error(),
		})
		return
	}

	// Convert to response format
	userOrgResponses := make([]UserOrganizationResponse, len(userOrganizations))
	for i, uo := range userOrganizations {
		userOrgResponses[i] = h.toUserOrganizationResponse(uo)
	}

	response := UserOrganizationListResponse{
		UserOrganizations: userOrgResponses,
		Pagination: responses.PaginationMeta{
			Page:       1,
			PerPage:    int(total),
			Total:      total,
			TotalPages: 1,
		},
	}

	c.JSON(http.StatusOK, response)
}
package handlers

import (
	"bytes"
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/gokeys/gokeys/internal/domain/repositories"
)

// MockLicenseRepository for testing
type MockLicenseRepository struct {
	mock.Mock
}

func (m *MockLicenseRepository) Create(ctx context.Context, license *entities.License) error {
	args := m.Called(ctx, license)
	return args.Error(0)
}

func (m *MockLicenseRepository) GetByID(ctx context.Context, id uuid.UUID) (*entities.License, error) {
	args := m.Called(ctx, id)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*entities.License), args.Error(1)
}

func (m *MockLicenseRepository) Update(ctx context.Context, license *entities.License) error {
	args := m.Called(ctx, license)
	return args.Error(0)
}

func (m *MockLicenseRepository) Delete(ctx context.Context, id uuid.UUID) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockLicenseRepository) SoftDelete(ctx context.Context, id uuid.UUID) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockLicenseRepository) List(ctx context.Context, filter repositories.ListFilter) ([]*entities.License, int64, error) {
	args := m.Called(ctx, filter)
	return args.Get(0).([]*entities.License), args.Get(1).(int64), args.Error(2)
}

func (m *MockLicenseRepository) Count(ctx context.Context, filter repositories.ListFilter) (int64, error) {
	args := m.Called(ctx, filter)
	return args.Get(0).(int64), args.Error(1)
}

func (m *MockLicenseRepository) Exists(ctx context.Context, id uuid.UUID) (bool, error) {
	args := m.Called(ctx, id)
	return args.Get(0).(bool), args.Error(1)
}

func (m *MockLicenseRepository) GetByKey(ctx context.Context, key string) (*entities.License, error) {
	args := m.Called(ctx, key)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*entities.License), args.Error(1)
}

func (m *MockLicenseRepository) GetByPolicy(ctx context.Context, policyID uuid.UUID) ([]*entities.License, error) {
	args := m.Called(ctx, policyID)
	return args.Get(0).([]*entities.License), args.Error(1)
}

func (m *MockLicenseRepository) GetExpiring(ctx context.Context, organizationID uuid.UUID, beforeDate time.Time) ([]*entities.License, error) {
	args := m.Called(ctx, organizationID, beforeDate)
	return args.Get(0).([]*entities.License), args.Error(1)
}

func (m *MockLicenseRepository) UpdateLastValidated(ctx context.Context, licenseID uuid.UUID) error {
	args := m.Called(ctx, licenseID)
	return args.Error(0)
}

func (m *MockLicenseRepository) IncrementValidationCount(ctx context.Context, licenseID uuid.UUID) error {
	args := m.Called(ctx, licenseID)
	return args.Error(0)
}

// Test helper to create a test license
func createTestLicense() *entities.License {
	now := time.Now()
	expiry := now.AddDate(1, 0, 0) // 1 year from now

	return &entities.License{
		ID:             uuid.New().String(),
		OrganizationID: uuid.New().String(),
		ProductID:      uuid.New().String(),
		PolicyID:       uuid.New().String(),
		Key:            "TEST-KEY-123456",
		Name:           stringPtr("Test License"),
		OwnerType:      entities.LicenseOwnerTypeUser,
		OwnerID:        uuid.New().String(),
		Status:         entities.LicenseStatusActive,
		Suspended:      false,
		Protected:      false,
		Uses:           5,
		ExpiresAt:      &expiry,
		CreatedAt:      now,
		UpdatedAt:      now,
	}
}

func TestLicenseHandler_SuspendLicense(t *testing.T) {
	gin.SetMode(gin.TestMode)

	tests := []struct {
		name           string
		licenseID      string
		setupMock      func(*MockLicenseRepository)
		expectedStatus int
		expectedBody   string
	}{
		{
			name:      "successful suspend",
			licenseID: uuid.New().String(),
			setupMock: func(repo *MockLicenseRepository) {
				license := createTestLicense()
				license.Suspended = false

				repo.On("GetByID", mock.Anything, mock.Anything).Return(license, nil)
				repo.On("Update", mock.Anything, mock.MatchedBy(func(l *entities.License) bool {
					return l.Suspended == true
				})).Return(nil)
			},
			expectedStatus: http.StatusOK,
		},
		{
			name:      "license already suspended",
			licenseID: uuid.New().String(),
			setupMock: func(repo *MockLicenseRepository) {
				license := createTestLicense()
				license.Suspended = true

				repo.On("GetByID", mock.Anything, mock.Anything).Return(license, nil)
			},
			expectedStatus: http.StatusUnprocessableEntity,
			expectedBody:   "license is already suspended",
		},
		{
			name:           "invalid license ID",
			licenseID:      "invalid-uuid",
			setupMock:      func(repo *MockLicenseRepository) {},
			expectedStatus: http.StatusBadRequest,
			expectedBody:   "invalid license ID",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockRepo := &MockLicenseRepository{}
			tt.setupMock(mockRepo)

			handler := &LicenseHandler{
				licenseRepo: mockRepo,
			}

			router := gin.New()
			router.POST("/licenses/:license_id/actions/suspend", handler.SuspendLicense)

			req, _ := http.NewRequest("POST", "/licenses/"+tt.licenseID+"/actions/suspend", nil)
			w := httptest.NewRecorder()

			router.ServeHTTP(w, req)

			assert.Equal(t, tt.expectedStatus, w.Code)
			if tt.expectedBody != "" {
				assert.Contains(t, w.Body.String(), tt.expectedBody)
			}

			mockRepo.AssertExpectations(t)
		})
	}
}

func TestLicenseHandler_ReinstateLicense(t *testing.T) {
	gin.SetMode(gin.TestMode)

	tests := []struct {
		name           string
		licenseID      string
		setupMock      func(*MockLicenseRepository)
		expectedStatus int
		expectedBody   string
	}{
		{
			name:      "successful reinstate",
			licenseID: uuid.New().String(),
			setupMock: func(repo *MockLicenseRepository) {
				license := createTestLicense()
				license.Suspended = true

				repo.On("GetByID", mock.Anything, mock.Anything).Return(license, nil)
				repo.On("Update", mock.Anything, mock.MatchedBy(func(l *entities.License) bool {
					return l.Suspended == false
				})).Return(nil)
			},
			expectedStatus: http.StatusOK,
		},
		{
			name:      "license not suspended",
			licenseID: uuid.New().String(),
			setupMock: func(repo *MockLicenseRepository) {
				license := createTestLicense()
				license.Suspended = false

				repo.On("GetByID", mock.Anything, mock.Anything).Return(license, nil)
			},
			expectedStatus: http.StatusUnprocessableEntity,
			expectedBody:   "license is not suspended",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockRepo := &MockLicenseRepository{}
			tt.setupMock(mockRepo)

			handler := &LicenseHandler{
				licenseRepo: mockRepo,
			}

			router := gin.New()
			router.POST("/licenses/:license_id/actions/reinstate", handler.ReinstateLicense)

			req, _ := http.NewRequest("POST", "/licenses/"+tt.licenseID+"/actions/reinstate", nil)
			w := httptest.NewRecorder()

			router.ServeHTTP(w, req)

			assert.Equal(t, tt.expectedStatus, w.Code)
			if tt.expectedBody != "" {
				assert.Contains(t, w.Body.String(), tt.expectedBody)
			}

			mockRepo.AssertExpectations(t)
		})
	}
}

func TestLicenseHandler_IncrementUsage(t *testing.T) {
	gin.SetMode(gin.TestMode)

	tests := []struct {
		name           string
		licenseID      string
		requestBody    map[string]interface{}
		setupMock      func(*MockLicenseRepository)
		expectedStatus int
		expectedUsage  int
	}{
		{
			name:      "increment by default (1)",
			licenseID: uuid.New().String(),
			requestBody: map[string]interface{}{
				"meta": map[string]interface{}{},
			},
			setupMock: func(repo *MockLicenseRepository) {
				license := createTestLicense()
				license.Uses = 5

				repo.On("GetByID", mock.Anything, mock.Anything).Return(license, nil)
				repo.On("Update", mock.Anything, mock.MatchedBy(func(l *entities.License) bool {
					return l.Uses == 6
				})).Return(nil)
			},
			expectedStatus: http.StatusOK,
			expectedUsage:  6,
		},
		{
			name:      "increment by 10",
			licenseID: uuid.New().String(),
			requestBody: map[string]interface{}{
				"meta": map[string]interface{}{
					"increment": 10,
				},
			},
			setupMock: func(repo *MockLicenseRepository) {
				license := createTestLicense()
				license.Uses = 5

				repo.On("GetByID", mock.Anything, mock.Anything).Return(license, nil)
				repo.On("Update", mock.Anything, mock.MatchedBy(func(l *entities.License) bool {
					return l.Uses == 15
				})).Return(nil)
			},
			expectedStatus: http.StatusOK,
			expectedUsage:  15,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockRepo := &MockLicenseRepository{}
			tt.setupMock(mockRepo)

			handler := &LicenseHandler{
				licenseRepo: mockRepo,
			}

			router := gin.New()
			router.POST("/licenses/:license_id/actions/increment-usage", handler.IncrementUsage)

			body, _ := json.Marshal(tt.requestBody)
			req, _ := http.NewRequest("POST", "/licenses/"+tt.licenseID+"/actions/increment-usage", bytes.NewBuffer(body))
			req.Header.Set("Content-Type", "application/json")
			w := httptest.NewRecorder()

			router.ServeHTTP(w, req)

			assert.Equal(t, tt.expectedStatus, w.Code)

			mockRepo.AssertExpectations(t)
		})
	}
}

package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/gokeys/gokeys/internal/domain/repositories"
	"github.com/google/uuid"
)

// PolicyHandler handles policy-related HTTP requests following keygen-api patterns
type PolicyHandler struct {
	policyRepo  repositories.PolicyRepository
	productRepo repositories.ProductRepository
}

// NewPolicyHandler creates a new policy handler
func NewPolicyHandler(
	policyRepo repositories.PolicyRepository,
	productRepo repositories.ProductRepository,
) *PolicyHandler {
	return &PolicyHandler{
		policyRepo:  policyRepo,
		productRepo: productRepo,
	}
}

// ListPolicies lists all policies with optional product scoping
func (h *PolicyHandler) ListPolicies(c *gin.Context) {
	organizationID := c.Param("organization_id")
	productID := c.Query("product") // Optional product filter from Ruby has_scope

	// Parse pagination parameters
	page, _ := strconv.Atoi(c.Defa<PERSON>("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))

	// Build filter
	filter := repositories.DefaultListFilter()
	filter.Page = page
	filter.PageSize = pageSize
	filter.Filters["organization_id = ?"] = organizationID

	// Apply product scope if specified (Ruby: has_scope(:product))
	if productID != "" {
		filter.Filters["product_id = ?"] = productID
	}

	// Get policies
	policies, total, err := h.policyRepo.List(c.Request.Context(), filter)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to get policies", "reason": err.Error()})
		return
	}

	// Calculate pagination
	totalPages := (int(total) + pageSize - 1) / pageSize

	c.JSON(http.StatusOK, gin.H{
		"policies": policies,
		"pagination": gin.H{
			"page":        page,
			"page_size":   pageSize,
			"total":       total,
			"total_pages": totalPages,
		},
	})
}

// GetPolicy retrieves a specific policy by ID
func (h *PolicyHandler) GetPolicy(c *gin.Context) {
	policyID := c.Param("policy_id")

	policyUUID, err := uuid.Parse(policyID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid policy ID"})
		return
	}

	policy, err := h.policyRepo.GetByID(c.Request.Context(), policyUUID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "policy not found"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"policy": policy})
}

// CreatePolicy creates a new policy following keygen-api patterns
func (h *PolicyHandler) CreatePolicy(c *gin.Context) {
	organizationID := c.Param("organization_id")

	var requestData struct {
		// Core attributes mapping from Ruby typed_params
		ProductID    string  `json:"product_id" binding:"required"`
		Name         string  `json:"name" binding:"required"`
		Scheme       *string `json:"scheme"`
		Encrypted    *bool   `json:"encrypted"`
		UsePool      *bool   `json:"use_pool"`
		Duration     *int    `json:"duration"`
		Strict       *bool   `json:"strict"`
		Floating     *bool   `json:"floating"`
		Protected    *bool   `json:"protected"`
		MaxMachines  *int    `json:"max_machines"`
		MaxProcesses *int    `json:"max_processes"`
		MaxUsers     *int    `json:"max_users"`
		MaxCores     *int    `json:"max_cores"`
		MaxUses      *int    `json:"max_uses"`

		// Strategy configurations (Ruby: matching keygen-api strategy enums)
		MachineUniquenessStrategy   *string `json:"machine_uniqueness_strategy"`
		MachineMatchingStrategy     *string `json:"machine_matching_strategy"`
		ComponentUniquenessStrategy *string `json:"component_uniqueness_strategy"`
		ComponentMatchingStrategy   *string `json:"component_matching_strategy"`
		ExpirationStrategy          *string `json:"expiration_strategy"`
		ExpirationBasis             *string `json:"expiration_basis"`
		RenewalBasis                *string `json:"renewal_basis"`
		TransferStrategy            *string `json:"transfer_strategy"`
		AuthenticationStrategy      *string `json:"authentication_strategy"`
		ProcessLeasingStrategy      *string `json:"process_leasing_strategy"`
		MachineLeasingStrategy      *string `json:"machine_leasing_strategy"`
		OverageStrategy             *string `json:"overage_strategy"`

		// Scope requirements (Ruby: require_*_scope fields)
		RequireProductScope     *bool `json:"require_product_scope"`
		RequirePolicyScope      *bool `json:"require_policy_scope"`
		RequireMachineScope     *bool `json:"require_machine_scope"`
		RequireFingerprintScope *bool `json:"require_fingerprint_scope"`
		RequireComponentsScope  *bool `json:"require_components_scope"`
		RequireUserScope        *bool `json:"require_user_scope"`
		RequireChecksumScope    *bool `json:"require_checksum_scope"`
		RequireVersionScope     *bool `json:"require_version_scope"`

		// Check-in and heartbeat configurations (Ruby: check_in_* and heartbeat_* fields)
		RequireCheckIn                *bool   `json:"require_check_in"`
		CheckInInterval               *string `json:"check_in_interval"`
		CheckInIntervalCount          *int    `json:"check_in_interval_count"`
		HeartbeatDuration             *int    `json:"heartbeat_duration"`
		HeartbeatCullStrategy         *string `json:"heartbeat_cull_strategy"`
		HeartbeatResurrectionStrategy *string `json:"heartbeat_resurrection_strategy"`
		HeartbeatBasis                *string `json:"heartbeat_basis"`
		RequireHeartbeat              *bool   `json:"require_heartbeat"`

		Metadata entities.Metadata `json:"metadata"`
	}

	if err := c.ShouldBindJSON(&requestData); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid request", "reason": err.Error()})
		return
	}

	// Validate product exists
	productUUID, err := uuid.Parse(requestData.ProductID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid product ID"})
		return
	}

	product, err := h.productRepo.GetByID(c.Request.Context(), productUUID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "product not found"})
		return
	}

	if product.OrganizationID != organizationID {
		c.JSON(http.StatusBadRequest, gin.H{"error": "product does not belong to this organization"})
		return
	}

	// Create policy entity with all fields mapping from Ruby Policy model
	policy := &entities.Policy{
		OrganizationID:                organizationID,
		ProductID:                     requestData.ProductID,
		Name:                          requestData.Name,
		Duration:                      requestData.Duration,
		Protected:                     requestData.Protected,
		MaxMachines:                   requestData.MaxMachines,
		MaxProcesses:                  requestData.MaxProcesses,
		MaxUsers:                      requestData.MaxUsers,
		MaxCores:                      requestData.MaxCores,
		MaxUses:                       requestData.MaxUses,
		MachineUniquenessStrategy:     requestData.MachineUniquenessStrategy,
		MachineMatchingStrategy:       requestData.MachineMatchingStrategy,
		ComponentUniquenessStrategy:   requestData.ComponentUniquenessStrategy,
		ComponentMatchingStrategy:     requestData.ComponentMatchingStrategy,
		ExpirationStrategy:            requestData.ExpirationStrategy,
		ExpirationBasis:               requestData.ExpirationBasis,
		RenewalBasis:                  requestData.RenewalBasis,
		TransferStrategy:              requestData.TransferStrategy,
		AuthenticationStrategy:        requestData.AuthenticationStrategy,
		ProcessLeasingStrategy:        requestData.ProcessLeasingStrategy,
		MachineLeasingStrategy:        requestData.MachineLeasingStrategy,
		OverageStrategy:               requestData.OverageStrategy,
		RequireCheckIn:                false, // Default value
		CheckInInterval:               requestData.CheckInInterval,
		CheckInIntervalCount:          requestData.CheckInIntervalCount,
		HeartbeatDuration:             requestData.HeartbeatDuration,
		HeartbeatCullStrategy:         requestData.HeartbeatCullStrategy,
		HeartbeatResurrectionStrategy: requestData.HeartbeatResurrectionStrategy,
		HeartbeatBasis:                requestData.HeartbeatBasis,
		RequireHeartbeat:              false, // Default value
		Metadata:                      requestData.Metadata,
	}

	// Set boolean fields with nil checks
	if requestData.Strict != nil {
		policy.Strict = *requestData.Strict
	}
	if requestData.Floating != nil {
		policy.Floating = *requestData.Floating
	}
	if requestData.UsePool != nil {
		policy.UsePool = *requestData.UsePool
	}
	if requestData.Encrypted != nil {
		policy.Encrypted = *requestData.Encrypted
	}
	if requestData.RequireProductScope != nil {
		policy.RequireProductScope = *requestData.RequireProductScope
	}
	if requestData.RequirePolicyScope != nil {
		policy.RequirePolicyScope = *requestData.RequirePolicyScope
	}
	if requestData.RequireMachineScope != nil {
		policy.RequireMachineScope = *requestData.RequireMachineScope
	}
	if requestData.RequireFingerprintScope != nil {
		policy.RequireFingerprintScope = *requestData.RequireFingerprintScope
	}
	if requestData.RequireComponentsScope != nil {
		policy.RequireComponentsScope = *requestData.RequireComponentsScope
	}
	if requestData.RequireUserScope != nil {
		policy.RequireUserScope = *requestData.RequireUserScope
	}
	if requestData.RequireChecksumScope != nil {
		policy.RequireChecksumScope = *requestData.RequireChecksumScope
	}
	if requestData.RequireVersionScope != nil {
		policy.RequireVersionScope = *requestData.RequireVersionScope
	}
	if requestData.RequireCheckIn != nil {
		policy.RequireCheckIn = *requestData.RequireCheckIn
	}
	if requestData.RequireHeartbeat != nil {
		policy.RequireHeartbeat = *requestData.RequireHeartbeat
	}

	// Set scheme if provided
	if requestData.Scheme != nil {
		policy.Scheme = entities.LicenseScheme(*requestData.Scheme)
	}

	// Set default values (equivalent to Ruby's before_create callbacks)
	policy.SetDefaults()

	// Validate policy according to keygen-api rules
	if validationErrors := policy.ValidatePolicy(); len(validationErrors) > 0 {
		c.JSON(http.StatusUnprocessableEntity, gin.H{
			"error":  "validation failed",
			"errors": validationErrors,
		})
		return
	}

	// Save policy
	if err := h.policyRepo.Create(c.Request.Context(), policy); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to create policy", "reason": err.Error()})
		return
	}

	// TODO: Broadcast policy.created event (Ruby: BroadcastEventService.call)

	c.JSON(http.StatusCreated, gin.H{"policy": policy})
}

// UpdatePolicy updates an existing policy following keygen-api patterns
func (h *PolicyHandler) UpdatePolicy(c *gin.Context) {
	policyID := c.Param("policy_id")

	policyUUID, err := uuid.Parse(policyID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid policy ID"})
		return
	}

	policy, err := h.policyRepo.GetByID(c.Request.Context(), policyUUID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "policy not found"})
		return
	}

	var requestData struct {
		// Update fields mapping from Ruby typed_params for update action
		Name                          *string            `json:"name"`
		Scheme                        *string            `json:"scheme"`
		Duration                      *int               `json:"duration"`
		Strict                        *bool              `json:"strict"`
		Floating                      *bool              `json:"floating"`
		Protected                     *bool              `json:"protected"`
		MaxMachines                   *int               `json:"max_machines"`
		MaxProcesses                  *int               `json:"max_processes"`
		MaxUsers                      *int               `json:"max_users"`
		MaxCores                      *int               `json:"max_cores"`
		MaxUses                       *int               `json:"max_uses"`
		MachineUniquenessStrategy     *string            `json:"machine_uniqueness_strategy"`
		MachineMatchingStrategy       *string            `json:"machine_matching_strategy"`
		ComponentUniquenessStrategy   *string            `json:"component_uniqueness_strategy"`
		ComponentMatchingStrategy     *string            `json:"component_matching_strategy"`
		ExpirationStrategy            *string            `json:"expiration_strategy"`
		ExpirationBasis               *string            `json:"expiration_basis"`
		RenewalBasis                  *string            `json:"renewal_basis"`
		TransferStrategy              *string            `json:"transfer_strategy"`
		AuthenticationStrategy        *string            `json:"authentication_strategy"`
		ProcessLeasingStrategy        *string            `json:"process_leasing_strategy"`
		MachineLeasingStrategy        *string            `json:"machine_leasing_strategy"`
		OverageStrategy               *string            `json:"overage_strategy"`
		RequireProductScope           *bool              `json:"require_product_scope"`
		RequirePolicyScope            *bool              `json:"require_policy_scope"`
		RequireMachineScope           *bool              `json:"require_machine_scope"`
		RequireFingerprintScope       *bool              `json:"require_fingerprint_scope"`
		RequireComponentsScope        *bool              `json:"require_components_scope"`
		RequireUserScope              *bool              `json:"require_user_scope"`
		RequireChecksumScope          *bool              `json:"require_checksum_scope"`
		RequireVersionScope           *bool              `json:"require_version_scope"`
		RequireCheckIn                *bool              `json:"require_check_in"`
		CheckInInterval               *string            `json:"check_in_interval"`
		CheckInIntervalCount          *int               `json:"check_in_interval_count"`
		HeartbeatDuration             *int               `json:"heartbeat_duration"`
		HeartbeatCullStrategy         *string            `json:"heartbeat_cull_strategy"`
		HeartbeatResurrectionStrategy *string            `json:"heartbeat_resurrection_strategy"`
		HeartbeatBasis                *string            `json:"heartbeat_basis"`
		RequireHeartbeat              *bool              `json:"require_heartbeat"`
		Metadata                      *entities.Metadata `json:"metadata"`
	}

	if err := c.ShouldBindJSON(&requestData); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid request", "reason": err.Error()})
		return
	}

	// Update fields if provided (Ruby: policy.update(policy_params))
	if requestData.Name != nil {
		policy.Name = *requestData.Name
	}
	if requestData.Duration != nil {
		policy.Duration = requestData.Duration
	}
	if requestData.Strict != nil {
		policy.Strict = *requestData.Strict
	}
	if requestData.Floating != nil {
		policy.Floating = *requestData.Floating
	}
	if requestData.Protected != nil {
		policy.Protected = requestData.Protected
	}
	if requestData.MaxMachines != nil {
		policy.MaxMachines = requestData.MaxMachines
	}
	if requestData.MaxProcesses != nil {
		policy.MaxProcesses = requestData.MaxProcesses
	}
	if requestData.MaxUsers != nil {
		policy.MaxUsers = requestData.MaxUsers
	}
	if requestData.MaxCores != nil {
		policy.MaxCores = requestData.MaxCores
	}
	if requestData.MaxUses != nil {
		policy.MaxUses = requestData.MaxUses
	}

	// Update strategy fields
	if requestData.MachineUniquenessStrategy != nil {
		policy.MachineUniquenessStrategy = requestData.MachineUniquenessStrategy
	}
	if requestData.MachineMatchingStrategy != nil {
		policy.MachineMatchingStrategy = requestData.MachineMatchingStrategy
	}
	if requestData.ComponentUniquenessStrategy != nil {
		policy.ComponentUniquenessStrategy = requestData.ComponentUniquenessStrategy
	}
	if requestData.ComponentMatchingStrategy != nil {
		policy.ComponentMatchingStrategy = requestData.ComponentMatchingStrategy
	}
	if requestData.ExpirationStrategy != nil {
		policy.ExpirationStrategy = requestData.ExpirationStrategy
	}
	if requestData.ExpirationBasis != nil {
		policy.ExpirationBasis = requestData.ExpirationBasis
	}
	if requestData.RenewalBasis != nil {
		policy.RenewalBasis = requestData.RenewalBasis
	}
	if requestData.TransferStrategy != nil {
		policy.TransferStrategy = requestData.TransferStrategy
	}
	if requestData.AuthenticationStrategy != nil {
		policy.AuthenticationStrategy = requestData.AuthenticationStrategy
	}
	if requestData.ProcessLeasingStrategy != nil {
		policy.ProcessLeasingStrategy = requestData.ProcessLeasingStrategy
	}
	if requestData.MachineLeasingStrategy != nil {
		policy.MachineLeasingStrategy = requestData.MachineLeasingStrategy
	}
	if requestData.OverageStrategy != nil {
		policy.OverageStrategy = requestData.OverageStrategy
	}

	// Update scope requirements
	if requestData.RequireProductScope != nil {
		policy.RequireProductScope = *requestData.RequireProductScope
	}
	if requestData.RequirePolicyScope != nil {
		policy.RequirePolicyScope = *requestData.RequirePolicyScope
	}
	if requestData.RequireMachineScope != nil {
		policy.RequireMachineScope = *requestData.RequireMachineScope
	}
	if requestData.RequireFingerprintScope != nil {
		policy.RequireFingerprintScope = *requestData.RequireFingerprintScope
	}
	if requestData.RequireComponentsScope != nil {
		policy.RequireComponentsScope = *requestData.RequireComponentsScope
	}
	if requestData.RequireUserScope != nil {
		policy.RequireUserScope = *requestData.RequireUserScope
	}
	if requestData.RequireChecksumScope != nil {
		policy.RequireChecksumScope = *requestData.RequireChecksumScope
	}
	if requestData.RequireVersionScope != nil {
		policy.RequireVersionScope = *requestData.RequireVersionScope
	}

	// Update check-in and heartbeat fields
	if requestData.RequireCheckIn != nil {
		policy.RequireCheckIn = *requestData.RequireCheckIn
	}
	if requestData.CheckInInterval != nil {
		policy.CheckInInterval = requestData.CheckInInterval
	}
	if requestData.CheckInIntervalCount != nil {
		policy.CheckInIntervalCount = requestData.CheckInIntervalCount
	}
	if requestData.HeartbeatDuration != nil {
		policy.HeartbeatDuration = requestData.HeartbeatDuration
	}
	if requestData.HeartbeatCullStrategy != nil {
		policy.HeartbeatCullStrategy = requestData.HeartbeatCullStrategy
	}
	if requestData.HeartbeatResurrectionStrategy != nil {
		policy.HeartbeatResurrectionStrategy = requestData.HeartbeatResurrectionStrategy
	}
	if requestData.HeartbeatBasis != nil {
		policy.HeartbeatBasis = requestData.HeartbeatBasis
	}
	if requestData.RequireHeartbeat != nil {
		policy.RequireHeartbeat = *requestData.RequireHeartbeat
	}
	if requestData.Metadata != nil {
		policy.Metadata = *requestData.Metadata
	}

	// Update scheme if provided
	if requestData.Scheme != nil {
		policy.Scheme = entities.LicenseScheme(*requestData.Scheme)
	}

	// Validate policy according to keygen-api rules
	if validationErrors := policy.ValidatePolicy(); len(validationErrors) > 0 {
		c.JSON(http.StatusUnprocessableEntity, gin.H{
			"error":  "validation failed",
			"errors": validationErrors,
		})
		return
	}

	if err := h.policyRepo.Update(c.Request.Context(), policy); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to update policy", "reason": err.Error()})
		return
	}

	// TODO: Broadcast policy.updated event (Ruby: BroadcastEventService.call)

	c.JSON(http.StatusOK, gin.H{"policy": policy})
}

// DeletePolicy soft deletes a policy
func (h *PolicyHandler) DeletePolicy(c *gin.Context) {
	policyID := c.Param("policy_id")

	policyUUID, err := uuid.Parse(policyID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid policy ID"})
		return
	}

	_, err = h.policyRepo.GetByID(c.Request.Context(), policyUUID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "policy not found"})
		return
	}

	// TODO: Broadcast policy.deleted event (Ruby: BroadcastEventService.call)

	if err := h.policyRepo.Delete(c.Request.Context(), policyUUID); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to delete policy", "reason": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "policy deleted successfully"})
}

package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/gokeys/gokeys/internal/domain/repositories"
	"github.com/google/uuid"
)

// PolicyHandler xử lý các HTTP requests liên quan đến policy
// Theo pattern của keygen-api Ruby, policy là trung tâm quản lý license
// Policy định nghĩa các quy tắc, giới hạn và chiến lược cho license
type PolicyHandler struct {
	policyRepo  repositories.PolicyRepository  // Repository để thao tác với policy trong database
	productRepo repositories.ProductRepository // Repository để validate product tồn tại
}

// NewPolicyHandler tạo một policy handler mới
// Cần cả policyRepo và productRepo vì policy phải thuộc về một product
func NewPolicyHandler(
	policyRepo repositories.PolicyRepository,
	productRepo repositories.ProductRepository,
) *PolicyHandler {
	return &PolicyHandler{
		policyRepo:  policyRepo,
		productRepo: productRepo,
	}
}

// ListPolicies liệt kê tất cả policies với khả năng filter theo product
// Trong keygen-api, policies được scope theo organization và có thể filter theo product
// Ví dụ: GET /organizations/123/policies?product=456&page=1&page_size=20
func (h *PolicyHandler) ListPolicies(c *gin.Context) {
	// Lấy organization_id từ URL path parameter
	// Mọi policy đều phải thuộc về một organization cụ thể
	organizationID := c.Param("organization_id")

	// Lấy product filter từ query parameter (tùy chọn)
	// Tương đương với Ruby has_scope(:product) trong keygen-api
	productID := c.Query("product")

	// Parse các tham số phân trang từ query parameters
	// Mặc định page=1, page_size=20 nếu không được cung cấp
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))

	// Xây dựng filter cho database query
	// DefaultListFilter() tạo một filter cơ bản với các giá trị mặc định
	filter := repositories.DefaultListFilter()
	filter.Page = page
	filter.PageSize = pageSize

	// Luôn filter theo organization_id để đảm bảo data isolation
	// Mỗi organization chỉ thấy được policies của mình
	filter.Filters["organization_id = ?"] = organizationID

	// Nếu có product filter, thêm vào điều kiện query
	// Điều này cho phép lọc policies theo product cụ thể
	if productID != "" {
		filter.Filters["product_id = ?"] = productID
	}

	// Thực hiện query database để lấy danh sách policies
	// Repository sẽ áp dụng tất cả filters và pagination
	policies, total, err := h.policyRepo.List(c.Request.Context(), filter)
	if err != nil {
		// Trả về lỗi 500 nếu có vấn đề với database
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to get policies", "reason": err.Error()})
		return
	}

	// Tính toán metadata cho pagination
	// totalPages = ceil(total / pageSize) - công thức tính số trang
	totalPages := (int(total) + pageSize - 1) / pageSize

	// Trả về response với format chuẩn của keygen-api
	// Bao gồm data và metadata cho pagination
	c.JSON(http.StatusOK, gin.H{
		"policies": policies, // Danh sách policies
		"pagination": gin.H{
			"page":        page,       // Trang hiện tại
			"page_size":   pageSize,   // Số items per page
			"total":       total,      // Tổng số policies
			"total_pages": totalPages, // Tổng số trang
		},
	})
}

// GetPolicy lấy thông tin chi tiết của một policy cụ thể theo ID
// Endpoint: GET /organizations/{org_id}/products/{product_id}/policies/{policy_id}
// Trả về đầy đủ thông tin policy bao gồm tất cả strategies và configurations
func (h *PolicyHandler) GetPolicy(c *gin.Context) {
	// Lấy policy_id từ URL path parameter
	policyID := c.Param("policy_id")

	// Parse policy_id thành UUID
	// Trong keygen-api, tất cả IDs đều là UUID format
	policyUUID, err := uuid.Parse(policyID)
	if err != nil {
		// Trả về lỗi 400 nếu policy_id không phải UUID hợp lệ
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid policy ID"})
		return
	}

	// Tìm policy trong database theo UUID
	policy, err := h.policyRepo.GetByID(c.Request.Context(), policyUUID)
	if err != nil {
		// Trả về lỗi 404 nếu không tìm thấy policy
		// Có thể do policy không tồn tại hoặc không thuộc về organization này
		c.JSON(http.StatusNotFound, gin.H{"error": "policy not found"})
		return
	}

	// Trả về policy với tất cả thông tin chi tiết
	// Bao gồm strategies, limits, scopes, và metadata
	c.JSON(http.StatusOK, gin.H{"policy": policy})
}

// CreatePolicy tạo một policy mới theo patterns của keygen-api
// Policy là template định nghĩa các quy tắc và giới hạn cho licenses
// Endpoint: POST /organizations/{org_id}/products/{product_id}/policies
func (h *PolicyHandler) CreatePolicy(c *gin.Context) {
	// Lấy organization_id từ URL để đảm bảo policy thuộc đúng organization
	organizationID := c.Param("organization_id")

	// Định nghĩa struct cho request data với tất cả fields có thể
	// Mapping trực tiếp từ Ruby typed_params trong keygen-api
	var requestData struct {
		// === CORE ATTRIBUTES ===
		// Các thuộc tính cơ bản của policy
		ProductID string  `json:"product_id" binding:"required"` // Policy phải thuộc về một product
		Name      string  `json:"name" binding:"required"`       // Tên policy (bắt buộc)
		Scheme    *string `json:"scheme"`                        // Crypto scheme cho license encryption
		Encrypted *bool   `json:"encrypted"`                     // License có được mã hóa không
		UsePool   *bool   `json:"use_pool"`                      // Sử dụng key pool không
		Duration  *int    `json:"duration"`                      // Thời gian sống của license (seconds)
		Strict    *bool   `json:"strict"`                        // Chế độ strict validation
		Floating  *bool   `json:"floating"`                      // License floating hay node-locked
		Protected *bool   `json:"protected"`                     // Policy có được bảo vệ không

		// === LIMITS & QUOTAS ===
		// Các giới hạn số lượng cho license
		MaxMachines  *int `json:"max_machines"`  // Số máy tối đa
		MaxProcesses *int `json:"max_processes"` // Số process tối đa
		MaxUsers     *int `json:"max_users"`     // Số user tối đa
		MaxCores     *int `json:"max_cores"`     // Số CPU core tối đa
		MaxUses      *int `json:"max_uses"`      // Số lần sử dụng tối đa

		// === STRATEGY CONFIGURATIONS ===
		// Các chiến lược xử lý, mapping từ Ruby keygen-api strategy enums

		// Machine strategies - Quy tắc về máy tính
		MachineUniquenessStrategy *string `json:"machine_uniqueness_strategy"` // Máy unique theo level nào (account/product/policy/license)
		MachineMatchingStrategy   *string `json:"machine_matching_strategy"`   // Cách match máy (any/two/most/all components)

		// Component strategies - Quy tắc về hardware components
		ComponentUniquenessStrategy *string `json:"component_uniqueness_strategy"` // Component unique theo level nào
		ComponentMatchingStrategy   *string `json:"component_matching_strategy"`   // Cách match components

		// Expiration strategies - Quy tắc về hết hạn
		ExpirationStrategy *string `json:"expiration_strategy"` // Xử lý khi hết hạn (restrict/revoke/maintain/allow)
		ExpirationBasis    *string `json:"expiration_basis"`    // Tính hết hạn từ khi nào (creation/first_use/etc)
		RenewalBasis       *string `json:"renewal_basis"`       // Gia hạn từ khi nào (expiry/now/etc)

		// Transfer & Auth strategies - Quy tắc chuyển nhượng và xác thực
		TransferStrategy       *string `json:"transfer_strategy"`       // Xử lý khi transfer license (reset/keep expiry)
		AuthenticationStrategy *string `json:"authentication_strategy"` // Phương thức xác thực (token/license/session/mixed/none)

		// Leasing strategies - Quy tắc thuê tài nguyên
		ProcessLeasingStrategy *string `json:"process_leasing_strategy"` // Process lease theo gì (license/machine/user)
		MachineLeasingStrategy *string `json:"machine_leasing_strategy"` // Machine lease theo gì (license/user)

		// Overage strategy - Quy tắc vượt quá giới hạn
		OverageStrategy *string `json:"overage_strategy"` // Cho phép vượt quá bao nhiêu (1.25x/1.5x/2x/always/no)

		// === SCOPE REQUIREMENTS ===
		// Các yêu cầu về scope khi validate license, mapping từ Ruby require_*_scope fields
		RequireProductScope     *bool `json:"require_product_scope"`     // Bắt buộc có product scope
		RequirePolicyScope      *bool `json:"require_policy_scope"`      // Bắt buộc có policy scope
		RequireMachineScope     *bool `json:"require_machine_scope"`     // Bắt buộc có machine scope
		RequireFingerprintScope *bool `json:"require_fingerprint_scope"` // Bắt buộc có fingerprint scope
		RequireComponentsScope  *bool `json:"require_components_scope"`  // Bắt buộc có components scope
		RequireUserScope        *bool `json:"require_user_scope"`        // Bắt buộc có user scope
		RequireChecksumScope    *bool `json:"require_checksum_scope"`    // Bắt buộc có checksum scope
		RequireVersionScope     *bool `json:"require_version_scope"`     // Bắt buộc có version scope

		// === CHECK-IN & HEARTBEAT CONFIGURATIONS ===
		// Cấu hình check-in và heartbeat, mapping từ Ruby check_in_* và heartbeat_* fields

		// Check-in settings - License phải check-in định kỳ
		RequireCheckIn       *bool   `json:"require_check_in"`        // Có bắt buộc check-in không
		CheckInInterval      *string `json:"check_in_interval"`       // Khoảng thời gian check-in (day/week/month/year)
		CheckInIntervalCount *int    `json:"check_in_interval_count"` // Số lượng interval (vd: 2 weeks = count=2, interval=week)

		// Heartbeat settings - Machine phải gửi heartbeat định kỳ
		HeartbeatDuration             *int    `json:"heartbeat_duration"`              // Thời gian heartbeat timeout (seconds)
		HeartbeatCullStrategy         *string `json:"heartbeat_cull_strategy"`         // Xử lý machine chết (deactivate/keep)
		HeartbeatResurrectionStrategy *string `json:"heartbeat_resurrection_strategy"` // Chiến lược hồi sinh machine (always/15min/10min/etc)
		HeartbeatBasis                *string `json:"heartbeat_basis"`                 // Tính heartbeat từ khi nào (creation/first_ping)
		RequireHeartbeat              *bool   `json:"require_heartbeat"`               // Có bắt buộc heartbeat không

		// === METADATA ===
		// Dữ liệu tùy chỉnh cho policy
		Metadata entities.Metadata `json:"metadata"` // Key-value metadata
	}

	// Parse và validate JSON request body
	// ShouldBindJSON sẽ check required fields và data types
	if err := c.ShouldBindJSON(&requestData); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid request", "reason": err.Error()})
		return
	}

	// === PRODUCT VALIDATION ===
	// Validate product tồn tại và thuộc về organization này
	productUUID, err := uuid.Parse(requestData.ProductID)
	if err != nil {
		// Product ID phải là UUID hợp lệ
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid product ID"})
		return
	}

	// Tìm product trong database
	product, err := h.productRepo.GetByID(c.Request.Context(), productUUID)
	if err != nil {
		// Product không tồn tại
		c.JSON(http.StatusNotFound, gin.H{"error": "product not found"})
		return
	}

	// Đảm bảo product thuộc về organization này (security check)
	// Tránh trường hợp user tạo policy cho product của organization khác
	if product.OrganizationID != organizationID {
		c.JSON(http.StatusBadRequest, gin.H{"error": "product does not belong to this organization"})
		return
	}

	// === POLICY ENTITY CREATION ===
	// Tạo policy entity với tất cả fields, mapping từ Ruby Policy model
	policy := &entities.Policy{
		// Core identifiers
		OrganizationID: organizationID,        // Policy thuộc organization nào
		ProductID:      requestData.ProductID, // Policy thuộc product nào
		Name:           requestData.Name,      // Tên policy

		// Basic configurations
		Duration:  requestData.Duration,  // Thời gian sống license (seconds)
		Protected: requestData.Protected, // Policy có được bảo vệ không

		// Limits & quotas
		MaxMachines:  requestData.MaxMachines,  // Giới hạn số máy
		MaxProcesses: requestData.MaxProcesses, // Giới hạn số process
		MaxUsers:     requestData.MaxUsers,     // Giới hạn số user
		MaxCores:     requestData.MaxCores,     // Giới hạn số CPU core
		MaxUses:      requestData.MaxUses,      // Giới hạn số lần sử dụng

		// Strategy configurations - Các chiến lược xử lý
		MachineUniquenessStrategy:   requestData.MachineUniquenessStrategy,   // Máy unique theo level nào
		MachineMatchingStrategy:     requestData.MachineMatchingStrategy,     // Cách match máy
		ComponentUniquenessStrategy: requestData.ComponentUniquenessStrategy, // Component unique theo level nào
		ComponentMatchingStrategy:   requestData.ComponentMatchingStrategy,   // Cách match components
		ExpirationStrategy:          requestData.ExpirationStrategy,          // Xử lý khi hết hạn
		ExpirationBasis:             requestData.ExpirationBasis,             // Tính hết hạn từ khi nào
		RenewalBasis:                requestData.RenewalBasis,                // Gia hạn từ khi nào
		TransferStrategy:            requestData.TransferStrategy,            // Xử lý khi transfer
		AuthenticationStrategy:      requestData.AuthenticationStrategy,      // Phương thức xác thực
		ProcessLeasingStrategy:      requestData.ProcessLeasingStrategy,      // Process lease strategy
		MachineLeasingStrategy:      requestData.MachineLeasingStrategy,      // Machine lease strategy
		OverageStrategy:             requestData.OverageStrategy,             // Overage strategy

		// Check-in & heartbeat configurations
		RequireCheckIn:                false,                                     // Default: không bắt buộc check-in
		CheckInInterval:               requestData.CheckInInterval,               // Khoảng thời gian check-in
		CheckInIntervalCount:          requestData.CheckInIntervalCount,          // Số lượng interval
		HeartbeatDuration:             requestData.HeartbeatDuration,             // Heartbeat timeout
		HeartbeatCullStrategy:         requestData.HeartbeatCullStrategy,         // Xử lý machine chết
		HeartbeatResurrectionStrategy: requestData.HeartbeatResurrectionStrategy, // Chiến lược hồi sinh
		HeartbeatBasis:                requestData.HeartbeatBasis,                // Tính heartbeat từ khi nào
		RequireHeartbeat:              false,                                     // Default: không bắt buộc heartbeat

		// Metadata
		Metadata: requestData.Metadata, // Custom metadata
	}

	// Set boolean fields with nil checks
	if requestData.Strict != nil {
		policy.Strict = *requestData.Strict
	}
	if requestData.Floating != nil {
		policy.Floating = *requestData.Floating
	}
	if requestData.UsePool != nil {
		policy.UsePool = *requestData.UsePool
	}
	if requestData.Encrypted != nil {
		policy.Encrypted = *requestData.Encrypted
	}
	if requestData.RequireProductScope != nil {
		policy.RequireProductScope = *requestData.RequireProductScope
	}
	if requestData.RequirePolicyScope != nil {
		policy.RequirePolicyScope = *requestData.RequirePolicyScope
	}
	if requestData.RequireMachineScope != nil {
		policy.RequireMachineScope = *requestData.RequireMachineScope
	}
	if requestData.RequireFingerprintScope != nil {
		policy.RequireFingerprintScope = *requestData.RequireFingerprintScope
	}
	if requestData.RequireComponentsScope != nil {
		policy.RequireComponentsScope = *requestData.RequireComponentsScope
	}
	if requestData.RequireUserScope != nil {
		policy.RequireUserScope = *requestData.RequireUserScope
	}
	if requestData.RequireChecksumScope != nil {
		policy.RequireChecksumScope = *requestData.RequireChecksumScope
	}
	if requestData.RequireVersionScope != nil {
		policy.RequireVersionScope = *requestData.RequireVersionScope
	}
	if requestData.RequireCheckIn != nil {
		policy.RequireCheckIn = *requestData.RequireCheckIn
	}
	if requestData.RequireHeartbeat != nil {
		policy.RequireHeartbeat = *requestData.RequireHeartbeat
	}

	// === CRYPTO SCHEME SETUP ===
	// Set crypto scheme nếu được cung cấp
	// Scheme định nghĩa cách mã hóa license (RSA, ED25519, etc.)
	if requestData.Scheme != nil {
		policy.Scheme = entities.LicenseScheme(*requestData.Scheme)
	}

	// === DEFAULT VALUES ===
	// Set các giá trị mặc định (tương đương Ruby's before_create callbacks)
	// Điều này đảm bảo policy có đầy đủ strategies cần thiết
	policy.SetDefaults()

	// === BUSINESS LOGIC VALIDATION ===
	// Validate policy theo các quy tắc của keygen-api
	// Bao gồm: duration, limits, strategy compatibility, etc.
	if validationErrors := policy.ValidatePolicy(); len(validationErrors) > 0 {
		// Trả về lỗi 422 với danh sách chi tiết các lỗi validation
		c.JSON(http.StatusUnprocessableEntity, gin.H{
			"error":  "validation failed",
			"errors": validationErrors, // Array các lỗi cụ thể
		})
		return
	}

	// === DATABASE SAVE ===
	// Lưu policy vào database
	if err := h.policyRepo.Create(c.Request.Context(), policy); err != nil {
		// Trả về lỗi 500 nếu có vấn đề với database
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to create policy", "reason": err.Error()})
		return
	}

	// === EVENT BROADCASTING ===
	// TODO: Broadcast policy.created event (Ruby: BroadcastEventService.call)
	// Để notify các services khác về policy mới được tạo

	// === SUCCESS RESPONSE ===
	// Trả về policy đã tạo với status 201 Created
	c.JSON(http.StatusCreated, gin.H{"policy": policy})
}

// UpdatePolicy updates an existing policy following keygen-api patterns
func (h *PolicyHandler) UpdatePolicy(c *gin.Context) {
	policyID := c.Param("policy_id")

	policyUUID, err := uuid.Parse(policyID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid policy ID"})
		return
	}

	policy, err := h.policyRepo.GetByID(c.Request.Context(), policyUUID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "policy not found"})
		return
	}

	var requestData struct {
		// Update fields mapping from Ruby typed_params for update action
		Name                          *string            `json:"name"`
		Scheme                        *string            `json:"scheme"`
		Duration                      *int               `json:"duration"`
		Strict                        *bool              `json:"strict"`
		Floating                      *bool              `json:"floating"`
		Protected                     *bool              `json:"protected"`
		MaxMachines                   *int               `json:"max_machines"`
		MaxProcesses                  *int               `json:"max_processes"`
		MaxUsers                      *int               `json:"max_users"`
		MaxCores                      *int               `json:"max_cores"`
		MaxUses                       *int               `json:"max_uses"`
		MachineUniquenessStrategy     *string            `json:"machine_uniqueness_strategy"`
		MachineMatchingStrategy       *string            `json:"machine_matching_strategy"`
		ComponentUniquenessStrategy   *string            `json:"component_uniqueness_strategy"`
		ComponentMatchingStrategy     *string            `json:"component_matching_strategy"`
		ExpirationStrategy            *string            `json:"expiration_strategy"`
		ExpirationBasis               *string            `json:"expiration_basis"`
		RenewalBasis                  *string            `json:"renewal_basis"`
		TransferStrategy              *string            `json:"transfer_strategy"`
		AuthenticationStrategy        *string            `json:"authentication_strategy"`
		ProcessLeasingStrategy        *string            `json:"process_leasing_strategy"`
		MachineLeasingStrategy        *string            `json:"machine_leasing_strategy"`
		OverageStrategy               *string            `json:"overage_strategy"`
		RequireProductScope           *bool              `json:"require_product_scope"`
		RequirePolicyScope            *bool              `json:"require_policy_scope"`
		RequireMachineScope           *bool              `json:"require_machine_scope"`
		RequireFingerprintScope       *bool              `json:"require_fingerprint_scope"`
		RequireComponentsScope        *bool              `json:"require_components_scope"`
		RequireUserScope              *bool              `json:"require_user_scope"`
		RequireChecksumScope          *bool              `json:"require_checksum_scope"`
		RequireVersionScope           *bool              `json:"require_version_scope"`
		RequireCheckIn                *bool              `json:"require_check_in"`
		CheckInInterval               *string            `json:"check_in_interval"`
		CheckInIntervalCount          *int               `json:"check_in_interval_count"`
		HeartbeatDuration             *int               `json:"heartbeat_duration"`
		HeartbeatCullStrategy         *string            `json:"heartbeat_cull_strategy"`
		HeartbeatResurrectionStrategy *string            `json:"heartbeat_resurrection_strategy"`
		HeartbeatBasis                *string            `json:"heartbeat_basis"`
		RequireHeartbeat              *bool              `json:"require_heartbeat"`
		Metadata                      *entities.Metadata `json:"metadata"`
	}

	if err := c.ShouldBindJSON(&requestData); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid request", "reason": err.Error()})
		return
	}

	// Update fields if provided (Ruby: policy.update(policy_params))
	if requestData.Name != nil {
		policy.Name = *requestData.Name
	}
	if requestData.Duration != nil {
		policy.Duration = requestData.Duration
	}
	if requestData.Strict != nil {
		policy.Strict = *requestData.Strict
	}
	if requestData.Floating != nil {
		policy.Floating = *requestData.Floating
	}
	if requestData.Protected != nil {
		policy.Protected = requestData.Protected
	}
	if requestData.MaxMachines != nil {
		policy.MaxMachines = requestData.MaxMachines
	}
	if requestData.MaxProcesses != nil {
		policy.MaxProcesses = requestData.MaxProcesses
	}
	if requestData.MaxUsers != nil {
		policy.MaxUsers = requestData.MaxUsers
	}
	if requestData.MaxCores != nil {
		policy.MaxCores = requestData.MaxCores
	}
	if requestData.MaxUses != nil {
		policy.MaxUses = requestData.MaxUses
	}

	// Update strategy fields
	if requestData.MachineUniquenessStrategy != nil {
		policy.MachineUniquenessStrategy = requestData.MachineUniquenessStrategy
	}
	if requestData.MachineMatchingStrategy != nil {
		policy.MachineMatchingStrategy = requestData.MachineMatchingStrategy
	}
	if requestData.ComponentUniquenessStrategy != nil {
		policy.ComponentUniquenessStrategy = requestData.ComponentUniquenessStrategy
	}
	if requestData.ComponentMatchingStrategy != nil {
		policy.ComponentMatchingStrategy = requestData.ComponentMatchingStrategy
	}
	if requestData.ExpirationStrategy != nil {
		policy.ExpirationStrategy = requestData.ExpirationStrategy
	}
	if requestData.ExpirationBasis != nil {
		policy.ExpirationBasis = requestData.ExpirationBasis
	}
	if requestData.RenewalBasis != nil {
		policy.RenewalBasis = requestData.RenewalBasis
	}
	if requestData.TransferStrategy != nil {
		policy.TransferStrategy = requestData.TransferStrategy
	}
	if requestData.AuthenticationStrategy != nil {
		policy.AuthenticationStrategy = requestData.AuthenticationStrategy
	}
	if requestData.ProcessLeasingStrategy != nil {
		policy.ProcessLeasingStrategy = requestData.ProcessLeasingStrategy
	}
	if requestData.MachineLeasingStrategy != nil {
		policy.MachineLeasingStrategy = requestData.MachineLeasingStrategy
	}
	if requestData.OverageStrategy != nil {
		policy.OverageStrategy = requestData.OverageStrategy
	}

	// Update scope requirements
	if requestData.RequireProductScope != nil {
		policy.RequireProductScope = *requestData.RequireProductScope
	}
	if requestData.RequirePolicyScope != nil {
		policy.RequirePolicyScope = *requestData.RequirePolicyScope
	}
	if requestData.RequireMachineScope != nil {
		policy.RequireMachineScope = *requestData.RequireMachineScope
	}
	if requestData.RequireFingerprintScope != nil {
		policy.RequireFingerprintScope = *requestData.RequireFingerprintScope
	}
	if requestData.RequireComponentsScope != nil {
		policy.RequireComponentsScope = *requestData.RequireComponentsScope
	}
	if requestData.RequireUserScope != nil {
		policy.RequireUserScope = *requestData.RequireUserScope
	}
	if requestData.RequireChecksumScope != nil {
		policy.RequireChecksumScope = *requestData.RequireChecksumScope
	}
	if requestData.RequireVersionScope != nil {
		policy.RequireVersionScope = *requestData.RequireVersionScope
	}

	// Update check-in and heartbeat fields
	if requestData.RequireCheckIn != nil {
		policy.RequireCheckIn = *requestData.RequireCheckIn
	}
	if requestData.CheckInInterval != nil {
		policy.CheckInInterval = requestData.CheckInInterval
	}
	if requestData.CheckInIntervalCount != nil {
		policy.CheckInIntervalCount = requestData.CheckInIntervalCount
	}
	if requestData.HeartbeatDuration != nil {
		policy.HeartbeatDuration = requestData.HeartbeatDuration
	}
	if requestData.HeartbeatCullStrategy != nil {
		policy.HeartbeatCullStrategy = requestData.HeartbeatCullStrategy
	}
	if requestData.HeartbeatResurrectionStrategy != nil {
		policy.HeartbeatResurrectionStrategy = requestData.HeartbeatResurrectionStrategy
	}
	if requestData.HeartbeatBasis != nil {
		policy.HeartbeatBasis = requestData.HeartbeatBasis
	}
	if requestData.RequireHeartbeat != nil {
		policy.RequireHeartbeat = *requestData.RequireHeartbeat
	}
	if requestData.Metadata != nil {
		policy.Metadata = *requestData.Metadata
	}

	// Update scheme if provided
	if requestData.Scheme != nil {
		policy.Scheme = entities.LicenseScheme(*requestData.Scheme)
	}

	// Validate policy according to keygen-api rules
	if validationErrors := policy.ValidatePolicy(); len(validationErrors) > 0 {
		c.JSON(http.StatusUnprocessableEntity, gin.H{
			"error":  "validation failed",
			"errors": validationErrors,
		})
		return
	}

	if err := h.policyRepo.Update(c.Request.Context(), policy); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to update policy", "reason": err.Error()})
		return
	}

	// TODO: Broadcast policy.updated event (Ruby: BroadcastEventService.call)

	c.JSON(http.StatusOK, gin.H{"policy": policy})
}

// DeletePolicy soft deletes a policy
func (h *PolicyHandler) DeletePolicy(c *gin.Context) {
	policyID := c.Param("policy_id")

	policyUUID, err := uuid.Parse(policyID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid policy ID"})
		return
	}

	_, err = h.policyRepo.GetByID(c.Request.Context(), policyUUID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "policy not found"})
		return
	}

	// TODO: Broadcast policy.deleted event (Ruby: BroadcastEventService.call)

	if err := h.policyRepo.Delete(c.Request.Context(), policyUUID); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to delete policy", "reason": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "policy deleted successfully"})
}

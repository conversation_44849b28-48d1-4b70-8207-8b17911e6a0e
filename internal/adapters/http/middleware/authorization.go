package middleware

import (
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/gokeys/gokeys/internal/domain/services/auth"
)

// AuthorizationMiddleware handles authorization checks
type AuthorizationMiddleware struct {
	authService *auth.AuthService
}

// NewAuthorizationMiddleware creates a new authorization middleware
func NewAuthorizationMiddleware(authService *auth.AuthService) *AuthorizationMiddleware {
	return &AuthorizationMiddleware{
		authService: authService,
	}
}

// RequirePermission middleware that checks if user has required permission
func (m *AuthorizationMiddleware) RequirePermission(resourceType, action string) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get user ID from context (set by authentication middleware)
		userID, exists := c.Get("user_id")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "unauthorized", "reason": "no user context"})
			c.Abort()
			return
		}

		userIDStr, ok := userID.(string)
		if !ok {
			c.<PERSON>(http.StatusUnauthorized, gin.H{"error": "unauthorized", "reason": "invalid user context"})
			c.Abort()
			return
		}

		// Get organization ID from context or path
		organizationID := m.getOrganizationID(c)

		// Get resource ID from path if present
		var resourceID *string
		if id := c.Param("id"); id != "" {
			resourceID = &id
		}

		// Check permission
		allowed, err := m.authService.CheckPermission(c.Request.Context(), userIDStr, resourceType, action, resourceID, organizationID)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "authorization check failed", "reason": err.Error()})
			c.Abort()
			return
		}

		if !allowed {
			c.JSON(http.StatusForbidden, gin.H{"error": "access denied", "reason": "insufficient permissions"})
			c.Abort()
			return
		}

		c.Next()
	}
}

// RequireSystemAdmin middleware that requires system admin permission
func (m *AuthorizationMiddleware) RequireSystemAdmin() gin.HandlerFunc {
	return func(c *gin.Context) {
		userID, exists := c.Get("user_id")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "unauthorized", "reason": "no user context"})
			c.Abort()
			return
		}

		userIDStr, ok := userID.(string)
		if !ok {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "unauthorized", "reason": "invalid user context"})
			c.Abort()
			return
		}

		// Check system admin permission
		isAdmin, err := m.authService.HasSystemAdminPermission(c.Request.Context(), userIDStr)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "authorization check failed", "reason": err.Error()})
			c.Abort()
			return
		}

		if !isAdmin {
			c.JSON(http.StatusForbidden, gin.H{"error": "access denied", "reason": "system admin required"})
			c.Abort()
			return
		}

		c.Next()
	}
}

// RequireOrganizationAdmin middleware that requires admin permission in an organization
func (m *AuthorizationMiddleware) RequireOrganizationAdmin() gin.HandlerFunc {
	return func(c *gin.Context) {
		userID, exists := c.Get("user_id")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "unauthorized", "reason": "no user context"})
			c.Abort()
			return
		}

		userIDStr, ok := userID.(string)
		if !ok {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "unauthorized", "reason": "invalid user context"})
			c.Abort()
			return
		}

		// Get organization ID
		organizationID := m.getOrganizationID(c)
		if organizationID == nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "bad request", "reason": "organization context required"})
			c.Abort()
			return
		}

		// Check organization admin permission
		isAdmin, err := m.authService.HasOrganizationAdminPermission(c.Request.Context(), userIDStr, *organizationID)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "authorization check failed", "reason": err.Error()})
			c.Abort()
			return
		}

		if !isAdmin {
			c.JSON(http.StatusForbidden, gin.H{"error": "access denied", "reason": "organization admin required"})
			c.Abort()
			return
		}

		c.Next()
	}
}

// RequireOrganizationMember middleware that requires user to be member of organization
func (m *AuthorizationMiddleware) RequireOrganizationMember() gin.HandlerFunc {
	return func(c *gin.Context) {
		userID, exists := c.Get("user_id")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "unauthorized", "reason": "no user context"})
			c.Abort()
			return
		}

		userIDStr, ok := userID.(string)
		if !ok {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "unauthorized", "reason": "invalid user context"})
			c.Abort()
			return
		}

		// Get organization ID
		organizationID := m.getOrganizationID(c)
		if organizationID == nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "bad request", "reason": "organization context required"})
			c.Abort()
			return
		}

		// Check organization membership
		isMember, err := m.authService.IsUserInOrganization(c.Request.Context(), userIDStr, *organizationID)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "membership check failed", "reason": err.Error()})
			c.Abort()
			return
		}

		if !isMember {
			c.JSON(http.StatusForbidden, gin.H{"error": "access denied", "reason": "organization membership required"})
			c.Abort()
			return
		}

		// Set organization ID in context for handlers
		c.Set("organization_id", *organizationID)

		c.Next()
	}
}

// getOrganizationID extracts organization ID from various sources
func (m *AuthorizationMiddleware) getOrganizationID(c *gin.Context) *string {
	// Try to get from URL path parameter
	if orgID := c.Param("organization_id"); orgID != "" {
		return &orgID
	}

	// Try to get from URL path parameter (alternative names)
	if orgID := c.Param("org_id"); orgID != "" {
		return &orgID
	}

	// Try to get from query parameter
	if orgID := c.Query("organization_id"); orgID != "" {
		return &orgID
	}

	// Try to get from user context (user's default organization)
	if orgID, exists := c.Get("user_organization_id"); exists {
		if orgIDStr, ok := orgID.(string); ok && orgIDStr != "" {
			return &orgIDStr
		}
	}

	// Try to extract from URL path patterns like /organizations/{id}/...
	path := c.Request.URL.Path
	if strings.Contains(path, "/organizations/") {
		parts := strings.Split(path, "/")
		for i, part := range parts {
			if part == "organizations" && i+1 < len(parts) && parts[i+1] != "" {
				return &parts[i+1]
			}
		}
	}

	return nil
}

// SetOrganizationContext middleware that sets organization context for requests
func (m *AuthorizationMiddleware) SetOrganizationContext() gin.HandlerFunc {
	return func(c *gin.Context) {
		if orgID := m.getOrganizationID(c); orgID != nil {
			c.Set("organization_id", *orgID)
		}
		c.Next()
	}
}
// Package middleware - Authorization layer
// Chịu trách nhiệm kiểm tra quyền truy cập (authorization) sau khi authentication thành công
// Refactored to use new constraint-based authorization with OPA
package middleware

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/gokeys/gokeys/internal/domain/services/auth"
)

// Resource types - các loại tài nguyên thực tế trong database schema
// Được sử dụng trong OPA input để xác định loại resource đang được truy cập
const (
	// ResourceTypeOrganization - Tổ chức/công ty, là đơn vị cấp cao nhất cho multi-tenancy
	ResourceTypeOrganization = "organization"
	// ResourceTypeProduct - Sản phẩm phần mềm của organization
	ResourceTypeProduct = "product"
	// ResourceTypePolicy - Chính sách license (template cho licenses)
	ResourceTypePolicy = "policy"
	// ResourceTypeLicense - License cụ thể được cấp cho user/customer
	ResourceTypeLicense = "license"
	// ResourceTypeMachine - Máy client chạy phần mềm (licensed machines)
	ResourceTypeMachine = "machine"
	// ResourceTypeUser - Người dùng trong hệ thống
	ResourceTypeUser = "user"
	// ResourceTypeSession - Phiên đăng nhập của user
	ResourceTypeSession = "session"
	// ResourceTypeAPIToken - Token API để truy cập hệ thống
	ResourceTypeAPIToken = "api_token"
	// ResourceTypeUserOrganization - Quan hệ user-organization với permissions
	ResourceTypeUserOrganization = "user_organization"
)

// Common actions - các hành động cơ bản cho resource-based authorization
const (
	ActionCreate    = "create"
	ActionRead      = "read"
	ActionUpdate    = "update"
	ActionDelete    = "delete"
	ActionValidate  = "validate"
	ActionCheckout  = "checkout"
	ActionHeartbeat = "heartbeat"
	ActionCheckIn   = "check-in"
)

// AuthorizationMiddleware provides constraint-based authorization using OPA
// This middleware works with the new auth service and constraint-based permissions
type AuthorizationMiddleware struct {
	authService *auth.AuthService
}

// NewAuthorizationMiddleware creates a new authorization middleware
func NewAuthorizationMiddleware(authService *auth.AuthService) *AuthorizationMiddleware {
	return &AuthorizationMiddleware{
		authService: authService,
	}
}

// RequireResourceAccess creates middleware that requires access to a specific resource type and action
// This is the main authorization method using constraint-based permissions
func (m *AuthorizationMiddleware) RequireResourceAccess(resourceType, action string) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get authenticated subject from context
		subject := GetSubject(c)
		if subject == nil {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "authentication required for authorization",
			})
			c.Abort()
			return
		}

		// Get resource ID from URL params if available
		resourceID := c.Param("id")
		if resourceID == "" {
			resourceID = c.Param("resource_id")
		}

		// Build resource for authorization
		resource := &auth.Resource{
			Type: resourceType,
			ID:   resourceID,
		}

		// Add organization context for all resource types
		// This is critical for organization-scoped permissions to work correctly
		if orgID, err := GetOrganizationID(c); err == nil && orgID != "" {
			resource.OrganizationID = &orgID
		}

		// Perform authorization check
		authReq := &auth.AuthorizationRequest{
			Subject:  subject,
			Resource: resource,
			Action:   action,
			Context: map[string]interface{}{
				"method": c.Request.Method,
				"path":   c.Request.URL.Path,
				"ip":     c.ClientIP(),
			},
		}

		result, err := m.authService.Authorize(c.Request.Context(), authReq)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error":   "authorization check failed",
				"details": err.Error(),
			})
			c.Abort()
			return
		}

		if !result.Allowed {
			c.JSON(http.StatusForbidden, gin.H{
				"error":  "access denied",
				"reason": result.Reason,
			})
			c.Abort()
			return
		}

		// Add authorization result to context for audit logging
		c.Set("auth_result", result)
		c.Next()
	}
}

// RequirePermission creates middleware that requires a specific permission
// This is a simpler check that works with direct permissions
func (m *AuthorizationMiddleware) RequirePermission(permission string) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get authenticated subject from context
		subject := GetSubject(c)
		if subject == nil {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "authentication required for permission check",
			})
			c.Abort()
			return
		}

		// Check if subject has the required permission
		hasPermission, err := m.authService.HasPermission(c.Request.Context(), subject, permission)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error":   "permission check failed",
				"details": err.Error(),
			})
			c.Abort()
			return
		}

		if !hasPermission {
			c.JSON(http.StatusForbidden, gin.H{
				"error":    "insufficient permissions",
				"required": permission,
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// RequireAnyPermission creates middleware that requires any of the specified permissions
func (m *AuthorizationMiddleware) RequireAnyPermission(permissions ...string) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get authenticated subject from context
		subject := GetSubject(c)
		if subject == nil {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "authentication required for permission check",
			})
			c.Abort()
			return
		}

		// Check if subject has any of the required permissions
		for _, permission := range permissions {
			hasPermission, err := m.authService.HasPermission(c.Request.Context(), subject, permission)
			if err != nil {
				continue // Try next permission on error
			}
			if hasPermission {
				c.Next()
				return
			}
		}

		c.JSON(http.StatusForbidden, gin.H{
			"error":        "insufficient permissions",
			"required_any": permissions,
		})
		c.Abort()
	}
}

// RequireOwnership creates middleware that requires the subject to own the resource
// This is useful for B2C scenarios where customers can only access their own resources
func (m *AuthorizationMiddleware) RequireOwnership(resourceType string) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get authenticated subject from context
		subject := GetSubject(c)
		if subject == nil {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "authentication required for ownership check",
			})
			c.Abort()
			return
		}

		// Get resource ID from URL params
		resourceID := c.Param("id")
		if resourceID == "" {
			c.JSON(http.StatusBadRequest, gin.H{
				"error": "resource ID required for ownership check",
			})
			c.Abort()
			return
		}

		// Build resource with owner context
		resource := &auth.Resource{
			Type:      resourceType,
			ID:        resourceID,
			OwnerID:   &subject.ID,
			OwnerType: subject.Type,
		}

		// Use "owner" action to check ownership
		authReq := &auth.AuthorizationRequest{
			Subject:  subject,
			Resource: resource,
			Action:   "owner",
		}

		result, err := m.authService.Authorize(c.Request.Context(), authReq)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error":   "ownership check failed",
				"details": err.Error(),
			})
			c.Abort()
			return
		}

		if !result.Allowed {
			c.JSON(http.StatusForbidden, gin.H{
				"error": "resource access denied - ownership required",
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

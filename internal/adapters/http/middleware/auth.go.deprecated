// Package middleware chứa các middleware cho HTTP server
// Authentication middleware xử lý việc xác thực người dùng và token
// Refactored to use new constraint-based auth service
package middleware

import (
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/gokeys/gokeys/internal/domain/services/auth"
)

// AuthMiddleware wraps the new auth service middleware for HTTP layer compatibility
type AuthMiddleware struct {
	authMiddleware *auth.AuthMiddleware
}

// NewAuthMiddleware creates a new HTTP auth middleware using the new auth service
func NewAuthMiddleware(authService *auth.AuthService) *AuthMiddleware {
	return &AuthMiddleware{
		authMiddleware: auth.NewAuthMiddleware(authService),
	}
}

// RequireAuth middleware that requires authentication
func (m *AuthMiddleware) RequireAuth() gin.HandlerFunc {
	return m.authMiddleware.RequireAuth()
}

// RequirePermission middleware that requires specific permission
func (m *AuthMiddleware) RequirePermission(permission string) gin.HandlerFunc {
	return m.authMiddleware.RequirePermission(permission)
}

// RequireResourceAccess middleware that requires access to specific resource
func (m *AuthMiddleware) RequireResourceAccess(resourceType, action string) gin.HandlerFunc {
	return m.authMiddleware.RequireResourceAccess(resourceType, action)
}

// Optional middleware for routes that work both with and without auth
func (m *AuthMiddleware) Optional() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Try to authenticate but don't fail if no auth provided
		subject, _ := m.authMiddleware.ExtractSubject(c)
		if subject != nil {
			// Store subject in context if found
			c.Set(string(auth.SubjectContextKey), subject)
		}
		c.Next()
	}
}

// Helper function to get subject from gin context
func GetSubject(c *gin.Context) *auth.Subject {
	if subject, exists := c.Get(string(auth.SubjectContextKey)); exists {
		if s, ok := subject.(*auth.Subject); ok {
			return s
		}
	}
	return nil
}

// GetOrganizationID extracts organization ID from authenticated subject
func GetOrganizationID(c *gin.Context) (string, error) {
	subject := GetSubject(c)
	if subject == nil {
		return "", fmt.Errorf("authentication required")
	}
	
	if subject.OrganizationID == nil {
		return "", fmt.Errorf("organization context required")
	}
	
	return *subject.OrganizationID, nil
}

// Deprecated: Legacy middleware functions - use new constraint-based methods instead
// These are kept for compatibility during migration

// LegacyRequireAuth provides backward compatibility
func (m *AuthMiddleware) LegacyRequireAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Legacy auth middleware is deprecated. Please use RequireAuth() instead.",
		})
		c.Abort()
	}
}
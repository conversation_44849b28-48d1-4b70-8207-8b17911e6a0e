package middleware

import (
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/gokeys/gokeys/internal/domain/repositories"
	"github.com/gokeys/gokeys/internal/domain/services/auth"
)

// AuthenticationMiddleware handles authentication
type AuthenticationMiddleware struct {
	userRepo     repositories.UserRepository
	sessionRepo  repositories.SessionRepository
	apiTokenRepo repositories.APITokenRepository
	authService  *auth.AuthService
}

// NewAuthenticationMiddleware creates a new authentication middleware
func NewAuthenticationMiddleware(
	userRepo repositories.UserRepository,
	sessionRepo repositories.SessionRepository,
	apiTokenRepo repositories.APITokenRepository,
	authService *auth.AuthService,
) *AuthenticationMiddleware {
	return &AuthenticationMiddleware{
		userRepo:     userRepo,
		sessionRepo:  sessionRepo,
		apiTokenRepo: apiTokenRepo,
		authService:  authService,
	}
}

// RequireAuth middleware that requires authentication
func (m *AuthenticationMiddleware) RequireAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Try different authentication methods
		authenticated := false

		// 1. Try Bearer token authentication (API tokens or sessions)
		if authHeader := c.GetHeader("Authorization"); authHeader != "" {
			if strings.HasPrefix(authHeader, "Bearer ") {
				token := strings.TrimPrefix(authHeader, "Bearer ")
				if m.authenticateWithToken(c, token) {
					authenticated = true
				}
			}
		}

		// 2. Try API key authentication (query parameter or header)
		if !authenticated {
			var apiKey string
			if key := c.GetHeader("X-API-Key"); key != "" {
				apiKey = key
			} else if key := c.Query("api_key"); key != "" {
				apiKey = key
			}

			if apiKey != "" && m.authenticateWithAPIKey(c, apiKey) {
				authenticated = true
			}
		}

		// 3. Try license key authentication (for license validation endpoints)
		if !authenticated {
			if licenseKey := c.GetHeader("X-License-Key"); licenseKey != "" {
				if m.authenticateWithLicenseKey(c, licenseKey) {
					authenticated = true
				}
			}
		}

		if !authenticated {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error":  "unauthorized",
				"reason": "authentication required",
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// authenticateWithToken authenticates using a bearer token (JWT, session or API token)
func (m *AuthenticationMiddleware) authenticateWithToken(c *gin.Context, token string) bool {
	// 1. Try JWT token first
	if claims, err := m.authService.VerifyJWT(token); err == nil {
		// Extract user info from JWT claims
		if userID, ok := claims["user_id"].(string); ok {
			sessionID, _ := claims["session_id"].(string)
			
			// Verify session exists and is valid
			if sessionID != "" {
				session, err := m.sessionRepo.GetByID(c.Request.Context(), parseUUID(sessionID))
				if err == nil && session != nil && session.ExpiresAt.After(time.Now()) {
					// Set user context from JWT
					c.Set("user_id", userID)
					c.Set("auth_type", "jwt")
					c.Set("session_id", sessionID)
					return true
				}
			}
		}
	}

	// 2. Try legacy session token lookup (for backward compatibility)
	session, err := m.sessionRepo.GetByTokenHash(c.Request.Context(), token)
	if err == nil && session != nil {
		// Set user context
		c.Set("user_id", session.UserID)
		c.Set("auth_type", "session")
		c.Set("session_id", session.ID)
		return true
	}

	// 3. Try API token
	apiToken, err := m.apiTokenRepo.GetByTokenHash(c.Request.Context(), token)
	if err == nil && apiToken != nil {
		// Set user context
		c.Set("user_id", apiToken.UserID)
		c.Set("auth_type", "api_token")
		c.Set("api_token_id", apiToken.ID)
		if apiToken.OrganizationID != nil {
			c.Set("user_organization_id", *apiToken.OrganizationID)
		}
		return true
	}

	return false
}

// parseUUID parses a string to UUID, returns zero UUID if invalid
func parseUUID(s string) uuid.UUID {
	id, err := uuid.Parse(s)
	if err != nil {
		return uuid.Nil
	}
	return id
}

// authenticateWithAPIKey authenticates using an API key
func (m *AuthenticationMiddleware) authenticateWithAPIKey(c *gin.Context, apiKey string) bool {
	// This would hash the API key and look it up
	// For now, using the same token lookup as placeholder
	return m.authenticateWithToken(c, apiKey)
}

// authenticateWithLicenseKey authenticates using a license key (for validation endpoints)
func (m *AuthenticationMiddleware) authenticateWithLicenseKey(c *gin.Context, licenseKey string) bool {
	// For license key authentication, we would validate the key
	// and set a different type of context
	// This is typically used for license validation endpoints
	
	// Placeholder implementation - would need license repository
	c.Set("auth_type", "license_key")
	c.Set("license_key", licenseKey)
	return true // Simplified for now
}

// OptionalAuth middleware that allows both authenticated and unauthenticated requests
func (m *AuthenticationMiddleware) OptionalAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Try authentication but don't fail if it doesn't work
		if authHeader := c.GetHeader("Authorization"); authHeader != "" {
			if strings.HasPrefix(authHeader, "Bearer ") {
				token := strings.TrimPrefix(authHeader, "Bearer ")
				m.authenticateWithToken(c, token)
			}
		}
		c.Next()
	}
}

// RequireNoAuth middleware that ensures the request is not authenticated (for login, register, etc.)
func (m *AuthenticationMiddleware) RequireNoAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		if _, exists := c.Get("user_id"); exists {
			c.JSON(http.StatusBadRequest, gin.H{
				"error":  "bad request",
				"reason": "already authenticated",
			})
			c.Abort()
			return
		}
		c.Next()
	}
}
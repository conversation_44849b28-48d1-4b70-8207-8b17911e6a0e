package responses

import (
	"fmt"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// JSONAPIResponse represents a JSON-API compliant response
type JSONAPIResponse struct {
	Data     interface{}            `json:"data,omitempty"`
	Included []interface{}          `json:"included,omitempty"`
	Meta     map[string]interface{} `json:"meta,omitempty"`
	Links    map[string]interface{} `json:"links,omitempty"`
	Errors   []ErrorDetail          `json:"errors,omitempty"`
}

// ResourceObject represents a JSON-API resource object
type ResourceObject struct {
	ID            string                 `json:"id"`
	Type          string                 `json:"type"`
	Attributes    map[string]interface{} `json:"attributes"`
	Relationships map[string]interface{} `json:"relationships,omitempty"`
	Links         map[string]interface{} `json:"links,omitempty"`
	Meta          map[string]interface{} `json:"meta,omitempty"`
}

// PaginationMeta represents pagination metadata
type PaginationMeta struct {
	Page       int   `json:"page"`
	PerPage    int   `json:"per_page"`
	Total      int64 `json:"total"`
	TotalPages int64 `json:"total_pages"`
}

// ValidationMeta represents license validation metadata
type ValidationMeta struct {
	Timestamp time.Time   `json:"ts"`
	Valid     bool        `json:"valid"`
	Detail    string      `json:"detail"`
	Code      string      `json:"code"`
	Nonce     *int        `json:"nonce,omitempty"`
	Scope     interface{} `json:"scope,omitempty"`
}


// RenderSingle renders a single resource response
func RenderSingle(c *gin.Context, status int, resource *ResourceObject, meta map[string]interface{}) {
	response := &JSONAPIResponse{
		Data: resource,
		Meta: meta,
	}
	c.JSON(status, response)
}

// RenderCollection renders a collection response with pagination
func RenderCollection(c *gin.Context, status int, resources []interface{}, pagination *PaginationMeta, meta map[string]interface{}) {
	if meta == nil {
		meta = make(map[string]interface{})
	}
	if pagination != nil {
		meta["pagination"] = pagination
	}

	response := &JSONAPIResponse{
		Data: resources,
		Meta: meta,
	}
	c.JSON(status, response)
}

// RenderValidation renders a license validation response
func RenderValidation(c *gin.Context, status int, resource *ResourceObject, validationMeta *ValidationMeta) {
	meta := map[string]interface{}{
		"ts":     validationMeta.Timestamp,
		"valid":  validationMeta.Valid,
		"detail": validationMeta.Detail,
		"code":   validationMeta.Code,
	}

	if validationMeta.Nonce != nil {
		meta["nonce"] = *validationMeta.Nonce
	}
	if validationMeta.Scope != nil {
		meta["scope"] = validationMeta.Scope
	}

	response := &JSONAPIResponse{
		Data: resource,
		Meta: meta,
	}
	c.JSON(status, response)
}


// ToResourceObject converts an entity to a JSON-API resource object
func ToResourceObject(entityType string, id interface{}, attributes map[string]interface{}) *ResourceObject {
	var idStr string
	switch v := id.(type) {
	case string:
		idStr = v
	case uuid.UUID:
		idStr = v.String()
	case *uuid.UUID:
		if v != nil {
			idStr = v.String()
		}
	default:
		idStr = fmt.Sprintf("%v", v)
	}

	return &ResourceObject{
		ID:         idStr,
		Type:       entityType,
		Attributes: attributes,
	}
}

// Resource type constants for JSON-API
const (
	ResourceTypeOrganization    = "organization"
	ResourceTypeUser            = "user"
	ResourceTypeProduct         = "product"
	ResourceTypePolicy          = "policy"
	ResourceTypeLicense         = "license"
	ResourceTypeMachine         = "machine"
	ResourceTypeEntitlement     = "entitlement"
	ResourceTypeGroup           = "group"
	ResourceTypeWebhookEndpoint = "webhook-endpoint"
	ResourceTypeToken           = "token"
	ResourceTypeEnvironment     = "environment"
)

// Helper functions for converting entities to resource objects

// LicenseToResource converts a license entity to a resource object
func LicenseToResource(license interface{}) *ResourceObject {
	// This would be implemented based on your License entity structure
	// For now, returning a placeholder
	return &ResourceObject{
		ID:   "placeholder-id",
		Type: ResourceTypeLicense,
		Attributes: map[string]interface{}{
			"key":        "placeholder-key",
			"status":     "active",
			"created_at": time.Now(),
		},
	}
}

// MachineToResource converts a machine entity to a resource object
func MachineToResource(machine interface{}) *ResourceObject {
	return &ResourceObject{
		ID:   "placeholder-id",
		Type: ResourceTypeMachine,
		Attributes: map[string]interface{}{
			"fingerprint": "placeholder-fingerprint",
			"name":        "placeholder-name",
			"created_at":  time.Now(),
		},
	}
}

// ProductToResource converts a product entity to a resource object
func ProductToResource(product interface{}) *ResourceObject {
	return &ResourceObject{
		ID:   "placeholder-id",
		Type: ResourceTypeProduct,
		Attributes: map[string]interface{}{
			"name":       "placeholder-name",
			"created_at": time.Now(),
		},
	}
}

// PolicyToResource converts a policy entity to a resource object
func PolicyToResource(policy interface{}) *ResourceObject {
	return &ResourceObject{
		ID:   "placeholder-id",
		Type: ResourceTypePolicy,
		Attributes: map[string]interface{}{
			"name":       "placeholder-name",
			"strict":     true,
			"created_at": time.Now(),
		},
	}
}

// EntitlementToResource converts an entitlement entity to a resource object
func EntitlementToResource(entitlement interface{}) *ResourceObject {
	return &ResourceObject{
		ID:   "placeholder-id",
		Type: ResourceTypeEntitlement,
		Attributes: map[string]interface{}{
			"name":       "placeholder-name",
			"code":       "placeholder-code",
			"created_at": time.Now(),
		},
	}
}

// GroupToResource converts a group entity to a resource object
func GroupToResource(group interface{}) *ResourceObject {
	return &ResourceObject{
		ID:   "placeholder-id",
		Type: ResourceTypeGroup,
		Attributes: map[string]interface{}{
			"name":         "placeholder-name",
			"max_users":    nil,
			"max_licenses": nil,
			"max_machines": nil,
			"created_at":   time.Now(),
		},
	}
}

// WebhookEndpointToResource converts a webhook endpoint entity to a resource object
func WebhookEndpointToResource(endpoint interface{}) *ResourceObject {
	return &ResourceObject{
		ID:   "placeholder-id",
		Type: ResourceTypeWebhookEndpoint,
		Attributes: map[string]interface{}{
			"url":                 "https://example.com/webhook",
			"subscriptions":       []string{},
			"signature_algorithm": "hmac-sha256",
			"created_at":          time.Now(),
		},
	}
}

package routes

import (
	"context"

	"github.com/gin-gonic/gin"
	"github.com/gokeys/gokeys/internal/adapters/http/handlers"
	"github.com/gokeys/gokeys/internal/adapters/http/middleware"
	"github.com/gokeys/gokeys/internal/config"
	postgresRepos "github.com/gokeys/gokeys/internal/adapters/database/postgres/repositories"
	"github.com/gokeys/gokeys/internal/domain/services"
	"github.com/gokeys/gokeys/internal/domain/services/auth"
)

// APIRoutes configures all API routes with new constraint-based authorization
type APIRoutes struct {
	config             *config.Config
	serviceCoordinator *services.ServiceCoordinator
	authService        *auth.AuthService
	authMiddleware     *middleware.AuthMiddleware
	authzMiddleware    *middleware.AuthorizationMiddleware

	// Handlers - chỉ các handlers thực tế có trong database schema
	authHandler             *handlers.AuthHandler
	licenseHandler          *handlers.LicenseHandler
	machineHandler          *handlers.MachineHandler
	productHandler          *handlers.ProductHandler
	policyHandler           *handlers.PolicyHandler
	organizationHandler     *handlers.OrganizationHandler
	userOrganizationHandler *handlers.UserOrganizationHandler
}

// NewAPIRoutes creates a new API routes configuration with the new auth system
func NewAPIRoutes(cfg *config.Config, serviceCoordinator *services.ServiceCoordinator, repoFactory *postgresRepos.RepositoryFactory) (*APIRoutes, error) {
	// Create auth service factory
	authFactory := auth.NewFactory(
		repoFactory.User(),
		repoFactory.UserOrganization(),
		repoFactory.APIToken(),
		repoFactory.Organization(),
		repoFactory.Product(),
		repoFactory.License(),
		repoFactory.Machine(),
		repoFactory.Policy(),
		repoFactory.Session(),
		serviceCoordinator.Crypto,
		cfg,
	)

	// Create auth service with OPA integration
	authService, err := authFactory.CreateAuthService(context.Background())
	if err != nil {
		return nil, err
	}

	// Create middleware with new auth service
	authMiddleware := middleware.NewAuthMiddleware(authService)
	authzMiddleware := middleware.NewAuthorizationMiddleware(authService)

	// Initialize handlers with correct constructors
	authHandler := handlers.NewAuthHandler(
		authService,
		repoFactory.User(),
		repoFactory.UserOrganization(),
		repoFactory.Organization(),
		repoFactory.Session(),
		serviceCoordinator.Crypto,
	)
	licenseHandler := handlers.NewLicenseHandler(
		authService,
		serviceCoordinator.LicenseValidation,
		// TODO: Add checkout and lookup services when available
		nil, // serviceCoordinator.LicenseCheckout,
		nil, // serviceCoordinator.LicenseLookup,
		repoFactory.License(),
		repoFactory.Policy(),
		repoFactory.Product(),
		repoFactory.User(),
		repoFactory.Organization(),
		serviceCoordinator.Events,
	)
	machineHandler := handlers.NewMachineHandler(serviceCoordinator)
	productHandler := handlers.NewProductHandler(serviceCoordinator)
	policyHandler := handlers.NewPolicyHandler(serviceCoordinator)
	organizationHandler := handlers.NewOrganizationHandler(serviceCoordinator)
	userOrganizationHandler := handlers.NewUserOrganizationHandler(serviceCoordinator)

	return &APIRoutes{
		config:                 cfg,
		serviceCoordinator:     serviceCoordinator,
		authService:            authService,
		authMiddleware:         authMiddleware,
		authzMiddleware:        authzMiddleware,
		authHandler:             authHandler,
		licenseHandler:          licenseHandler,
		machineHandler:          machineHandler,
		productHandler:          productHandler,
		policyHandler:           policyHandler,
		organizationHandler:     organizationHandler,
		userOrganizationHandler: userOrganizationHandler,
	}, nil
}

// SetupRoutes configures all API routes with proper authorization
func (r *APIRoutes) SetupRoutes(router *gin.Engine) {
	// API v1 routes
	v1 := router.Group("/v1")

	// Public routes (no authentication required)
	r.setupPublicRoutes(v1)

	// Protected routes (authentication required)
	protected := v1.Group("")
	protected.Use(r.authMiddleware.RequireAuth())
	r.setupProtectedRoutes(protected)

	// Admin routes (admin permissions required)
	admin := v1.Group("/admin")
	admin.Use(r.authMiddleware.RequireAuth())
	admin.Use(r.authzMiddleware.RequireResourceAccess("*", "admin"))
	r.setupAdminRoutes(admin)
}

// setupPublicRoutes configures routes that don't require authentication
func (r *APIRoutes) setupPublicRoutes(rg *gin.RouterGroup) {
	// Health checks
	rg.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{"status": "ok"})
	})

	// Authentication endpoints
	auth := rg.Group("/auth")
	{
		auth.POST("/register", r.authHandler.RegisterHandler)
		auth.POST("/login", r.authHandler.LoginHandler)
	}

	// License validation (public for end-user software)
	licenses := rg.Group("/licenses")
	{
		// These endpoints are used by end-user software for license validation
		// Authentication is done via license key in the request
		licenses.POST("/validate", r.licenseHandler.ValidateByKey)
		licenses.POST("/checkout", r.licenseHandler.CheckoutLicense)
		licenses.GET("/key/:key/info", r.licenseHandler.GetLicenseInfo) // Changed to avoid conflict
	}

	// Machine operations (public for end-user software)
	machines := rg.Group("/machines")
	{
		// Machine registration and heartbeat - authenticated via license key
		machines.POST("/checkout", r.machineHandler.CheckoutMachine)
		machines.POST("/:id/heartbeat", r.machineHandler.SendHeartbeat)
		machines.POST("/:id/check-in", r.machineHandler.CheckInMachine)
	}
}

// setupProtectedRoutes configures routes that require authentication
func (r *APIRoutes) setupProtectedRoutes(rg *gin.RouterGroup) {
	// Organization management
	orgs := rg.Group("/organizations")
	{
		orgs.GET("", 
			r.authzMiddleware.RequireResourceAccess(middleware.ResourceTypeOrganization, middleware.ActionRead),
			r.organizationHandler.ListOrganizations)
		orgs.POST("", 
			r.authzMiddleware.RequireResourceAccess(middleware.ResourceTypeOrganization, middleware.ActionCreate),
			r.organizationHandler.CreateOrganization)
		orgs.GET("/:id", 
			r.authzMiddleware.RequireResourceAccess(middleware.ResourceTypeOrganization, middleware.ActionRead),
			r.organizationHandler.GetOrganization)
		orgs.PUT("/:id", 
			r.authzMiddleware.RequireResourceAccess(middleware.ResourceTypeOrganization, middleware.ActionUpdate),
			r.organizationHandler.UpdateOrganization)
		orgs.DELETE("/:id", 
			r.authzMiddleware.RequireResourceAccess(middleware.ResourceTypeOrganization, middleware.ActionDelete),
			r.organizationHandler.DeleteOrganization)
	}

	// Product management
	products := rg.Group("/products")
	{
		products.GET("", 
			r.authzMiddleware.RequireResourceAccess(middleware.ResourceTypeProduct, middleware.ActionRead),
			r.productHandler.ListProducts)
		products.POST("", 
			r.authzMiddleware.RequireResourceAccess(middleware.ResourceTypeProduct, middleware.ActionCreate),
			r.productHandler.CreateProduct)
		products.GET("/:id", 
			r.authzMiddleware.RequireResourceAccess(middleware.ResourceTypeProduct, middleware.ActionRead),
			r.productHandler.GetProduct)
		products.PUT("/:id", 
			r.authzMiddleware.RequireResourceAccess(middleware.ResourceTypeProduct, middleware.ActionUpdate),
			r.productHandler.UpdateProduct)
		products.DELETE("/:id", 
			r.authzMiddleware.RequireResourceAccess(middleware.ResourceTypeProduct, middleware.ActionDelete),
			r.productHandler.DeleteProductHandler)
	}

	// Policy management
	policies := rg.Group("/policies")
	{
		policies.GET("", 
			r.authzMiddleware.RequireResourceAccess(middleware.ResourceTypePolicy, middleware.ActionRead),
			r.policyHandler.ListPolicies)
		policies.POST("", 
			r.authzMiddleware.RequireResourceAccess(middleware.ResourceTypePolicy, middleware.ActionCreate),
			r.policyHandler.CreatePolicy)
		policies.GET("/:id", 
			r.authzMiddleware.RequireResourceAccess(middleware.ResourceTypePolicy, middleware.ActionRead),
			r.policyHandler.GetPolicy)
		policies.PUT("/:id", 
			r.authzMiddleware.RequireResourceAccess(middleware.ResourceTypePolicy, middleware.ActionUpdate),
			r.policyHandler.UpdatePolicy)
		policies.DELETE("/:id", 
			r.authzMiddleware.RequireResourceAccess(middleware.ResourceTypePolicy, middleware.ActionDelete),
			r.policyHandler.DeletePolicyHandler)
	}

	// License management (for organizations)
	licenses := rg.Group("/licenses")
	{
		licenses.GET("", 
			r.authzMiddleware.RequireResourceAccess(middleware.ResourceTypeLicense, middleware.ActionRead),
			r.licenseHandler.ListLicenses)
		licenses.POST("", 
			r.authzMiddleware.RequireResourceAccess(middleware.ResourceTypeLicense, middleware.ActionCreate),
			r.licenseHandler.CreateLicense)
		licenses.GET("/:id", 
			r.authzMiddleware.RequireResourceAccess(middleware.ResourceTypeLicense, middleware.ActionRead),
			r.licenseHandler.GetLicense)
		licenses.PUT("/:id", 
			r.authzMiddleware.RequireResourceAccess(middleware.ResourceTypeLicense, middleware.ActionUpdate),
			r.licenseHandler.UpdateLicense)
		licenses.DELETE("/:id", 
			r.authzMiddleware.RequireResourceAccess(middleware.ResourceTypeLicense, middleware.ActionDelete),
			r.licenseHandler.DeleteLicense)
		
		// License validation with authentication
		licenses.POST("/:id/validate", 
			r.authzMiddleware.RequireResourceAccess(middleware.ResourceTypeLicense, middleware.ActionValidate),
			r.licenseHandler.ValidateLicense)
	}

	// Machine management (for organizations)
	machines := rg.Group("/machines")
	{
		machines.GET("", 
			r.authzMiddleware.RequireResourceAccess(middleware.ResourceTypeMachine, middleware.ActionRead),
			r.machineHandler.ListMachines)
		machines.GET("/:id", 
			r.authzMiddleware.RequireResourceAccess(middleware.ResourceTypeMachine, middleware.ActionRead),
			r.machineHandler.GetMachine)
		machines.PUT("/:id", 
			r.authzMiddleware.RequireResourceAccess(middleware.ResourceTypeMachine, middleware.ActionUpdate),
			r.machineHandler.UpdateMachine)
		machines.DELETE("/:id", 
			r.authzMiddleware.RequireResourceAccess(middleware.ResourceTypeMachine, middleware.ActionDelete),
			r.machineHandler.DeleteMachineHandler)
	}

	// User management
	users := rg.Group("/users")
	{
		users.GET("", 
			r.authzMiddleware.RequireResourceAccess(middleware.ResourceTypeUser, middleware.ActionRead),
			r.authHandler.ListUsers)
		users.GET("/:id", 
			r.authzMiddleware.RequireResourceAccess(middleware.ResourceTypeUser, middleware.ActionRead),
			r.authHandler.GetUser)
		users.PUT("/:id", 
			r.authzMiddleware.RequireResourceAccess(middleware.ResourceTypeUser, middleware.ActionUpdate),
			r.authHandler.UpdateUser)
	}

	// User-Organization management (core permission system)
	userOrgs := rg.Group("/user-organizations")
	{
		userOrgs.GET("", 
			r.authzMiddleware.RequireResourceAccess(middleware.ResourceTypeUserOrganization, middleware.ActionRead),
			r.userOrganizationHandler.ListUserOrganizations)
		userOrgs.POST("", 
			r.authzMiddleware.RequireResourceAccess(middleware.ResourceTypeUserOrganization, middleware.ActionCreate),
			r.userOrganizationHandler.CreateUserOrganization)
		userOrgs.GET("/:id", 
			r.authzMiddleware.RequireResourceAccess(middleware.ResourceTypeUserOrganization, middleware.ActionRead),
			r.userOrganizationHandler.GetUserOrganization)
		userOrgs.PUT("/:id", 
			r.authzMiddleware.RequireResourceAccess(middleware.ResourceTypeUserOrganization, middleware.ActionUpdate),
			r.userOrganizationHandler.UpdateUserOrganization)
		userOrgs.DELETE("/:id", 
			r.authzMiddleware.RequireResourceAccess(middleware.ResourceTypeUserOrganization, middleware.ActionDelete),
			r.userOrganizationHandler.DeleteUserOrganization)
	}

	// User permissions endpoint (to get effective permissions for a user)
	// Move to user-organizations group to avoid conflict
	userOrgs.GET("/user/:user_id/permissions", 
		r.authzMiddleware.RequireResourceAccess(middleware.ResourceTypeUser, middleware.ActionRead),
		r.userOrganizationHandler.GetUserPermissions)

	// TODO: Webhook endpoints sẽ được implement sau khi có entity
}

// setupAdminRoutes configures routes that require admin permissions
func (r *APIRoutes) setupAdminRoutes(rg *gin.RouterGroup) {
	// User management (admin only)
	users := rg.Group("/users")
	{
		users.POST("", r.authHandler.CreateUser)
		users.DELETE("/:id", r.authHandler.DeleteUser)
	}

	// TODO: Implement system-wide analytics and metrics
	// rg.GET("/analytics", r.organizationHandler.GetSystemAnalytics)
	// rg.GET("/metrics", r.organizationHandler.GetSystemMetrics)
	
	// TODO: Implement policy debugging and management
	// policies := rg.Group("/policies")
	// {
	//     policies.GET("/debug/:id", r.policyHandler.DebugPolicy)
	//     policies.POST("/validate", r.policyHandler.ValidatePolicy)
	// }
}

// GetAuthService returns the auth service for use in other components
func (r *APIRoutes) GetAuthService() *auth.AuthService {
	return r.authService
}
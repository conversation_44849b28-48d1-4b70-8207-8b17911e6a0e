package routes

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/gokeys/gokeys/internal/adapters/http/handlers"
	"github.com/gokeys/gokeys/internal/adapters/http/middleware"
	"github.com/gokeys/gokeys/internal/domain/services/auth"
)

// API route constants for better maintainability
const (
	// API versions
	APIVersionV1 = "/api/v1"
	PublicAPIV1  = "/api/v1/public"

	// Route groups
	AdminGroup         = "/admin"
	OrganizationsGroup = "/organizations"
	UsersGroup         = "/users"
	ProductsGroup      = "/products"
	LicensesGroup      = "/licenses"
	MachinesGroup      = "/machines"
	PoliciesGroup      = "/policies"
	AuthGroup          = "/auth"
	ActionsGroup       = "/actions"

	// Parameter names
	ParamOrganizationID = "organization_id"
	ParamProductID      = "product_id"
	ParamLicenseID      = "license_id"
	ParamUserID         = "user_id"
	ParamMachineID      = "machine_id"
	ParamPolicyID       = "policy_id"
	ParamKey            = "key"

	// System user ID for bypass authentication
	SystemAdminUserID = "eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee"
)

// HTTP methods for route definitions
const (
	GET    = http.MethodGet
	POST   = http.MethodPost
	PUT    = http.MethodPut
	DELETE = http.MethodDelete
	PATCH  = http.MethodPatch
)

// Route definition for clean route management
type Route struct {
	Method      string
	Path        string
	Handler     gin.HandlerFunc
	Middlewares []gin.HandlerFunc
}

// RouteGroup represents a group of related routes
type RouteGroup struct {
	Prefix      string
	Routes      []Route
	Middlewares []gin.HandlerFunc
	SubGroups   []RouteGroup
}

// APIRoutes sets up API routes with clean authorization
type APIRoutes struct {
	authService *auth.AuthService
	authMW      *middleware.AuthenticationMiddleware
	authzMW     *middleware.AuthorizationMiddleware

	// Handlers
	userHandler    *handlers.UserHandler
	orgHandler     *handlers.OrganizationHandler
	productHandler *handlers.ProductHandler
	licenseHandler *handlers.LicenseHandler
	policyHandler  *handlers.PolicyHandler
	machineHandler *handlers.MachineHandler
	authHandler    *handlers.AuthHandler
}

// NewAPIRoutes creates a new API routes handler
func NewAPIRoutes(
	authService *auth.AuthService,
	authMW *middleware.AuthenticationMiddleware,
	authzMW *middleware.AuthorizationMiddleware,
	userHandler *handlers.UserHandler,
	orgHandler *handlers.OrganizationHandler,
	productHandler *handlers.ProductHandler,
	licenseHandler *handlers.LicenseHandler,
	policyHandler *handlers.PolicyHandler,
	machineHandler *handlers.MachineHandler,
	authHandler *handlers.AuthHandler,
) *APIRoutes {
	return &APIRoutes{
		authService:    authService,
		authMW:         authMW,
		authzMW:        authzMW,
		userHandler:    userHandler,
		orgHandler:     orgHandler,
		productHandler: productHandler,
		licenseHandler: licenseHandler,
		policyHandler:  policyHandler,
		machineHandler: machineHandler,
		authHandler:    authHandler,
	}
}

// Helper functions for clean route management

// noAuthRequired is a temporary helper to bypass authorization during business logic testing
func (r *APIRoutes) noAuthRequired() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Set dummy user context for handlers that expect it
		c.Set("user_id", SystemAdminUserID)
		c.Set("auth_type", "bypass")
		c.Next()
	}
}

// withAuth creates a middleware chain with authentication
func (r *APIRoutes) withAuth() []gin.HandlerFunc {
	return []gin.HandlerFunc{
		r.authMW.RequireAuth(),
		r.authzMW.SetOrganizationContext(),
	}
}

// withNoAuth creates a middleware chain without authentication (for testing)
func (r *APIRoutes) withNoAuth() []gin.HandlerFunc {
	return []gin.HandlerFunc{r.noAuthRequired()}
}

// createRoute creates a route with the specified method, path, handler and middlewares
func createRoute(method, path string, handler gin.HandlerFunc, middlewares ...gin.HandlerFunc) Route {
	return Route{
		Method:      method,
		Path:        path,
		Handler:     handler,
		Middlewares: middlewares,
	}
}

// applyRoutes applies a slice of routes to a router group
func (r *APIRoutes) applyRoutes(group *gin.RouterGroup, routes []Route) {
	for _, route := range routes {
		handlers := append(route.Middlewares, route.Handler)
		group.Handle(route.Method, route.Path, handlers...)
	}
}

// applyRouteGroup applies a route group to a router group recursively
func (r *APIRoutes) applyRouteGroup(parent gin.IRouter, routeGroup RouteGroup) {
	group := parent.Group(routeGroup.Prefix, routeGroup.Middlewares...)

	// Apply routes in this group
	r.applyRoutes(group, routeGroup.Routes)

	// Apply sub-groups recursively
	for _, subGroup := range routeGroup.SubGroups {
		r.applyRouteGroup(group, subGroup)
	}
}

// paramPath creates a parameterized path
func paramPath(segments ...string) string {
	path := ""
	for _, segment := range segments {
		if segment[0] == ':' {
			path += "/" + segment
		} else {
			path += "/" + segment
		}
	}
	return path
}

// resourcePath creates a RESTful resource path
func resourcePath(resource string, id ...string) string {
	path := "/" + resource
	if len(id) > 0 {
		path += "/:" + id[0]
	}
	return path
}

// SetupRoutes sets up all API routes using clean Go patterns
func (r *APIRoutes) SetupRoutes(router *gin.Engine) {
	// Define route groups using Go's composability
	routeGroups := []RouteGroup{
		r.createPublicRouteGroup(),
		r.createProtectedRouteGroup(),
	}

	// Apply all route groups
	for _, group := range routeGroups {
		r.applyRouteGroup(router, group)
	}
}

// createPublicRouteGroup creates public API routes (no authentication)
func (r *APIRoutes) createPublicRouteGroup() RouteGroup {
	return RouteGroup{
		Prefix: PublicAPIV1,
		Routes: []Route{
			// Authentication endpoints
			createRoute(POST, "/auth/login", r.authHandler.Login),
			createRoute(POST, "/auth/register", r.authHandler.Register),

			// Public license validation endpoints
			createRoute(POST, "/licenses/validate", r.licenseHandler.ValidateLicense),
			createRoute(GET, paramPath("licenses", ":"+ParamKey, "info"), r.licenseHandler.GetLicenseInfo),
		},
	}
}

// createProtectedRouteGroup creates protected API routes with authentication
func (r *APIRoutes) createProtectedRouteGroup() RouteGroup {
	// Use temporary bypass for business logic testing
	authMiddlewares := r.withNoAuth()
	// TODO: Replace with r.withAuth() after business logic is complete

	return RouteGroup{
		Prefix:      APIVersionV1,
		Middlewares: authMiddlewares,
		SubGroups: []RouteGroup{
			r.createSystemAdminRouteGroup(),
			r.createOrganizationRouteGroup(),
			r.createUserRouteGroup(),
			r.createGlobalLicenseRouteGroup(),
			r.createGlobalMachineRouteGroup(),
		},
	}
}

// Route group creation methods using Go's composability

// createSystemAdminRouteGroup creates system admin routes
func (r *APIRoutes) createSystemAdminRouteGroup() RouteGroup {
	return RouteGroup{
		Prefix: AdminGroup,
		Routes: []Route{
			// Organization management (system admin only)
			createRoute(GET, resourcePath("organizations"), r.orgHandler.ListAllOrganizations),
			createRoute(POST, resourcePath("organizations"), r.orgHandler.CreateOrganization),

			// User management across organizations
			createRoute(GET, resourcePath("users"), func(c *gin.Context) {
				c.JSON(200, gin.H{"message": "list all users (system admin)"})
			}),

			// System-wide statistics and monitoring
			createRoute(GET, "/stats", r.orgHandler.GetSystemStats),
		},
	}
}

// createOrganizationRouteGroup creates organization-scoped routes
func (r *APIRoutes) createOrganizationRouteGroup() RouteGroup {
	return RouteGroup{
		Prefix: resourcePath("organizations", ParamOrganizationID),
		Routes: []Route{
			// Organization info (members can read)
			createRoute(GET, "/", r.orgHandler.GetOrganization),
			// Organization management (admin only)
			createRoute(PUT, "/", r.orgHandler.UpdateOrganization),
		},
		SubGroups: []RouteGroup{
			r.createOrganizationUserRouteGroup(),
			r.createOrganizationProductRouteGroup(),
		},
	}
}

// createOrganizationUserRouteGroup creates user management within organization
func (r *APIRoutes) createOrganizationUserRouteGroup() RouteGroup {
	return RouteGroup{
		Prefix: UsersGroup,
		Routes: []Route{
			// List organization users (members can read)
			createRoute(GET, "/", r.userHandler.ListOrganizationUsers),
			// Add user to organization (admin only)
			createRoute(POST, "/", r.userHandler.AddUserToOrganization),
		},
		SubGroups: []RouteGroup{
			{
				Prefix: resourcePath("", ParamUserID),
				Routes: []Route{
					// Get user info (members can read)
					createRoute(GET, "/", r.userHandler.GetOrganizationUser),
					// Remove user from organization (admin only)
					createRoute(DELETE, "/", r.userHandler.RemoveUserFromOrganization),
					// User permissions management (admin only)
					createRoute(GET, "/permissions", r.userHandler.GetUserPermissionsInOrganization),
					createRoute(POST, "/permissions", r.userHandler.GrantUserPermission),
					createRoute(DELETE, "/permissions", r.userHandler.RevokeUserPermission),
				},
			},
		},
	}
}

// createOrganizationProductRouteGroup creates product management within organization
func (r *APIRoutes) createOrganizationProductRouteGroup() RouteGroup {
	return RouteGroup{
		Prefix: ProductsGroup,
		Routes: []Route{
			// List products (members can read)
			createRoute(GET, "/", r.productHandler.ListOrganizationProducts),
			// Create product (requires create permission)
			createRoute(POST, "/", r.productHandler.CreateProduct),
		},
		SubGroups: []RouteGroup{
			r.createProductRouteGroup(),
		},
	}
}

// createProductRouteGroup creates product-specific routes
func (r *APIRoutes) createProductRouteGroup() RouteGroup {
	return RouteGroup{
		Prefix: resourcePath("", ParamProductID),
		Routes: []Route{
			// Get product info
			createRoute(GET, "/", r.productHandler.GetProduct),
			// Update product
			createRoute(PUT, "/", r.productHandler.UpdateProduct),
			// Delete product
			createRoute(DELETE, "/", r.productHandler.DeleteProduct),
			// Product stats
			createRoute(GET, "/stats", r.productHandler.GetProductStats),
		},
		SubGroups: []RouteGroup{
			r.createProductPolicyRouteGroup(),
			r.createProductLicenseRouteGroup(),
		},
	}
}

// createProductPolicyRouteGroup creates policy management for products
func (r *APIRoutes) createProductPolicyRouteGroup() RouteGroup {
	return RouteGroup{
		Prefix: PoliciesGroup,
		Routes: []Route{
			// List product policies
			createRoute(GET, "/", r.policyHandler.ListPolicies),
			// Create policy
			createRoute(POST, "/", r.policyHandler.CreatePolicy),
		},
		SubGroups: []RouteGroup{
			{
				Prefix: resourcePath("", ParamPolicyID),
				Routes: []Route{
					// Get policy info
					createRoute(GET, "/", r.policyHandler.GetPolicy),
					// Update policy
					createRoute(PUT, "/", r.policyHandler.UpdatePolicy),
					// Delete policy
					createRoute(DELETE, "/", r.policyHandler.DeletePolicy),
				},
			},
		},
	}
}

// createProductLicenseRouteGroup creates license management for products
func (r *APIRoutes) createProductLicenseRouteGroup() RouteGroup {
	return RouteGroup{
		Prefix: LicensesGroup,
		Routes: []Route{
			// List product licenses
			createRoute(GET, "/", r.licenseHandler.ListProductLicenses),
			// Create license
			createRoute(POST, "/", r.licenseHandler.CreateLicense),
		},
		SubGroups: []RouteGroup{
			r.createLicenseRouteGroup(),
		},
	}
}

// createLicenseRouteGroup creates license-specific routes
func (r *APIRoutes) createLicenseRouteGroup() RouteGroup {
	return RouteGroup{
		Prefix: resourcePath("", ParamLicenseID),
		Routes: []Route{
			// Get license info
			createRoute(GET, "/", r.licenseHandler.GetLicense),
			// Update license
			createRoute(PUT, "/", r.licenseHandler.UpdateLicense),
			// Delete/revoke license
			createRoute(DELETE, "/", r.licenseHandler.DeleteLicense),
			// License statistics
			createRoute(GET, "/stats", r.licenseHandler.GetLicenseStats),
		},
		SubGroups: []RouteGroup{
			r.createLicenseActionRouteGroup(),
			r.createLicenseMachineRouteGroup(),
		},
	}
}

// createLicenseActionRouteGroup creates license action routes following keygen-api patterns
func (r *APIRoutes) createLicenseActionRouteGroup() RouteGroup {
	return RouteGroup{
		Prefix: ActionsGroup,
		Routes: []Route{
			// License validation actions
			createRoute(GET, "/validate", r.licenseHandler.QuickValidateLicenseByID),
			createRoute(POST, "/validate", r.licenseHandler.ValidateLicenseByKey),

			// License permit actions (following keygen-api permits_controller.rb)
			createRoute(POST, "/suspend", r.licenseHandler.SuspendLicense),
			createRoute(POST, "/reinstate", r.licenseHandler.ReinstateLicense),
			createRoute(DELETE, "/revoke", r.licenseHandler.RevokeLicense),
			createRoute(POST, "/renew", r.licenseHandler.RenewLicense),
			createRoute(POST, "/check-in", r.licenseHandler.CheckInLicense),

			// License usage actions (following keygen-api uses_controller.rb)
			createRoute(POST, "/increment-usage", r.licenseHandler.IncrementUsage),
			createRoute(POST, "/decrement-usage", r.licenseHandler.DecrementUsage),
			createRoute(POST, "/reset-usage", r.licenseHandler.ResetUsage),

			// License checkout actions
			createRoute(POST, "/check-out", r.licenseHandler.CheckoutLicense),
			createRoute(GET, "/check-out", r.licenseHandler.CheckoutLicense),
		},
	}
}

// createLicenseMachineRouteGroup creates machine management for licenses
func (r *APIRoutes) createLicenseMachineRouteGroup() RouteGroup {
	return RouteGroup{
		Prefix: MachinesGroup,
		Routes: []Route{
			// List license machines
			createRoute(GET, "/", r.licenseHandler.GetLicenseMachines),
			// Machine checkout/registration
			createRoute(POST, "/", r.machineHandler.CreateMachine),
		},
		SubGroups: []RouteGroup{
			{
				Prefix: resourcePath("", ParamMachineID),
				Routes: []Route{
					// Get machine info
					createRoute(GET, "/", r.machineHandler.GetMachine),
					// Machine heartbeat
					createRoute(POST, "/heartbeat", r.machineHandler.MachineHeartbeat),
					// Remove machine
					createRoute(DELETE, "/", r.machineHandler.DeleteMachine),
				},
			},
		},
	}
}

// createUserRouteGroup creates user-specific routes
func (r *APIRoutes) createUserRouteGroup() RouteGroup {
	return RouteGroup{
		Prefix: UsersGroup,
		Routes: []Route{
			// Current user info
			createRoute(GET, "/me", r.userHandler.GetCurrentUser),
			// Update current user
			createRoute(PUT, "/me", r.userHandler.UpdateCurrentUser),
			// User's organizations
			createRoute(GET, "/me/organizations", r.userHandler.GetUserOrganizations),
			// User's permissions
			createRoute(GET, "/me/permissions", r.userHandler.GetUserPermissions),
		},
		SubGroups: []RouteGroup{
			{
				Prefix: AuthGroup,
				Routes: []Route{
					createRoute(POST, "/logout", r.authHandler.Logout),
					createRoute(POST, "/refresh", r.authHandler.RefreshToken),
				},
			},
		},
	}
}

// createGlobalLicenseRouteGroup creates general license routes (across organizations)
func (r *APIRoutes) createGlobalLicenseRouteGroup() RouteGroup {
	return RouteGroup{
		Prefix: LicensesGroup,
		SubGroups: []RouteGroup{
			{
				Prefix: ActionsGroup,
				Routes: []Route{
					// Global license validation by key (Ruby: validate_key)
					createRoute(POST, "/validate-key", r.licenseHandler.ValidateLicenseByKey),
				},
			},
			{
				Prefix: paramPath(":" + ParamKey),
				Routes: []Route{
					// License key-based routes
					createRoute(POST, "/validate", r.licenseHandler.ValidateLicense),
					createRoute(POST, "/machines", func(c *gin.Context) {
						c.JSON(200, gin.H{"message": "checkout machine for license"})
					}),
				},
			},
		},
	}
}

// createGlobalMachineRouteGroup creates general machine routes
func (r *APIRoutes) createGlobalMachineRouteGroup() RouteGroup {
	return RouteGroup{
		Prefix: MachinesGroup,
		Routes: []Route{
			// Machine heartbeat (can be called by machine with license key)
			createRoute(POST, paramPath(":"+ParamMachineID, "heartbeat"), r.machineHandler.MachineHeartbeat),
		},
		SubGroups: []RouteGroup{
			// Organization-scoped machine management
			{
				Prefix: paramPath("organizations", ":"+ParamOrganizationID, "machines"),
				Routes: []Route{
					// List organization machines
					createRoute(GET, "/", r.machineHandler.ListMachines),
					// Create machine
					createRoute(POST, "/", r.machineHandler.CreateMachine),
				},
				SubGroups: []RouteGroup{
					{
						Prefix: resourcePath("", ParamMachineID),
						Routes: []Route{
							// Get machine info
							createRoute(GET, "/", r.machineHandler.GetMachine),
							// Update machine
							createRoute(PUT, "/", r.machineHandler.UpdateMachine),
							// Delete machine
							createRoute(DELETE, "/", r.machineHandler.DeleteMachine),
							// Machine heartbeat
							createRoute(POST, "/heartbeat", r.machineHandler.MachineHeartbeat),
							// Machine processes
							createRoute(GET, "/processes", r.machineHandler.GetMachineProcesses),
							// Machine components
							createRoute(GET, "/components", r.machineHandler.GetMachineComponents),
						},
					},
				},
			},
		},
	}
}

// setupPublicRoutes sets up public routes (no authentication required)
func (r *APIRoutes) setupPublicRoutes(public *gin.RouterGroup) {
	// Authentication endpoints
	public.POST("/auth/login", r.authHandler.Login)
	public.POST("/auth/register", r.authHandler.Register)

	// License validation endpoints (public with license key auth)
	public.POST("/licenses/validate", r.licenseHandler.ValidateLicense)
	public.GET("/licenses/:key/info", r.licenseHandler.GetLicenseInfo)
}

// setupSystemAdminRoutes sets up system admin routes
func (r *APIRoutes) setupSystemAdminRoutes(api *gin.RouterGroup) {
	admin := api.Group("/admin")
	admin.Use(r.noAuthRequired())
	{
		// Organization management (system admin only)
		admin.GET("/organizations", r.orgHandler.ListAllOrganizations)
		admin.POST("/organizations", r.orgHandler.CreateOrganization)

		// User management across organizations
		admin.GET("/users", func(c *gin.Context) {
			c.JSON(200, gin.H{"message": "list all users (system admin)"})
		})

		// System-wide statistics and monitoring
		admin.GET("/stats", r.orgHandler.GetSystemStats)
	}
}

// setupOrganizationRoutes sets up organization-scoped routes
func (r *APIRoutes) setupOrganizationRoutes(api *gin.RouterGroup) {
	// Organization routes with organization context
	orgs := api.Group("/organizations/:organization_id")
	orgs.Use(r.noAuthRequired())
	{
		// Organization info (members can read)
		orgs.GET("/", r.noAuthRequired(), r.orgHandler.GetOrganization)

		// Organization management (admin only)
		orgs.PUT("/", r.noAuthRequired(), r.orgHandler.UpdateOrganization)

		// User management within organization
		r.setupOrganizationUserRoutes(orgs)

		// Product management within organization
		r.setupOrganizationProductRoutes(orgs)
	}
}

// setupOrganizationUserRoutes sets up user management within organization
func (r *APIRoutes) setupOrganizationUserRoutes(orgs *gin.RouterGroup) {
	users := orgs.Group("/users")
	{
		// List organization users (members can read)
		users.GET("/", r.noAuthRequired(), r.userHandler.ListOrganizationUsers)

		// Add user to organization (admin only)
		users.POST("/", r.noAuthRequired(), r.userHandler.AddUserToOrganization)

		// User-specific routes
		user := users.Group("/:user_id")
		{
			// Get user info (members can read)
			user.GET("/", r.noAuthRequired(), r.userHandler.GetOrganizationUser)

			// Remove user from organization (admin only)
			user.DELETE("/", r.noAuthRequired(), r.userHandler.RemoveUserFromOrganization)

			// User permissions management (admin only)
			user.GET("/permissions", r.noAuthRequired(), r.userHandler.GetUserPermissionsInOrganization)
			user.POST("/permissions", r.noAuthRequired(), r.userHandler.GrantUserPermission)
			user.DELETE("/permissions", r.noAuthRequired(), r.userHandler.RevokeUserPermission)
		}
	}
}

// setupOrganizationProductRoutes sets up product management within organization
func (r *APIRoutes) setupOrganizationProductRoutes(orgs *gin.RouterGroup) {
	products := orgs.Group("/products")
	{
		// List products (members can read)
		products.GET("/", r.noAuthRequired(), r.productHandler.ListOrganizationProducts)

		// Create product (requires create permission)
		products.POST("/", r.noAuthRequired(), r.productHandler.CreateProduct)

		// Product-specific routes
		product := products.Group("/:product_id")
		{
			// Get product info
			product.GET("/", r.noAuthRequired(), r.productHandler.GetProduct)

			// Update product
			product.PUT("/", r.noAuthRequired(), r.productHandler.UpdateProduct)

			// Delete product
			product.DELETE("/", r.noAuthRequired(), r.productHandler.DeleteProduct)

			// Product policies
			r.setupProductPolicyRoutes(product)

			// Product stats
			product.GET("/stats", r.noAuthRequired(), r.productHandler.GetProductStats)

			// Product licenses
			r.setupProductLicenseRoutes(product)
		}
	}
}

// setupProductLicenseRoutes sets up license management for products
func (r *APIRoutes) setupProductLicenseRoutes(product *gin.RouterGroup) {
	licenses := product.Group("/licenses")
	{
		// List product licenses
		licenses.GET("/", r.noAuthRequired(), r.licenseHandler.ListProductLicenses)

		// Create license
		licenses.POST("/", r.noAuthRequired(), r.licenseHandler.CreateLicense)

		// License-specific routes
		license := licenses.Group("/:license_id")
		{
			// Get license info
			license.GET("/", r.noAuthRequired(), r.licenseHandler.GetLicense)

			// Update license
			license.PUT("/", r.noAuthRequired(), r.licenseHandler.UpdateLicense)

			// Delete/revoke license
			license.DELETE("/", r.noAuthRequired(), r.licenseHandler.DeleteLicense)

			// License statistics
			license.GET("/stats", r.noAuthRequired(), r.licenseHandler.GetLicenseStats)

			// License actions (following keygen-api patterns)
			r.setupLicenseActionRoutes(license)

			// License machines
			r.setupLicenseMachineRoutes(license)
		}
	}
}

// setupLicenseActionRoutes sets up license action routes following keygen-api patterns
func (r *APIRoutes) setupLicenseActionRoutes(license *gin.RouterGroup) {
	actions := license.Group("/actions")
	{
		// License validation actions
		actions.GET("/validate", r.noAuthRequired(), r.licenseHandler.QuickValidateLicenseByID)
		actions.POST("/validate", r.noAuthRequired(), r.licenseHandler.ValidateLicenseByKey)

		// License permit actions (following keygen-api permits_controller.rb)
		actions.POST("/suspend", r.noAuthRequired(), r.licenseHandler.SuspendLicense)
		actions.POST("/reinstate", r.noAuthRequired(), r.licenseHandler.ReinstateLicense)
		actions.DELETE("/revoke", r.noAuthRequired(), r.licenseHandler.RevokeLicense)
		actions.POST("/renew", r.noAuthRequired(), r.licenseHandler.RenewLicense)
		actions.POST("/check-in", r.noAuthRequired(), r.licenseHandler.CheckInLicense)

		// License usage actions (following keygen-api uses_controller.rb)
		actions.POST("/increment-usage", r.noAuthRequired(), r.licenseHandler.IncrementUsage)
		actions.POST("/decrement-usage", r.noAuthRequired(), r.licenseHandler.DecrementUsage)
		actions.POST("/reset-usage", r.noAuthRequired(), r.licenseHandler.ResetUsage)

		// License checkout actions
		actions.POST("/check-out", r.noAuthRequired(), r.licenseHandler.CheckoutLicense)
		actions.GET("/check-out", r.noAuthRequired(), r.licenseHandler.CheckoutLicense)
	}
}

// setupLicenseMachineRoutes sets up machine management for licenses
func (r *APIRoutes) setupLicenseMachineRoutes(license *gin.RouterGroup) {
	machines := license.Group("/machines")
	{
		// List license machines
		machines.GET("/", r.noAuthRequired(), r.licenseHandler.GetLicenseMachines)

		// Machine checkout/registration
		machines.POST("/", r.noAuthRequired(), r.machineHandler.CreateMachine)

		// Machine-specific routes
		machine := machines.Group("/:machine_id")
		{
			// Get machine info
			machine.GET("/", r.noAuthRequired(), r.machineHandler.GetMachine)

			// Machine heartbeat
			machine.POST("/heartbeat", r.noAuthRequired(), r.machineHandler.MachineHeartbeat)

			// Remove machine
			machine.DELETE("/", r.noAuthRequired(), r.machineHandler.DeleteMachine)
		}
	}
}

// setupProductPolicyRoutes sets up policy management for products
func (r *APIRoutes) setupProductPolicyRoutes(product *gin.RouterGroup) {
	policies := product.Group("/policies")
	{
		// List product policies
		policies.GET("/", r.noAuthRequired(), r.policyHandler.ListPolicies)

		// Create policy
		policies.POST("/", r.noAuthRequired(), r.policyHandler.CreatePolicy)

		// Policy-specific routes
		policy := policies.Group("/:policy_id")
		{
			// Get policy info
			policy.GET("/", r.noAuthRequired(), r.policyHandler.GetPolicy)

			// Update policy
			policy.PUT("/", r.noAuthRequired(), r.policyHandler.UpdatePolicy)

			// Delete policy
			policy.DELETE("/", r.noAuthRequired(), r.policyHandler.DeletePolicy)
		}
	}
}

// setupUserRoutes sets up user-specific routes
func (r *APIRoutes) setupUserRoutes(api *gin.RouterGroup) {
	users := api.Group("/users")
	{
		// Current user info
		users.GET("/me", r.userHandler.GetCurrentUser)

		// Update current user
		users.PUT("/me", r.userHandler.UpdateCurrentUser)

		// User's organizations
		users.GET("/me/organizations", r.userHandler.GetUserOrganizations)

		// User's permissions
		users.GET("/me/permissions", r.userHandler.GetUserPermissions)
	}

	// Authentication routes (protected)
	auth := api.Group("/auth")
	{
		auth.POST("/logout", r.authHandler.Logout)
		auth.POST("/refresh", r.authHandler.RefreshToken)
	}
}

// setupLicenseRoutes sets up general license routes (across organizations)
func (r *APIRoutes) setupLicenseRoutes(api *gin.RouterGroup) {
	// These routes are for license validation and machine operations
	// They use different authentication (license key based)
	licenses := api.Group("/licenses")
	{
		// License validation actions (following keygen-api collection routes)
		actions := licenses.Group("/actions")
		{
			// Global license validation by key (Ruby: validate_key)
			actions.POST("/validate-key", r.noAuthRequired(), r.licenseHandler.ValidateLicenseByKey)
		}

		// License key-based routes
		licenses.POST("/:key/validate", r.noAuthRequired(), r.licenseHandler.ValidateLicense)
		licenses.POST("/:key/machines", func(c *gin.Context) {
			c.JSON(200, gin.H{"message": "checkout machine for license"})
		})
	}
}

// setupMachineRoutes sets up general machine routes
func (r *APIRoutes) setupMachineRoutes(api *gin.RouterGroup) {
	// Organization-scoped machine management
	orgMachines := api.Group("/organizations/:organization_id/machines")
	{
		// List organization machines
		orgMachines.GET("/", r.noAuthRequired(), r.machineHandler.ListMachines)

		// Create machine
		orgMachines.POST("/", r.noAuthRequired(), r.machineHandler.CreateMachine)

		// Machine-specific routes
		machine := orgMachines.Group("/:machine_id")
		{
			// Get machine info
			machine.GET("/", r.noAuthRequired(), r.machineHandler.GetMachine)

			// Update machine
			machine.PUT("/", r.noAuthRequired(), r.machineHandler.UpdateMachine)

			// Delete machine
			machine.DELETE("/", r.noAuthRequired(), r.machineHandler.DeleteMachine)

			// Machine heartbeat
			machine.POST("/heartbeat", r.noAuthRequired(), r.machineHandler.MachineHeartbeat)

			// Machine processes
			machine.GET("/processes", r.noAuthRequired(), r.machineHandler.GetMachineProcesses)

			// Machine components
			machine.GET("/components", r.noAuthRequired(), r.machineHandler.GetMachineComponents)
		}
	}

	// Global machine routes (for license key authentication)
	machines := api.Group("/machines")
	{
		// Machine heartbeat (can be called by machine with license key)
		machines.POST("/:machine_id/heartbeat", r.noAuthRequired(), r.machineHandler.MachineHeartbeat)
	}
}

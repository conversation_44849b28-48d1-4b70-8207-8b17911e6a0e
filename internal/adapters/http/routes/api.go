package routes

import (
	"github.com/gin-gonic/gin"
	"github.com/gokeys/gokeys/internal/adapters/http/handlers"
	"github.com/gokeys/gokeys/internal/adapters/http/middleware"
	"github.com/gokeys/gokeys/internal/domain/services/auth"
)

// APIRoutes sets up API routes with clean authorization
type APIRoutes struct {
	authService *auth.AuthService
	authMW      *middleware.AuthenticationMiddleware
	authzMW     *middleware.AuthorizationMiddleware
	
	// Handlers
	userHandler    *handlers.UserHandler
	orgHandler     *handlers.OrganizationHandler
	productHandler *handlers.ProductHandler
	licenseHandler *handlers.LicenseHandler
	policyHandler  *handlers.PolicyHandler
	machineHandler *handlers.MachineHandler
	authHandler    *handlers.AuthHandler
}

// NewAPIRoutes creates a new API routes handler
func NewAPIRoutes(
	authService *auth.AuthService,
	authMW *middleware.AuthenticationMiddleware,
	authzMW *middleware.AuthorizationMiddleware,
	userHandler *handlers.UserHandler,
	orgHandler *handlers.<PERSON>H<PERSON><PERSON>,
	productHandler *handlers.ProductHandler,
	licenseHandler *handlers.LicenseHandler,
	policyHandler *handlers.PolicyHandler,
	machineHandler *handlers.MachineHandler,
	authHandler *handlers.AuthHandler,
) *APIRoutes {
	return &APIRoutes{
		authService:    authService,
		authMW:         authMW,
		authzMW:        authzMW,
		userHandler:    userHandler,
		orgHandler:     orgHandler,
		productHandler: productHandler,
		licenseHandler: licenseHandler,
		policyHandler:  policyHandler,
		machineHandler: machineHandler,
		authHandler:    authHandler,
	}
}

// noAuthRequired is a temporary helper to bypass authorization during business logic testing
func (r *APIRoutes) noAuthRequired() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Set dummy user context for handlers that expect it
		c.Set("user_id", "eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee") // System admin
		c.Set("auth_type", "bypass")
		c.Next()
	}
}

// SetupRoutes sets up all API routes
func (r *APIRoutes) SetupRoutes(router *gin.Engine) {
	// Public API routes (no auth required)
	public := router.Group("/api/v1/public")
	r.setupPublicRoutes(public)

	// Protected API routes (authentication required) - TEMPORARILY DISABLED FOR BUSINESS LOGIC TESTING  
	api := router.Group("/api/v1")
	// TODO: Re-enable after business logic is complete
	// api.Use(r.authMW.RequireAuth())
	// api.Use(r.authzMW.SetOrganizationContext())
	api.Use(r.noAuthRequired()) // Temporary bypass for business logic testing

	// System admin routes
	r.setupSystemAdminRoutes(api)

	// Organization routes
	r.setupOrganizationRoutes(api)

	// User routes
	r.setupUserRoutes(api)

	// License management routes
	r.setupLicenseRoutes(api)

	// Machine management routes
	r.setupMachineRoutes(api)
}


// setupPublicRoutes sets up public routes (no authentication required)
func (r *APIRoutes) setupPublicRoutes(public *gin.RouterGroup) {
	// Authentication endpoints
	public.POST("/auth/login", r.authHandler.Login)
	public.POST("/auth/register", r.authHandler.Register)

	// License validation endpoints (public with license key auth)
	public.POST("/licenses/validate", r.licenseHandler.ValidateLicense)
	public.GET("/licenses/:key/info", r.licenseHandler.GetLicenseInfo)
}

// setupSystemAdminRoutes sets up system admin routes
func (r *APIRoutes) setupSystemAdminRoutes(api *gin.RouterGroup) {
	admin := api.Group("/admin")
	admin.Use(r.noAuthRequired())
	{
		// Organization management (system admin only)
		admin.GET("/organizations", r.orgHandler.ListAllOrganizations)
		admin.POST("/organizations", r.orgHandler.CreateOrganization)

		// User management across organizations
		admin.GET("/users", func(c *gin.Context) {
			c.JSON(200, gin.H{"message": "list all users (system admin)"})
		})

		// System-wide statistics and monitoring
		admin.GET("/stats", r.orgHandler.GetSystemStats)
	}
}

// setupOrganizationRoutes sets up organization-scoped routes
func (r *APIRoutes) setupOrganizationRoutes(api *gin.RouterGroup) {
	// Organization routes with organization context
	orgs := api.Group("/organizations/:organization_id")
	orgs.Use(r.noAuthRequired())
	{
		// Organization info (members can read)
		orgs.GET("/", r.noAuthRequired(), r.orgHandler.GetOrganization)

		// Organization management (admin only)
		orgs.PUT("/", r.noAuthRequired(), r.orgHandler.UpdateOrganization)

		// User management within organization
		r.setupOrganizationUserRoutes(orgs)

		// Product management within organization
		r.setupOrganizationProductRoutes(orgs)
	}
}

// setupOrganizationUserRoutes sets up user management within organization
func (r *APIRoutes) setupOrganizationUserRoutes(orgs *gin.RouterGroup) {
	users := orgs.Group("/users")
	{
		// List organization users (members can read)
		users.GET("/", r.noAuthRequired(), r.userHandler.ListOrganizationUsers)

		// Add user to organization (admin only)
		users.POST("/", r.noAuthRequired(), r.userHandler.AddUserToOrganization)

		// User-specific routes
		user := users.Group("/:user_id")
		{
			// Get user info (members can read)
			user.GET("/", r.noAuthRequired(), r.userHandler.GetOrganizationUser)

			// Remove user from organization (admin only)
			user.DELETE("/", r.noAuthRequired(), r.userHandler.RemoveUserFromOrganization)

			// User permissions management (admin only)
			user.GET("/permissions", r.noAuthRequired(), r.userHandler.GetUserPermissionsInOrganization)
			user.POST("/permissions", r.noAuthRequired(), r.userHandler.GrantUserPermission)
			user.DELETE("/permissions", r.noAuthRequired(), r.userHandler.RevokeUserPermission)
		}
	}
}

// setupOrganizationProductRoutes sets up product management within organization
func (r *APIRoutes) setupOrganizationProductRoutes(orgs *gin.RouterGroup) {
	products := orgs.Group("/products")
	{
		// List products (members can read)
		products.GET("/", r.noAuthRequired(), r.productHandler.ListOrganizationProducts)

		// Create product (requires create permission)
		products.POST("/", r.noAuthRequired(), r.productHandler.CreateProduct)

		// Product-specific routes
		product := products.Group("/:product_id")
		{
			// Get product info
			product.GET("/", r.noAuthRequired(), r.productHandler.GetProduct)

			// Update product
			product.PUT("/", r.noAuthRequired(), r.productHandler.UpdateProduct)

			// Delete product
			product.DELETE("/", r.noAuthRequired(), r.productHandler.DeleteProduct)

			// Product policies
			r.setupProductPolicyRoutes(product)

			// Product stats
			product.GET("/stats", r.noAuthRequired(), r.productHandler.GetProductStats)

			// Product licenses
			r.setupProductLicenseRoutes(product)
		}
	}
}

// setupProductLicenseRoutes sets up license management for products
func (r *APIRoutes) setupProductLicenseRoutes(product *gin.RouterGroup) {
	licenses := product.Group("/licenses")
	{
		// List product licenses
		licenses.GET("/", r.noAuthRequired(), r.licenseHandler.ListProductLicenses)

		// Create license
		licenses.POST("/", r.noAuthRequired(), r.licenseHandler.CreateLicense)

		// License-specific routes
		license := licenses.Group("/:license_id")
		{
			// Get license info
			license.GET("/", r.noAuthRequired(), r.licenseHandler.GetLicense)

			// Update license
			license.PUT("/", r.noAuthRequired(), r.licenseHandler.UpdateLicense)

			// Delete/revoke license
			license.DELETE("/", r.noAuthRequired(), r.licenseHandler.DeleteLicense)

			// License statistics
			license.GET("/stats", r.noAuthRequired(), r.licenseHandler.GetLicenseStats)

			// License machines
			r.setupLicenseMachineRoutes(license)
		}
	}
}

// setupLicenseMachineRoutes sets up machine management for licenses
func (r *APIRoutes) setupLicenseMachineRoutes(license *gin.RouterGroup) {
	machines := license.Group("/machines")
	{
		// List license machines
		machines.GET("/", r.noAuthRequired(), r.licenseHandler.GetLicenseMachines)

		// Machine checkout/registration
		machines.POST("/", r.noAuthRequired(), r.machineHandler.CreateMachine)

		// Machine-specific routes
		machine := machines.Group("/:machine_id")
		{
			// Get machine info
			machine.GET("/", r.noAuthRequired(), r.machineHandler.GetMachine)

			// Machine heartbeat
			machine.POST("/heartbeat", r.noAuthRequired(), r.machineHandler.MachineHeartbeat)

			// Remove machine
			machine.DELETE("/", r.noAuthRequired(), r.machineHandler.DeleteMachine)
		}
	}
}

// setupProductPolicyRoutes sets up policy management for products
func (r *APIRoutes) setupProductPolicyRoutes(product *gin.RouterGroup) {
	policies := product.Group("/policies")
	{
		// List product policies
		policies.GET("/", r.noAuthRequired(), r.policyHandler.ListPolicies)

		// Create policy
		policies.POST("/", r.noAuthRequired(), r.policyHandler.CreatePolicy)

		// Policy-specific routes
		policy := policies.Group("/:policy_id")
		{
			// Get policy info
			policy.GET("/", r.noAuthRequired(), r.policyHandler.GetPolicy)

			// Update policy
			policy.PUT("/", r.noAuthRequired(), r.policyHandler.UpdatePolicy)

			// Delete policy
			policy.DELETE("/", r.noAuthRequired(), r.policyHandler.DeletePolicy)
		}
	}
}

// setupUserRoutes sets up user-specific routes
func (r *APIRoutes) setupUserRoutes(api *gin.RouterGroup) {
	users := api.Group("/users")
	{
		// Current user info
		users.GET("/me", r.userHandler.GetCurrentUser)

		// Update current user
		users.PUT("/me", r.userHandler.UpdateCurrentUser)

		// User's organizations
		users.GET("/me/organizations", r.userHandler.GetUserOrganizations)

		// User's permissions
		users.GET("/me/permissions", r.userHandler.GetUserPermissions)
	}

	// Authentication routes (protected)
	auth := api.Group("/auth")
	{
		auth.POST("/logout", r.authHandler.Logout)
		auth.POST("/refresh", r.authHandler.RefreshToken)
	}
}

// setupLicenseRoutes sets up general license routes (across organizations)
func (r *APIRoutes) setupLicenseRoutes(api *gin.RouterGroup) {
	// These routes are for license validation and machine operations
	// They use different authentication (license key based)
	licenses := api.Group("/licenses")
	{
		// License validation (can be called by license key)
		licenses.POST("/:key/validate", func(c *gin.Context) {
			c.JSON(200, gin.H{"message": "validate license"})
		})

		// Machine checkout (can be called by license key)
		licenses.POST("/:key/machines", func(c *gin.Context) {
			c.JSON(200, gin.H{"message": "checkout machine for license"})
		})
	}
}

// setupMachineRoutes sets up general machine routes
func (r *APIRoutes) setupMachineRoutes(api *gin.RouterGroup) {
	// Organization-scoped machine management
	orgMachines := api.Group("/organizations/:organization_id/machines")
	{
		// List organization machines
		orgMachines.GET("/", r.noAuthRequired(), r.machineHandler.ListMachines)

		// Create machine
		orgMachines.POST("/", r.noAuthRequired(), r.machineHandler.CreateMachine)

		// Machine-specific routes
		machine := orgMachines.Group("/:machine_id")
		{
			// Get machine info
			machine.GET("/", r.noAuthRequired(), r.machineHandler.GetMachine)

			// Update machine
			machine.PUT("/", r.noAuthRequired(), r.machineHandler.UpdateMachine)

			// Delete machine
			machine.DELETE("/", r.noAuthRequired(), r.machineHandler.DeleteMachine)

			// Machine heartbeat
			machine.POST("/heartbeat", r.noAuthRequired(), r.machineHandler.MachineHeartbeat)

			// Machine processes
			machine.GET("/processes", r.noAuthRequired(), r.machineHandler.GetMachineProcesses)

			// Machine components
			machine.GET("/components", r.noAuthRequired(), r.machineHandler.GetMachineComponents)
		}
	}

	// Global machine routes (for license key authentication)
	machines := api.Group("/machines")
	{
		// Machine heartbeat (can be called by machine with license key)
		machines.POST("/:machine_id/heartbeat", r.noAuthRequired(), r.machineHandler.MachineHeartbeat)
	}
}
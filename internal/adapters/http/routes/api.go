package routes

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/gokeys/gokeys/internal/adapters/http/handlers"
	"github.com/gokeys/gokeys/internal/adapters/http/middleware"
	"github.com/gokeys/gokeys/internal/domain/services/auth"
)

// API route constants for better maintainability
const (
	// API versions
	APIVersionV1 = "/api/v1"
	PublicAPIV1  = "/api/v1/public"

	// Route groups
	AdminGroup         = "/admin"
	OrganizationsGroup = "/organizations"
	UsersGroup         = "/users"
	ProductsGroup      = "/products"
	LicensesGroup      = "/licenses"
	MachinesGroup      = "/machines"
	PoliciesGroup      = "/policies"
	AuthGroup          = "/auth"
	ActionsGroup       = "/actions"

	// Parameter names
	ParamOrganizationID = "organization_id"
	ParamProductID      = "product_id"
	ParamLicenseID      = "license_id"
	ParamUserID         = "user_id"
	ParamMachineID      = "machine_id"
	ParamPolicyID       = "policy_id"
	ParamKey            = "key"

	// System user ID for bypass authentication
	SystemAdminUserID = "eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee"
)

// HTTP methods for route definitions
const (
	GET    = http.MethodGet
	POST   = http.MethodPost
	PUT    = http.MethodPut
	DELETE = http.MethodDelete
	PATCH  = http.MethodPatch
)

// APIRoutes sets up API routes with clean authorization
type APIRoutes struct {
	authService *auth.AuthService
	authMW      *middleware.AuthenticationMiddleware
	authzMW     *middleware.AuthorizationMiddleware

	// Handlers
	userHandler    *handlers.UserHandler
	orgHandler     *handlers.OrganizationHandler
	productHandler *handlers.ProductHandler
	licenseHandler *handlers.LicenseHandler
	policyHandler  *handlers.PolicyHandler
	machineHandler *handlers.MachineHandler
	authHandler    *handlers.AuthHandler
}

// NewAPIRoutes creates a new API routes handler
func NewAPIRoutes(
	authService *auth.AuthService,
	authMW *middleware.AuthenticationMiddleware,
	authzMW *middleware.AuthorizationMiddleware,
	userHandler *handlers.UserHandler,
	orgHandler *handlers.OrganizationHandler,
	productHandler *handlers.ProductHandler,
	licenseHandler *handlers.LicenseHandler,
	policyHandler *handlers.PolicyHandler,
	machineHandler *handlers.MachineHandler,
	authHandler *handlers.AuthHandler,
) *APIRoutes {
	return &APIRoutes{
		authService:    authService,
		authMW:         authMW,
		authzMW:        authzMW,
		userHandler:    userHandler,
		orgHandler:     orgHandler,
		productHandler: productHandler,
		licenseHandler: licenseHandler,
		policyHandler:  policyHandler,
		machineHandler: machineHandler,
		authHandler:    authHandler,
	}
}

// noAuthRequired is a temporary helper to bypass authorization during business logic testing
func (r *APIRoutes) noAuthRequired() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Set dummy user context for handlers that expect it
		c.Set("user_id", SystemAdminUserID)
		c.Set("auth_type", "bypass")
		c.Next()
	}
}

// SetupRoutes sets up all API routes using Gin's native features
func (r *APIRoutes) SetupRoutes(router *gin.Engine) {
	// Public API routes (no authentication)
	r.setupPublicRoutes(router)

	// Protected API routes (with authentication)
	r.setupProtectedRoutes(router)
}

// setupPublicRoutes sets up public routes (no authentication)
func (r *APIRoutes) setupPublicRoutes(router *gin.Engine) {
	public := router.Group("/api/v1/public")

	// Authentication endpoints
	public.POST("/auth/login", r.authHandler.Login)
	public.POST("/auth/register", r.authHandler.Register)

	// Public license validation endpoints
	public.POST("/licenses/validate", r.licenseHandler.ValidateLicense)
	public.GET("/licenses/:key/info", r.licenseHandler.GetLicenseInfo)
}

// setupProtectedRoutes sets up protected routes (with authentication)
func (r *APIRoutes) setupProtectedRoutes(router *gin.Engine) {
	// API v1 with temporary auth bypass for business logic testing
	api := router.Group("/api/v1")
	api.Use(r.noAuthRequired()) // TODO: Replace with proper auth after business logic is complete

	// System admin routes
	r.setupSystemAdminRoutes(api)

	// Organization routes
	r.setupOrganizationRoutes(api)

	// User routes
	r.setupUserRoutes(api)

	// Global license routes
	r.setupGlobalLicenseRoutes(api)

	// Global machine routes
	r.setupGlobalMachineRoutes(api)
}

// setupSystemAdminRoutes sets up system admin routes
func (r *APIRoutes) setupSystemAdminRoutes(api *gin.RouterGroup) {
	admin := api.Group("/admin")

	// Organization management (system admin only)
	admin.GET("/organizations", r.orgHandler.ListAllOrganizations)
	admin.POST("/organizations", r.orgHandler.CreateOrganization)

	// User management across organizations
	admin.GET("/users", func(c *gin.Context) {
		c.JSON(200, gin.H{"message": "list all users (system admin)"})
	})

	// System-wide statistics and monitoring
	admin.GET("/stats", r.orgHandler.GetSystemStats)
}

// setupOrganizationRoutes sets up organization-scoped routes
func (r *APIRoutes) setupOrganizationRoutes(api *gin.RouterGroup) {
	orgs := api.Group("/organizations/:organization_id")

	// Organization info and management
	orgs.GET("/", r.orgHandler.GetOrganization)
	orgs.PUT("/", r.orgHandler.UpdateOrganization)

	// Organization users
	r.setupOrganizationUserRoutes(orgs)

	// Organization products
	r.setupOrganizationProductRoutes(orgs)
}

// setupOrganizationUserRoutes sets up user management within organization
func (r *APIRoutes) setupOrganizationUserRoutes(orgs *gin.RouterGroup) {
	users := orgs.Group("/users")

	// List and add organization users
	users.GET("/", r.userHandler.ListOrganizationUsers)
	users.POST("/", r.userHandler.AddUserToOrganization)

	// Individual user management
	user := users.Group("/:user_id")
	user.GET("/", r.userHandler.GetOrganizationUser)
	user.DELETE("/", r.userHandler.RemoveUserFromOrganization)

	// User permissions
	user.GET("/permissions", r.userHandler.GetUserPermissionsInOrganization)
	user.POST("/permissions", r.userHandler.GrantUserPermission)
	user.DELETE("/permissions", r.userHandler.RevokeUserPermission)
}

// setupOrganizationProductRoutes sets up product management within organization
func (r *APIRoutes) setupOrganizationProductRoutes(orgs *gin.RouterGroup) {
	products := orgs.Group("/products")

	// List and create products
	products.GET("/", r.productHandler.ListOrganizationProducts)
	products.POST("/", r.productHandler.CreateProduct)

	// Individual product management
	r.setupProductRoutes(products)
}

// setupProductRoutes sets up product-specific routes
func (r *APIRoutes) setupProductRoutes(products *gin.RouterGroup) {
	product := products.Group("/:product_id")

	// Product info and management
	product.GET("/", r.productHandler.GetProduct)
	product.PUT("/", r.productHandler.UpdateProduct)
	product.DELETE("/", r.productHandler.DeleteProduct)
	product.GET("/stats", r.productHandler.GetProductStats)

	// Product policies
	r.setupProductPolicyRoutes(product)

	// Product licenses
	r.setupProductLicenseRoutes(product)
}

// setupProductPolicyRoutes sets up policy management for products
func (r *APIRoutes) setupProductPolicyRoutes(product *gin.RouterGroup) {
	policies := product.Group("/policies")

	// List and create policies
	policies.GET("/", r.policyHandler.ListPolicies)
	policies.POST("/", r.policyHandler.CreatePolicy)

	// Individual policy management
	policy := policies.Group("/:policy_id")
	policy.GET("/", r.policyHandler.GetPolicy)
	policy.PUT("/", r.policyHandler.UpdatePolicy)
	policy.DELETE("/", r.policyHandler.DeletePolicy)
}

// setupProductLicenseRoutes sets up license management for products
func (r *APIRoutes) setupProductLicenseRoutes(product *gin.RouterGroup) {
	licenses := product.Group("/licenses")

	// List and create licenses
	licenses.GET("/", r.licenseHandler.ListProductLicenses)
	licenses.POST("/", r.licenseHandler.CreateLicense)

	// Individual license management
	r.setupLicenseRoutes(licenses)
}

// setupLicenseRoutes sets up license-specific routes
func (r *APIRoutes) setupLicenseRoutes(licenses *gin.RouterGroup) {
	license := licenses.Group("/:license_id")

	// License info and management
	license.GET("/", r.licenseHandler.GetLicense)
	license.PUT("/", r.licenseHandler.UpdateLicense)
	license.DELETE("/", r.licenseHandler.DeleteLicense)
	license.GET("/stats", r.licenseHandler.GetLicenseStats)

	// License actions
	r.setupLicenseActionRoutes(license)

	// License machines
	r.setupLicenseMachineRoutes(license)
}

// setupLicenseActionRoutes sets up license action routes following keygen-api patterns
func (r *APIRoutes) setupLicenseActionRoutes(license *gin.RouterGroup) {
	actions := license.Group("/actions")

	// License validation actions
	actions.GET("/validate", r.licenseHandler.QuickValidateLicenseByID)
	actions.POST("/validate", r.licenseHandler.ValidateLicenseByKey)

	// License permit actions (following keygen-api permits_controller.rb)
	actions.POST("/suspend", r.licenseHandler.SuspendLicense)
	actions.POST("/reinstate", r.licenseHandler.ReinstateLicense)
	actions.DELETE("/revoke", r.licenseHandler.RevokeLicense)
	actions.POST("/renew", r.licenseHandler.RenewLicense)
	actions.POST("/check-in", r.licenseHandler.CheckInLicense)

	// License usage actions (following keygen-api uses_controller.rb)
	actions.POST("/increment-usage", r.licenseHandler.IncrementUsage)
	actions.POST("/decrement-usage", r.licenseHandler.DecrementUsage)
	actions.POST("/reset-usage", r.licenseHandler.ResetUsage)

	// License checkout actions
	actions.POST("/check-out", r.licenseHandler.CheckoutLicense)
	actions.GET("/check-out", r.licenseHandler.CheckoutLicense)
}

// setupLicenseMachineRoutes sets up machine management for licenses
func (r *APIRoutes) setupLicenseMachineRoutes(license *gin.RouterGroup) {
	machines := license.Group("/machines")

	// List license machines and machine checkout/registration
	machines.GET("/", r.licenseHandler.GetLicenseMachines)
	machines.POST("/", r.machineHandler.CreateMachine)

	// Individual machine management
	machine := machines.Group("/:machine_id")
	machine.GET("/", r.machineHandler.GetMachine)
	machine.POST("/heartbeat", r.machineHandler.MachineHeartbeat)
	machine.DELETE("/", r.machineHandler.DeleteMachine)
}

// setupUserRoutes sets up user-specific routes
func (r *APIRoutes) setupUserRoutes(api *gin.RouterGroup) {
	users := api.Group("/users")

	// Current user info
	users.GET("/me", r.userHandler.GetCurrentUser)
	users.PUT("/me", r.userHandler.UpdateCurrentUser)
	users.GET("/me/organizations", r.userHandler.GetUserOrganizations)
	users.GET("/me/permissions", r.userHandler.GetUserPermissions)

	// User auth
	auth := users.Group("/auth")
	auth.POST("/logout", r.authHandler.Logout)
	auth.POST("/refresh", r.authHandler.RefreshToken)
}

// setupGlobalLicenseRoutes sets up general license routes (across organizations)
func (r *APIRoutes) setupGlobalLicenseRoutes(api *gin.RouterGroup) {
	licenses := api.Group("/licenses")

	// Global license validation by key
	actions := licenses.Group("/actions")
	actions.POST("/validate-key", r.licenseHandler.ValidateLicenseByKey)

	// License key-based routes
	keyRoutes := licenses.Group("/:key")
	keyRoutes.POST("/validate", r.licenseHandler.ValidateLicense)
	keyRoutes.POST("/machines", func(c *gin.Context) {
		c.JSON(200, gin.H{"message": "checkout machine for license"})
	})
}

// setupGlobalMachineRoutes sets up general machine routes
func (r *APIRoutes) setupGlobalMachineRoutes(api *gin.RouterGroup) {
	machines := api.Group("/machines")

	// Machine heartbeat (can be called by machine with license key)
	machines.POST("/:machine_id/heartbeat", r.machineHandler.MachineHeartbeat)

	// Organization-scoped machine management
	orgMachines := machines.Group("/organizations/:organization_id/machines")
	orgMachines.GET("/", r.machineHandler.ListMachines)
	orgMachines.POST("/", r.machineHandler.CreateMachine)

	// Individual machine management
	orgMachine := orgMachines.Group("/:machine_id")
	orgMachine.GET("/", r.machineHandler.GetMachine)
	orgMachine.PUT("/", r.machineHandler.UpdateMachine)
	orgMachine.DELETE("/", r.machineHandler.DeleteMachine)
	orgMachine.POST("/heartbeat", r.machineHandler.MachineHeartbeat)
	orgMachine.GET("/processes", r.machineHandler.GetMachineProcesses)
	orgMachine.GET("/components", r.machineHandler.GetMachineComponents)
}

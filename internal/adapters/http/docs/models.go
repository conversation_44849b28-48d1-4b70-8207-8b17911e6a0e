package docs

import (
	"time"

	"github.com/google/uuid"
)

// API Response Models for Swagger Documentation

// ErrorResponse represents an API error response
// @Description Standard error response format
type ErrorResponse struct {
	Error   string                 `json:"error" example:"validation_failed"`
	Message string                 `json:"message" example:"Request validation failed"`
	Details map[string]interface{} `json:"details,omitempty"`
	Code    int                    `json:"code" example:"400"`
} // @name ErrorResponse

// SuccessResponse represents a successful API response
// @Description Standard success response format
type SuccessResponse struct {
	Success bool        `json:"success" example:"true"`
	Message string      `json:"message" example:"Operation completed successfully"`
	Data    interface{} `json:"data,omitempty"`
} // @name SuccessResponse

// PaginationInfo represents pagination information
// @Description Pagination metadata
type PaginationInfo struct {
	Page       int   `json:"page" example:"1"`
	PageSize   int   `json:"page_size" example:"20"`
	Total      int64 `json:"total" example:"150"`
	TotalPages int   `json:"total_pages" example:"8"`
} // @name PaginationInfo

// PaginatedResponse represents a paginated API response
// @Description Standard paginated response format
type PaginatedResponse struct {
	Success    bool           `json:"success" example:"true"`
	Data       interface{}    `json:"data"`
	Pagination PaginationInfo `json:"pagination"`
} // @name PaginatedResponse

// License Models

// LicenseResponse represents a license in API responses
// @Description License information
type LicenseResponse struct {
	ID              uuid.UUID  `json:"id" example:"550e8400-e29b-41d4-a716-************"`
	Key             string     `json:"key" example:"GLK-1234-5678-ABCD-EFGH"`
	OrganizationID  uuid.UUID  `json:"organization_id" example:"550e8400-e29b-41d4-a716-************"`
	ProductID       uuid.UUID  `json:"product_id" example:"550e8400-e29b-41d4-a716-************"`
	PolicyID        *uuid.UUID `json:"policy_id,omitempty" example:"550e8400-e29b-41d4-a716-************"`
	UserID          *uuid.UUID `json:"user_id,omitempty" example:"550e8400-e29b-41d4-a716-************"`
	Name            *string    `json:"name,omitempty" example:"Enterprise License"`
	Status          string     `json:"status" example:"active"`
	Uses            int        `json:"uses" example:"42"`
	Protected       *bool      `json:"protected,omitempty" example:"false"`
	Suspended       *bool      `json:"suspended,omitempty" example:"false"`
	ExpiresAt       *time.Time `json:"expires_at,omitempty" example:"2025-12-31T23:59:59Z"`
	LastValidated   *time.Time `json:"last_validated,omitempty" example:"2024-01-15T10:30:00Z"`
	ValidationCount int        `json:"validation_count" example:"1337"`
	CreatedAt       time.Time  `json:"created_at" example:"2024-01-01T00:00:00Z"`
	UpdatedAt       time.Time  `json:"updated_at" example:"2024-01-15T10:30:00Z"`
} // @name LicenseResponse

// LicenseValidationRequest represents a license validation request
// @Description License validation request payload
type LicenseValidationRequest struct {
	LicenseKey         string            `json:"license_key" binding:"required" example:"GLK-1234-5678-ABCD-EFGH"`
	MachineFingerprint *string           `json:"machine_fingerprint,omitempty" example:"fp-machine-12345"`
	MachineMetadata    map[string]string `json:"machine_metadata,omitempty"`
} // @name LicenseValidationRequest

// LicenseValidationResponse represents a license validation response
// @Description License validation result
type LicenseValidationResponse struct {
	Valid           bool                  `json:"valid" example:"true"`
	License         *LicenseResponse      `json:"license,omitempty"`
	Organization    *OrganizationResponse `json:"organization,omitempty"`
	Policy          *PolicyResponse       `json:"policy,omitempty"`
	MachinesUsed    int                   `json:"machines_used" example:"3"`
	MachinesAllowed int                   `json:"machines_allowed" example:"5"`
	ExpiresAt       *time.Time            `json:"expires_at,omitempty" example:"2025-12-31T23:59:59Z"`
	Errors          []string              `json:"errors,omitempty"`
	TTL             int                   `json:"ttl" example:"3600"`
} // @name LicenseValidationResponse

// Organization Models

// OrganizationResponse represents an organization in API responses
// @Description Organization information
type OrganizationResponse struct {
	ID           uuid.UUID `json:"id" example:"550e8400-e29b-41d4-a716-************"`
	Name         string    `json:"name" example:"Enterprise Corp"`
	Slug         string    `json:"slug" example:"enterprise-corp"`
	Email        *string   `json:"email,omitempty" example:"<EMAIL>"`
	Protected    *bool     `json:"protected,omitempty" example:"false"`
	Suspended    *bool     `json:"suspended,omitempty" example:"false"`
	BillingEmail *string   `json:"billing_email,omitempty" example:"<EMAIL>"`
	CreatedAt    time.Time `json:"created_at" example:"2024-01-01T00:00:00Z"`
	UpdatedAt    time.Time `json:"updated_at" example:"2024-01-15T10:30:00Z"`
} // @name OrganizationResponse

// Policy Models

// PolicyResponse represents a policy in API responses
// @Description Policy configuration
type PolicyResponse struct {
	ID                uuid.UUID  `json:"id" example:"550e8400-e29b-41d4-a716-************"`
	OrganizationID    uuid.UUID  `json:"organization_id" example:"550e8400-e29b-41d4-a716-************"`
	ProductID         *uuid.UUID `json:"product_id,omitempty" example:"550e8400-e29b-41d4-a716-************"`
	Name              string     `json:"name" example:"Standard Policy"`
	Duration          *string    `json:"duration,omitempty" example:"P1Y"`
	Scheme            string     `json:"scheme" example:"RSA-2048"`
	RequireHeartbeat  *bool      `json:"require_heartbeat,omitempty" example:"true"`
	HeartbeatDuration *string    `json:"heartbeat_duration,omitempty" example:"PT1H"`
	MaxMachines       *int       `json:"max_machines,omitempty" example:"5"`
	MaxProcesses      *int       `json:"max_processes,omitempty" example:"10"`
	MaxUsers          *int       `json:"max_users,omitempty" example:"100"`
	MaxUses           *int       `json:"max_uses,omitempty" example:"1000"`
	Protected         *bool      `json:"protected,omitempty" example:"false"`
	Strict            *bool      `json:"strict,omitempty" example:"true"`
	Floating          *bool      `json:"floating,omitempty" example:"false"`
	CreatedAt         time.Time  `json:"created_at" example:"2024-01-01T00:00:00Z"`
	UpdatedAt         time.Time  `json:"updated_at" example:"2024-01-15T10:30:00Z"`
} // @name PolicyResponse

// Machine Models

// MachineResponse represents a machine in API responses
// @Description Machine information
type MachineResponse struct {
	ID             uuid.UUID              `json:"id" example:"550e8400-e29b-41d4-a716-************"`
	OrganizationID uuid.UUID              `json:"organization_id" example:"550e8400-e29b-41d4-a716-************"`
	LicenseID      *uuid.UUID             `json:"license_id,omitempty" example:"550e8400-e29b-41d4-a716-************"`
	Fingerprint    string                 `json:"fingerprint" example:"fp-machine-12345"`
	Name           *string                `json:"name,omitempty" example:"Production Server 01"`
	Hostname       *string                `json:"hostname,omitempty" example:"prod-srv-01"`
	Platform       *string                `json:"platform,omitempty" example:"linux"`
	IP             *string                `json:"ip,omitempty" example:"*************"`
	Metadata       map[string]interface{} `json:"metadata,omitempty"`
	RequireLicense *bool                  `json:"require_license,omitempty" example:"true"`
	LastSeen       *time.Time             `json:"last_seen,omitempty" example:"2024-01-15T10:30:00Z"`
	CreatedAt      time.Time              `json:"created_at" example:"2024-01-01T00:00:00Z"`
	UpdatedAt      time.Time              `json:"updated_at" example:"2024-01-15T10:30:00Z"`
} // @name MachineResponse

// Product Models

// ProductResponse represents a product in API responses
// @Description Product information
type ProductResponse struct {
	ID                   uuid.UUID `json:"id" example:"550e8400-e29b-41d4-a716-************"`
	OrganizationID       uuid.UUID `json:"organization_id" example:"550e8400-e29b-41d4-a716-************"`
	Name                 string    `json:"name" example:"Enterprise Software"`
	URL                  *string   `json:"url,omitempty" example:"https://enterprise.com/software"`
	DistributionStrategy *string   `json:"distribution_strategy,omitempty" example:"licensed"`
	Platforms            []string  `json:"platforms,omitempty" example:"linux,windows,macos"`
	CreatedAt            time.Time `json:"created_at" example:"2024-01-01T00:00:00Z"`
	UpdatedAt            time.Time `json:"updated_at" example:"2024-01-15T10:30:00Z"`
} // @name ProductResponse

// User Models

// UserResponse represents a user in API responses
// @Description User information
type UserResponse struct {
	ID              uuid.UUID  `json:"id" example:"550e8400-e29b-41d4-a716-************"`
	GroupID         *uuid.UUID `json:"group_id,omitempty" example:"550e8400-e29b-41d4-a716-************"`
	Email           string     `json:"email" example:"<EMAIL>"`
	FirstName       *string    `json:"first_name,omitempty" example:"John"`
	LastName        *string    `json:"last_name,omitempty" example:"Doe"`
	Role            string     `json:"role" example:"admin"`
	PasswordHash    string     `json:"-"` // Never expose password hash
	EmailVerified   *bool      `json:"email_verified,omitempty" example:"true"`
	EmailVerifiedAt *time.Time `json:"email_verified_at,omitempty" example:"2024-01-01T12:00:00Z"`
	Banned          *bool      `json:"banned,omitempty" example:"false"`
	BannedAt        *time.Time `json:"banned_at,omitempty"`
	BannedReason    *string    `json:"banned_reason,omitempty"`
	LastLoginAt     *time.Time `json:"last_login_at,omitempty" example:"2024-01-15T10:30:00Z"`
	CreatedAt       time.Time  `json:"created_at" example:"2024-01-01T00:00:00Z"`
	UpdatedAt       time.Time  `json:"updated_at" example:"2024-01-15T10:30:00Z"`
} // @name UserResponse

// Health Models

// HealthResponse represents the health check response
// @Description System health information
type HealthResponse struct {
	Status    string                 `json:"status" example:"healthy"`
	Timestamp time.Time              `json:"timestamp" example:"2024-01-15T10:30:00Z"`
	Version   string                 `json:"version" example:"1.0.0"`
	Services  map[string]interface{} `json:"services"`
	Uptime    string                 `json:"uptime" example:"72h30m15s"`
} // @name HealthResponse

// Authentication Models

// LoginRequest represents a login request
// @Description User login credentials
type LoginRequest struct {
	Email    string  `json:"email" binding:"required,email" example:"<EMAIL>"`
	Password string  `json:"password" binding:"required" example:"secretpassword"`
	GroupID  *string `json:"group_id,omitempty" example:"550e8400-e29b-41d4-a716-************"`
} // @name LoginRequest

// LoginResponse represents a login response
// @Description Login result with JWT token
type LoginResponse struct {
	Success      bool         `json:"success" example:"true"`
	Token        string       `json:"token" example:"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."`
	RefreshToken string       `json:"refresh_token" example:"refresh_token_here"`
	ExpiresAt    time.Time    `json:"expires_at" example:"2024-01-16T10:30:00Z"`
	User         UserResponse `json:"user"`
} // @name LoginResponse

// RegisterRequest represents a user registration request
// @Description User registration details
type RegisterRequest struct {
	Email               string  `json:"email" binding:"required,email" example:"<EMAIL>"`
	Password            string  `json:"password" binding:"required" example:"SecurePassword123!"`
	FirstName           *string `json:"first_name,omitempty" example:"John"`
	LastName            *string `json:"last_name,omitempty" example:"Doe"`
	GroupID             *string `json:"group_id,omitempty" example:"550e8400-e29b-41d4-a716-************"`
	CreatePersonalGroup *bool   `json:"create_personal_group,omitempty" example:"true"`
} // @name RegisterRequest

// ChangePasswordRequest represents a password change request
// @Description Password change details
type ChangePasswordRequest struct {
	CurrentPassword string `json:"current_password" binding:"required" example:"OldPassword123!"`
	NewPassword     string `json:"new_password" binding:"required" example:"NewPassword123!"`
} // @name ChangePasswordRequest

// Security Models

// APIKeyResponse represents an API key
// @Description API key information
type APIKeyResponse struct {
	ID             uuid.UUID  `json:"id" example:"550e8400-e29b-41d4-a716-************"`
	Name           string     `json:"name" example:"Production API Key"`
	Key            string     `json:"key,omitempty" example:"ak_prod_1234567890abcdef"` // Only shown on creation
	KeyHash        string     `json:"-"`                                                // Never expose key hash
	OrganizationID uuid.UUID  `json:"organization_id" example:"550e8400-e29b-41d4-a716-************"`
	UserID         *uuid.UUID `json:"user_id,omitempty" example:"550e8400-e29b-41d4-a716-************"`
	ExpiresAt      *time.Time `json:"expires_at,omitempty" example:"2025-01-15T10:30:00Z"`
	LastUsed       *time.Time `json:"last_used,omitempty" example:"2024-01-15T10:30:00Z"`
	CreatedAt      time.Time  `json:"created_at" example:"2024-01-01T00:00:00Z"`
	UpdatedAt      time.Time  `json:"updated_at" example:"2024-01-15T10:30:00Z"`
} // @name APIKeyResponse

// Metrics Models

// MetricsResponse represents system metrics
// @Description System performance metrics
type MetricsResponse struct {
	Timestamp           time.Time `json:"timestamp" example:"2024-01-15T10:30:00Z"`
	ActiveLicenses      int       `json:"active_licenses" example:"1500"`
	TotalValidations    int64     `json:"total_validations" example:"50000"`
	ValidationRate      float64   `json:"validation_rate" example:"125.5"`
	ErrorRate           float64   `json:"error_rate" example:"0.02"`
	AverageResponseTime float64   `json:"avg_response_time_ms" example:"45.2"`
	CacheHitRate        float64   `json:"cache_hit_rate" example:"0.95"`
} // @name MetricsResponse

package integration

import (
	"time"

	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/google/uuid"
)

// === COMPREHENSIVE BUSINESS LOGIC TESTS ===
// Test toàn bộ business logic integration giữa các entities
// Bao gồm: complex scenarios, edge cases, performance tests

// TestCompleteWorkflow test complete licensing workflow
func (suite *PostgreSQLTestSuite) TestCompleteWorkflow() {
	// === STEP 1: CREATE ORGANIZATION ===
	org := &entities.Organization{
		ID:   uuid.New().String(),
		Name: "Complete Workflow Org",
		Slug: "complete-workflow-org",
	}
	err := suite.db.Create(org).Error
	suite.NoError(err)

	// === STEP 2: CREATE PRODUCT ===
	product := &entities.Product{
		ID:             uuid.New().String(),
		OrganizationID: org.ID,
		Name:           "Complete Workflow Product",
		Code:           "complete-workflow-product",
		Key:            "complete-workflow-product-key",
	}
	err = suite.db.Create(product).Error
	suite.NoError(err)

	// === STEP 3: CREATE POLICY WITH COMPLEX RULES ===
	policy := &entities.Policy{
		ID:                uuid.New().String(),
		OrganizationID:    org.ID,
		ProductID:         product.ID,
		Name:              "Enterprise Policy",
		Floating:          true,
		MaxMachines:       &[]int{10}[0],
		MaxCores:          &[]int{80}[0],
		MaxUsers:          &[]int{50}[0],
		Duration:          &[]int{86400 * 365}[0], // 1 year
		RequireHeartbeat:  true,
		HeartbeatDuration: &[]int{600}[0], // 10 minutes
	}

	// Set advanced strategies
	machineStrategy := "UNIQUE_PER_POLICY"
	componentStrategy := "UNIQUE_PER_MACHINE"
	machineMatching := "MATCH_MOST"
	componentMatching := "MATCH_TWO"
	overageStrategy := "ALLOW_1_25X_OVERAGE"

	policy.MachineUniquenessStrategy = &machineStrategy
	policy.ComponentUniquenessStrategy = &componentStrategy
	policy.MachineMatchingStrategy = &machineMatching
	policy.ComponentMatchingStrategy = &componentMatching
	policy.OverageStrategy = &overageStrategy

	policy.SetDefaults()
	err = suite.db.Create(policy).Error
	suite.NoError(err)

	// === STEP 4: CREATE LICENSES WITH OVERRIDES ===
	licenses := make([]*entities.License, 3)
	for i := 0; i < 3; i++ {
		license := &entities.License{
			ID:             uuid.New().String(),
			OrganizationID: org.ID,
			ProductID:      product.ID,
			PolicyID:       policy.ID,
			Key:            "ENTERPRISE-LICENSE-" + string(rune(i+1)),
			OwnerType:      "organization",
			OwnerID:        org.ID,
			Status:         "active",
		}

		// Different overrides for each license
		if i == 0 {
			// Premium license - more machines
			license.MaxMachinesOverride = &[]int{20}[0]
			license.MaxCoresOverride = &[]int{160}[0]
		} else if i == 1 {
			// Standard license - default limits
			// No overrides
		} else {
			// Limited license - fewer machines
			license.MaxMachinesOverride = &[]int{5}[0]
			license.MaxCoresOverride = &[]int{40}[0]
		}

		err = suite.db.Create(license).Error
		suite.NoError(err)
		licenses[i] = license
	}

	// === STEP 5: CREATE MACHINES FOR EACH LICENSE ===
	machines := make([]*entities.Machine, 0)

	for i, license := range licenses {
		// Create different number of machines per license
		machineCount := []int{5, 3, 2}[i] // Premium, Standard, Limited

		for j := 0; j < machineCount; j++ {
			machine := &entities.Machine{
				ID:          uuid.New().String(),
				LicenseID:   license.ID,
				PolicyID:    policy.ID,
				Fingerprint: "machine-" + string(rune(i+1)) + "-" + string(rune(j+1)),
				Name:        &[]string{"Machine " + string(rune(i+1)) + "-" + string(rune(j+1))}[0],
				Platform:    &[]string{[]string{"windows", "linux", "macos"}[j%3]}[0],
				Cores:       []int{8, 16, 4}[j%3],
				Status:      "active",
				Components: entities.MachineComponents{
					"cpu_id":         "CPU-" + string(rune(i+1)) + "-" + string(rune(j+1)),
					"motherboard_id": "MB-" + string(rune(i+1)) + "-" + string(rune(j+1)),
					"disk_id":        "DISK-" + string(rune(i+1)) + "-" + string(rune(j+1)),
					"mac_address":    "00:11:22:33:44:" + string(rune(55+i*10+j)),
				},
			}

			err = suite.db.Create(machine).Error
			suite.NoError(err)
			machines = append(machines, machine)
		}
	}

	// === STEP 6: VERIFY BUSINESS LOGIC ===

	// Test policy business logic
	suite.True(policy.IsFloating())
	suite.True(policy.MachineUniquePerPolicy())
	suite.True(policy.ComponentUniquePerMachine())
	suite.True(policy.MachineMatchMost())
	suite.True(policy.ComponentMatchTwo())
	suite.True(policy.Allow125xOverage())
	suite.True(policy.RequiresHeartbeat())

	// Test license overrides
	premiumLicense := licenses[0]
	suite.NotNil(premiumLicense.MaxMachinesOverride)
	suite.Equal(20, *premiumLicense.MaxMachinesOverride)

	standardLicense := licenses[1]
	suite.Nil(standardLicense.MaxMachinesOverride) // Uses policy default

	limitedLicense := licenses[2]
	suite.NotNil(limitedLicense.MaxMachinesOverride)
	suite.Equal(5, *limitedLicense.MaxMachinesOverride)

	// Test machine business logic
	for _, machine := range machines {
		suite.True(machine.IsActive())
		suite.NotEmpty(machine.Components.GetFingerprint())
		suite.True(machine.Components.HasComponent("cpu_id"))
		suite.True(machine.Components.HasComponent("mac_address"))
	}

	// === STEP 7: TEST COMPLEX QUERIES ===

	// Count machines per license
	var machineCount int64
	err = suite.db.Model(&entities.Machine{}).Where("license_id = ?", premiumLicense.ID).Count(&machineCount).Error
	suite.NoError(err)
	suite.Equal(int64(5), machineCount)

	// Count total cores per license
	var totalCores int64
	err = suite.db.Model(&entities.Machine{}).Where("license_id = ?", premiumLicense.ID).Select("SUM(cores)").Scan(&totalCores).Error
	suite.NoError(err)
	suite.Greater(totalCores, int64(0))

	// Find machines by platform
	var windowsMachines []entities.Machine
	err = suite.db.Where("platform = ?", "windows").Find(&windowsMachines).Error
	suite.NoError(err)
	suite.Greater(len(windowsMachines), 0)

	// === STEP 8: TEST RELATIONSHIPS ===

	// Load organization with all relationships
	var fullOrg entities.Organization
	err = suite.db.Preload("Products.Policies").Where("id = ?", org.ID).First(&fullOrg).Error
	suite.NoError(err)

	suite.Equal(org.Name, fullOrg.Name)
	suite.GreaterOrEqual(len(fullOrg.Products), 1)
	suite.GreaterOrEqual(len(fullOrg.Products[0].Policies), 1)

	// Load license with all relationships
	var fullLicense entities.License
	err = suite.db.Preload("Organization").Preload("Product").Preload("Policy").Preload("Machines").
		Where("id = ?", premiumLicense.ID).First(&fullLicense).Error
	suite.NoError(err)

	suite.Equal(org.Name, fullLicense.Organization.Name)
	suite.Equal(product.Name, fullLicense.Product.Name)
	suite.Equal(policy.Name, fullLicense.Policy.Name)
	suite.Equal(5, len(fullLicense.Machines))
}

// TestEdgeCases test various edge cases
func (suite *PostgreSQLTestSuite) TestEdgeCases() {
	// === EDGE CASE 1: ZERO LIMITS ===
	zeroLimitPolicy := &entities.Policy{
		ID:             uuid.New().String(),
		OrganizationID: suite.testOrg.ID,
		ProductID:      suite.testProduct.ID,
		Name:           "Zero Limit Policy",
		MaxMachines:    &[]int{0}[0],
		MaxCores:       &[]int{0}[0],
		MaxUsers:       &[]int{0}[0],
	}

	zeroLimitPolicy.SetDefaults()
	validationErrors := zeroLimitPolicy.ValidatePolicy()
	suite.Empty(validationErrors) // Zero limits should be valid

	err := suite.db.Create(zeroLimitPolicy).Error
	suite.NoError(err)

	// === EDGE CASE 2: MAXIMUM LIMITS ===
	maxLimitPolicy := &entities.Policy{
		ID:             uuid.New().String(),
		OrganizationID: suite.testOrg.ID,
		ProductID:      suite.testProduct.ID,
		Name:           "Max Limit Policy",
		MaxMachines:    &[]int{**********}[0], // Max int32
		MaxCores:       &[]int{**********}[0],
		MaxUsers:       &[]int{**********}[0],
		Duration:       &[]int{**********}[0],
	}

	maxLimitPolicy.SetDefaults()
	validationErrors = maxLimitPolicy.ValidatePolicy()
	suite.Empty(validationErrors) // Max limits should be valid

	err = suite.db.Create(maxLimitPolicy).Error
	suite.NoError(err)

	// === EDGE CASE 3: EMPTY COMPONENTS ===
	emptyComponentMachine := &entities.Machine{
		ID:          uuid.New().String(),
		LicenseID:   suite.testLicense.ID,
		PolicyID:    suite.testPolicy.ID,
		Fingerprint: "empty-components-machine",
		Components:  entities.MachineComponents{}, // Empty components
		Status:      "active",
	}

	err = suite.db.Create(emptyComponentMachine).Error
	suite.NoError(err)

	// Test empty components behavior
	suite.False(emptyComponentMachine.Components.HasComponent("cpu_id"))
	suite.Equal("", emptyComponentMachine.Components.GetComponent("cpu_id"))
	suite.Equal("", emptyComponentMachine.Components.GetFingerprint())

	// === EDGE CASE 4: VERY LONG STRINGS ===
	longString := string(make([]byte, 1000)) // 1000 character string
	for i := range longString {
		longString = longString[:i] + "a" + longString[i+1:]
	}

	longStringMachine := &entities.Machine{
		ID:          uuid.New().String(),
		LicenseID:   suite.testLicense.ID,
		PolicyID:    suite.testPolicy.ID,
		Fingerprint: "long-string-machine",
		Name:        &longString,
		Status:      "active",
	}

	err = suite.db.Create(longStringMachine).Error
	// Should succeed or fail based on database constraints
	// In production, you'd have proper length constraints
	suite.NoError(err)

	// === EDGE CASE 5: SPECIAL CHARACTERS ===
	specialChars := "!@#$%^&*()_+-=[]{}|;':\",./<>?`~"
	specialCharMachine := &entities.Machine{
		ID:          uuid.New().String(),
		LicenseID:   suite.testLicense.ID,
		PolicyID:    suite.testPolicy.ID,
		Fingerprint: "special-chars-machine",
		Name:        &specialChars,
		Status:      "active",
		Components: entities.MachineComponents{
			"special_component": specialChars,
		},
	}

	err = suite.db.Create(specialCharMachine).Error
	suite.NoError(err)

	// Verify special characters are preserved
	var retrievedMachine entities.Machine
	err = suite.db.Where("id = ?", specialCharMachine.ID).First(&retrievedMachine).Error
	suite.NoError(err)
	suite.Equal(specialChars, *retrievedMachine.Name)
	suite.Equal(specialChars, retrievedMachine.Components.GetComponent("special_component"))
}

// TestPerformanceScenarios test performance with larger datasets
func (suite *PostgreSQLTestSuite) TestPerformanceScenarios() {
	// Create multiple organizations for performance testing
	orgs := make([]*entities.Organization, 5)
	for i := 0; i < 5; i++ {
		org := &entities.Organization{
			ID:   uuid.New().String(),
			Name: "Perf Org " + string(rune(i+1)),
			Slug: "perf-org-" + string(rune(i+1)),
		}
		err := suite.db.Create(org).Error
		suite.NoError(err)
		orgs[i] = org
	}

	// Create multiple products per organization
	products := make([]*entities.Product, 0)
	for _, org := range orgs {
		for j := 0; j < 3; j++ {
			product := &entities.Product{
				ID:             uuid.New().String(),
				OrganizationID: org.ID,
				Name:           "Perf Product " + string(rune(j+1)),
				Code:           "perf-product-" + string(rune(j+1)),
				Key:            "perf-product-key-" + string(rune(j+1)),
			}
			err := suite.db.Create(product).Error
			suite.NoError(err)
			products = append(products, product)
		}
	}

	// Create policies for products
	policies := make([]*entities.Policy, 0)
	for _, product := range products {
		policy := &entities.Policy{
			ID:             uuid.New().String(),
			OrganizationID: product.OrganizationID,
			ProductID:      product.ID,
			Name:           "Perf Policy for " + product.Name,
			Floating:       true,
			MaxMachines:    &[]int{100}[0],
		}
		policy.SetDefaults()
		err := suite.db.Create(policy).Error
		suite.NoError(err)
		policies = append(policies, policy)
	}

	// Create licenses for policies
	licenses := make([]*entities.License, 0)
	for _, policy := range policies {
		for k := 0; k < 2; k++ {
			license := &entities.License{
				ID:             uuid.New().String(),
				OrganizationID: policy.OrganizationID,
				ProductID:      policy.ProductID,
				PolicyID:       policy.ID,
				Key:            "PERF-LICENSE-" + policy.ID[:8] + "-" + string(rune(k+1)),
				OwnerType:      "organization",
				OwnerID:        policy.OrganizationID,
				Status:         "active",
			}
			err := suite.db.Create(license).Error
			suite.NoError(err)
			licenses = append(licenses, license)
		}
	}

	// Test bulk queries
	start := time.Now()

	// Query all organizations with products and policies
	var allOrgs []entities.Organization
	err := suite.db.Preload("Products.Policies").Find(&allOrgs).Error
	suite.NoError(err)

	queryTime := time.Since(start)
	suite.Less(queryTime, 5*time.Second, "Query should complete within 5 seconds")

	// Verify data integrity
	suite.GreaterOrEqual(len(allOrgs), 5) // At least our test orgs

	totalProducts := 0
	totalPolicies := 0
	for _, org := range allOrgs {
		totalProducts += len(org.Products)
		for _, product := range org.Products {
			totalPolicies += len(product.Policies)
		}
	}

	suite.GreaterOrEqual(totalProducts, 15) // 5 orgs * 3 products
	suite.GreaterOrEqual(totalPolicies, 15) // 15 products * 1 policy

	// Test license counting
	var licenseCount int64
	err = suite.db.Model(&entities.License{}).Count(&licenseCount).Error
	suite.NoError(err)
	suite.GreaterOrEqual(licenseCount, int64(30)) // 15 policies * 2 licenses
}

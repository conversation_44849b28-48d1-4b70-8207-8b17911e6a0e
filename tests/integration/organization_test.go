package integration

import (
	"net/http"

	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/google/uuid"
)

// === ORGANIZATION API TESTS ===
// Test toàn bộ CRUD operations cho Organization API
// Bao gồm: List, Create, Get, Update, Delete

// TestOrganizationList test GET /api/v1/organizations
func (suite *APITestSuite) TestOrganizationList() {
	// Test list organizations
	w := suite.makeRequest("GET", "/api/v1/organizations", nil)
	
	suite.Equal(http.StatusOK, w.Code)
	
	var response struct {
		Organizations []entities.Organization `json:"organizations"`
		Pagination    struct {
			Page       int `json:"page"`
			PageSize   int `json:"page_size"`
			Total      int `json:"total"`
			TotalPages int `json:"total_pages"`
		} `json:"pagination"`
	}
	suite.parseResponse(w, &response)
	
	// Should have at least our test organization
	suite.GreaterOrEqual(len(response.Organizations), 1)
	suite.Equal(1, response.Pagination.Page)
	suite.Equal(20, response.Pagination.PageSize)
	suite.GreaterOrEqual(response.Pagination.Total, 1)
}

// TestOrganizationListWithPagination test pagination
func (suite *APITestSuite) TestOrganizationListWithPagination() {
	// Create additional organizations for pagination test
	for i := 0; i < 25; i++ {
		org := &entities.Organization{
			ID:   uuid.New().String(),
			Name: "Test Org " + string(rune(i)),
			Slug: "test-org-" + string(rune(i)),
		}
		suite.db.Create(org)
	}
	
	// Test first page
	w := suite.makeRequest("GET", "/api/v1/organizations?page=1&page_size=10", nil)
	suite.Equal(http.StatusOK, w.Code)
	
	var response struct {
		Organizations []entities.Organization `json:"organizations"`
		Pagination    struct {
			Page       int `json:"page"`
			PageSize   int `json:"page_size"`
			Total      int `json:"total"`
			TotalPages int `json:"total_pages"`
		} `json:"pagination"`
	}
	suite.parseResponse(w, &response)
	
	suite.Equal(10, len(response.Organizations))
	suite.Equal(1, response.Pagination.Page)
	suite.Equal(10, response.Pagination.PageSize)
	suite.GreaterOrEqual(response.Pagination.Total, 26) // 1 original + 25 new
	suite.GreaterOrEqual(response.Pagination.TotalPages, 3)
	
	// Test second page
	w = suite.makeRequest("GET", "/api/v1/organizations?page=2&page_size=10", nil)
	suite.Equal(http.StatusOK, w.Code)
	suite.parseResponse(w, &response)
	
	suite.Equal(10, len(response.Organizations))
	suite.Equal(2, response.Pagination.Page)
}

// TestOrganizationCreate test POST /api/v1/organizations
func (suite *APITestSuite) TestOrganizationCreate() {
	requestData := map[string]interface{}{
		"name": "New Test Organization",
		"slug": "new-test-org",
		"metadata": map[string]interface{}{
			"description": "A test organization",
			"location":    "Vietnam",
		},
	}
	
	w := suite.makeRequest("POST", "/api/v1/organizations", requestData)
	suite.Equal(http.StatusCreated, w.Code)
	
	var response struct {
		Organization entities.Organization `json:"organization"`
	}
	suite.parseResponse(w, &response)
	
	// Verify response
	suite.Equal("New Test Organization", response.Organization.Name)
	suite.Equal("new-test-org", response.Organization.Slug)
	suite.NotEmpty(response.Organization.ID)
	suite.NotZero(response.Organization.CreatedAt)
	
	// Verify in database
	var dbOrg entities.Organization
	err := suite.db.Where("id = ?", response.Organization.ID).First(&dbOrg).Error
	suite.NoError(err)
	suite.Equal("New Test Organization", dbOrg.Name)
	suite.Equal("new-test-org", dbOrg.Slug)
}

// TestOrganizationCreateValidation test validation errors
func (suite *APITestSuite) TestOrganizationCreateValidation() {
	// Test missing required fields
	requestData := map[string]interface{}{
		"slug": "test-slug",
		// Missing name
	}
	
	w := suite.makeRequest("POST", "/api/v1/organizations", requestData)
	suite.Equal(http.StatusBadRequest, w.Code)
	
	var response struct {
		Error  string `json:"error"`
		Reason string `json:"reason"`
	}
	suite.parseResponse(w, &response)
	suite.Equal("invalid request", response.Error)
	suite.Contains(response.Reason, "name")
}

// TestOrganizationGet test GET /api/v1/organizations/:id
func (suite *APITestSuite) TestOrganizationGet() {
	w := suite.makeRequest("GET", "/api/v1/organizations/"+suite.testOrg.ID, nil)
	suite.Equal(http.StatusOK, w.Code)
	
	var response struct {
		Organization entities.Organization `json:"organization"`
	}
	suite.parseResponse(w, &response)
	
	suite.Equal(suite.testOrg.ID, response.Organization.ID)
	suite.Equal(suite.testOrg.Name, response.Organization.Name)
	suite.Equal(suite.testOrg.Slug, response.Organization.Slug)
}

// TestOrganizationGetNotFound test 404 error
func (suite *APITestSuite) TestOrganizationGetNotFound() {
	nonExistentID := uuid.New().String()
	w := suite.makeRequest("GET", "/api/v1/organizations/"+nonExistentID, nil)
	suite.Equal(http.StatusNotFound, w.Code)
	
	var response struct {
		Error string `json:"error"`
	}
	suite.parseResponse(w, &response)
	suite.Equal("organization not found", response.Error)
}

// TestOrganizationGetInvalidID test invalid UUID
func (suite *APITestSuite) TestOrganizationGetInvalidID() {
	w := suite.makeRequest("GET", "/api/v1/organizations/invalid-uuid", nil)
	suite.Equal(http.StatusBadRequest, w.Code)
	
	var response struct {
		Error string `json:"error"`
	}
	suite.parseResponse(w, &response)
	suite.Equal("invalid organization ID", response.Error)
}

// TestOrganizationUpdate test PUT /api/v1/organizations/:id
func (suite *APITestSuite) TestOrganizationUpdate() {
	requestData := map[string]interface{}{
		"name": "Updated Organization Name",
		"slug": "updated-org-slug",
		"metadata": map[string]interface{}{
			"updated": true,
			"version": 2,
		},
	}
	
	w := suite.makeRequest("PUT", "/api/v1/organizations/"+suite.testOrg.ID, requestData)
	suite.Equal(http.StatusOK, w.Code)
	
	var response struct {
		Organization entities.Organization `json:"organization"`
	}
	suite.parseResponse(w, &response)
	
	// Verify response
	suite.Equal(suite.testOrg.ID, response.Organization.ID)
	suite.Equal("Updated Organization Name", response.Organization.Name)
	suite.Equal("updated-org-slug", response.Organization.Slug)
	
	// Verify in database
	var dbOrg entities.Organization
	err := suite.db.Where("id = ?", suite.testOrg.ID).First(&dbOrg).Error
	suite.NoError(err)
	suite.Equal("Updated Organization Name", dbOrg.Name)
	suite.Equal("updated-org-slug", dbOrg.Slug)
}

// TestOrganizationUpdatePartial test partial update
func (suite *APITestSuite) TestOrganizationUpdatePartial() {
	originalName := suite.testOrg.Name
	
	requestData := map[string]interface{}{
		"slug": "new-slug-only",
		// Name không được update
	}
	
	w := suite.makeRequest("PUT", "/api/v1/organizations/"+suite.testOrg.ID, requestData)
	suite.Equal(http.StatusOK, w.Code)
	
	var response struct {
		Organization entities.Organization `json:"organization"`
	}
	suite.parseResponse(w, &response)
	
	// Name should remain unchanged, slug should be updated
	suite.Equal(originalName, response.Organization.Name)
	suite.Equal("new-slug-only", response.Organization.Slug)
}

// TestOrganizationUpdateNotFound test update non-existent organization
func (suite *APITestSuite) TestOrganizationUpdateNotFound() {
	nonExistentID := uuid.New().String()
	requestData := map[string]interface{}{
		"name": "Updated Name",
	}
	
	w := suite.makeRequest("PUT", "/api/v1/organizations/"+nonExistentID, requestData)
	suite.Equal(http.StatusNotFound, w.Code)
	
	var response struct {
		Error string `json:"error"`
	}
	suite.parseResponse(w, &response)
	suite.Equal("organization not found", response.Error)
}

// TestOrganizationDelete test DELETE /api/v1/organizations/:id
func (suite *APITestSuite) TestOrganizationDelete() {
	// Create a separate organization for deletion test
	orgToDelete := &entities.Organization{
		ID:   uuid.New().String(),
		Name: "Organization to Delete",
		Slug: "org-to-delete",
	}
	err := suite.db.Create(orgToDelete).Error
	suite.NoError(err)
	
	w := suite.makeRequest("DELETE", "/api/v1/organizations/"+orgToDelete.ID, nil)
	suite.Equal(http.StatusNoContent, w.Code)
	
	// Verify organization is deleted (soft delete)
	var dbOrg entities.Organization
	err = suite.db.Where("id = ?", orgToDelete.ID).First(&dbOrg).Error
	suite.Error(err) // Should not find the organization
	
	// Verify with Unscoped (should find soft deleted record)
	err = suite.db.Unscoped().Where("id = ?", orgToDelete.ID).First(&dbOrg).Error
	suite.NoError(err)
	suite.NotNil(dbOrg.DeletedAt)
}

// TestOrganizationDeleteNotFound test delete non-existent organization
func (suite *APITestSuite) TestOrganizationDeleteNotFound() {
	nonExistentID := uuid.New().String()
	w := suite.makeRequest("DELETE", "/api/v1/organizations/"+nonExistentID, nil)
	suite.Equal(http.StatusNotFound, w.Code)
	
	var response struct {
		Error string `json:"error"`
	}
	suite.parseResponse(w, &response)
	suite.Equal("organization not found", response.Error)
}

package integration

import (
	"bytes"
	"encoding/json"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"

	"github.com/gokeys/gokeys/internal/adapters/http/handlers"
	"github.com/gokeys/gokeys/internal/adapters/repositories/gorm_repo"
	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/gokeys/gokeys/internal/domain/services/crypto"
	"github.com/gokeys/gokeys/internal/domain/services/license"
)

// APITestSuite là test suite chính cho toàn bộ API integration tests
// Test toàn bộ API endpoints mà không cần authentication/authorization
// Sử dụng in-memory SQLite database để test isolation
type APITestSuite struct {
	suite.Suite
	db     *gorm.DB
	router *gin.Engine

	// Test data
	testOrg     *entities.Organization
	testProduct *entities.Product
	testPolicy  *entities.Policy
	testLicense *entities.License
	testMachine *entities.Machine
}

// SetupSuite khởi tạo test suite với database và router
func (suite *APITestSuite) SetupSuite() {
	// Tắt Gin debug mode cho clean test output
	gin.SetMode(gin.TestMode)

	// Setup in-memory SQLite database
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	require.NoError(suite.T(), err)
	suite.db = db

	// Auto migrate all entities
	err = db.AutoMigrate(
		&entities.Organization{},
		&entities.Product{},
		&entities.Policy{},
		&entities.License{},
		&entities.Machine{},
		&entities.User{},
		&entities.Session{},
		&entities.APIToken{},
		&entities.Permission{},
		&entities.UsersOrganization{},
	)
	require.NoError(suite.T(), err)

	// Setup repositories
	orgRepo := gorm_repo.NewOrganizationRepository(db)
	productRepo := gorm_repo.NewProductRepository(db)
	policyRepo := gorm_repo.NewPolicyRepository(db)
	licenseRepo := gorm_repo.NewLicenseRepository(db)
	machineRepo := gorm_repo.NewMachineRepository(db)
	userRepo := gorm_repo.NewUserRepository(db)

	// Setup services
	cryptoService := crypto.NewCryptoService()
	validationService := license.NewValidationService(licenseRepo, policyRepo, machineRepo, orgRepo, cryptoService)
	checkoutService := license.NewCheckoutService(licenseRepo, orgRepo, policyRepo, cryptoService)
	lookupService := license.NewLookupService(licenseRepo, orgRepo)

	// Setup handlers
	orgHandler := handlers.NewOrganizationHandler(orgRepo)
	productHandler := handlers.NewProductHandler(productRepo, orgRepo)
	policyHandler := handlers.NewPolicyHandler(policyRepo, productRepo)
	licenseHandler := handlers.NewLicenseHandler(licenseRepo, validationService, checkoutService, lookupService)
	machineHandler := handlers.NewMachineHandler(machineRepo, licenseRepo, policyRepo, userRepo)

	// Setup router
	router := gin.New()

	// API v1 routes
	v1 := router.Group("/api/v1")
	{
		// Organization routes
		orgs := v1.Group("/organizations")
		{
			orgs.GET("", orgHandler.ListOrganizations)
			orgs.POST("", orgHandler.CreateOrganization)
			orgs.GET("/:organization_id", orgHandler.GetOrganization)
			orgs.PUT("/:organization_id", orgHandler.UpdateOrganization)
			orgs.DELETE("/:organization_id", orgHandler.DeleteOrganization)

			// Product routes
			orgs.GET("/:organization_id/products", productHandler.ListProducts)
			orgs.POST("/:organization_id/products", productHandler.CreateProduct)
			orgs.GET("/:organization_id/products/:product_id", productHandler.GetProduct)
			orgs.PUT("/:organization_id/products/:product_id", productHandler.UpdateProduct)
			orgs.DELETE("/:organization_id/products/:product_id", productHandler.DeleteProduct)

			// Policy routes
			orgs.GET("/:organization_id/products/:product_id/policies", policyHandler.ListPolicies)
			orgs.POST("/:organization_id/products/:product_id/policies", policyHandler.CreatePolicy)
			orgs.GET("/:organization_id/products/:product_id/policies/:policy_id", policyHandler.GetPolicy)
			orgs.PUT("/:organization_id/policies/:policy_id", policyHandler.UpdatePolicy)
			orgs.DELETE("/:organization_id/policies/:policy_id", policyHandler.DeletePolicy)

			// License routes
			orgs.GET("/:organization_id/products/:product_id/licenses", licenseHandler.ListLicenses)
			orgs.POST("/:organization_id/products/:product_id/licenses", licenseHandler.CreateLicense)
			orgs.GET("/:organization_id/licenses/:license_id", licenseHandler.GetLicense)
			orgs.PUT("/:organization_id/licenses/:license_id", licenseHandler.UpdateLicense)
			orgs.DELETE("/:organization_id/licenses/:license_id", licenseHandler.DeleteLicense)

			// License operations
			orgs.POST("/:organization_id/licenses/:license_id/actions/validate", licenseHandler.ValidateLicense)
			orgs.POST("/:organization_id/licenses/:license_id/actions/checkout", licenseHandler.CheckoutLicense)
			orgs.POST("/:organization_id/licenses/:license_id/actions/suspend", licenseHandler.SuspendLicense)
			orgs.POST("/:organization_id/licenses/:license_id/actions/reinstate", licenseHandler.ReinstateLicense)
			orgs.POST("/:organization_id/licenses/:license_id/actions/renew", licenseHandler.RenewLicense)
			orgs.POST("/:organization_id/licenses/:license_id/actions/increment-usage", licenseHandler.IncrementUsage)
			orgs.POST("/:organization_id/licenses/:license_id/actions/decrement-usage", licenseHandler.DecrementUsage)

			// Machine routes
			orgs.GET("/:organization_id/machines", machineHandler.ListMachines)
			orgs.POST("/:organization_id/machines", machineHandler.CreateMachine)
			orgs.GET("/:organization_id/machines/:machine_id", machineHandler.GetMachine)
			orgs.PUT("/:organization_id/machines/:machine_id", machineHandler.UpdateMachine)
			orgs.DELETE("/:organization_id/machines/:machine_id", machineHandler.DeleteMachine)

			// Machine operations
			orgs.POST("/:organization_id/machines/:machine_id/actions/heartbeat", machineHandler.MachineHeartbeat)
			orgs.POST("/:organization_id/machines/:machine_id/actions/activate", machineHandler.ActivateMachine)
			orgs.POST("/:organization_id/machines/:machine_id/actions/deactivate", machineHandler.DeactivateMachine)
		}
	}

	suite.router = router
}

// SetupTest tạo test data mới cho mỗi test case
func (suite *APITestSuite) SetupTest() {
	// Clean database
	suite.db.Exec("DELETE FROM machines")
	suite.db.Exec("DELETE FROM licenses")
	suite.db.Exec("DELETE FROM policies")
	suite.db.Exec("DELETE FROM products")
	suite.db.Exec("DELETE FROM organizations")

	// Create test organization
	suite.testOrg = &entities.Organization{
		ID:   uuid.New().String(),
		Name: "Test Organization",
		Slug: "test-org",
	}
	err := suite.db.Create(suite.testOrg).Error
	require.NoError(suite.T(), err)

	// Create test product
	suite.testProduct = &entities.Product{
		ID:             uuid.New().String(),
		OrganizationID: suite.testOrg.ID,
		Name:           "Test Product",
		Slug:           "test-product",
	}
	err = suite.db.Create(suite.testProduct).Error
	require.NoError(suite.T(), err)

	// Create test policy
	suite.testPolicy = &entities.Policy{
		ID:             uuid.New().String(),
		OrganizationID: suite.testOrg.ID,
		ProductID:      suite.testProduct.ID,
		Name:           "Test Policy",
		Floating:       true,
		MaxMachines:    &[]int{5}[0],
		Duration:       &[]int{86400 * 30}[0], // 30 days
	}
	// Set defaults
	suite.testPolicy.SetDefaults()
	err = suite.db.Create(suite.testPolicy).Error
	require.NoError(suite.T(), err)

	// Create test license
	suite.testLicense = &entities.License{
		ID:             uuid.New().String(),
		OrganizationID: suite.testOrg.ID,
		ProductID:      suite.testProduct.ID,
		PolicyID:       suite.testPolicy.ID,
		Key:            "TEST-LICENSE-KEY-" + uuid.New().String()[:8],
		OwnerType:      "organization",
		OwnerID:        suite.testOrg.ID,
		Status:         "active",
	}
	err = suite.db.Create(suite.testLicense).Error
	require.NoError(suite.T(), err)

	// Create test machine
	suite.testMachine = &entities.Machine{
		ID:          uuid.New().String(),
		LicenseID:   suite.testLicense.ID,
		PolicyID:    suite.testPolicy.ID,
		Fingerprint: "test-fingerprint-" + uuid.New().String()[:8],
		Name:        &[]string{"Test Machine"}[0],
		Platform:    &[]string{"linux"}[0],
		Cores:       4,
		Status:      "active",
	}
	err = suite.db.Create(suite.testMachine).Error
	require.NoError(suite.T(), err)
}

// TearDownSuite cleanup sau khi chạy xong tất cả tests
func (suite *APITestSuite) TearDownSuite() {
	if suite.db != nil {
		sqlDB, _ := suite.db.DB()
		sqlDB.Close()
	}
}

// Helper methods

// makeRequest tạo HTTP request và trả về response
func (suite *APITestSuite) makeRequest(method, path string, body interface{}) *httptest.ResponseRecorder {
	var reqBody []byte
	if body != nil {
		var err error
		reqBody, err = json.Marshal(body)
		require.NoError(suite.T(), err)
	}

	req := httptest.NewRequest(method, path, bytes.NewBuffer(reqBody))
	req.Header.Set("Content-Type", "application/json")

	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	return w
}

// parseResponse parse JSON response thành struct
func (suite *APITestSuite) parseResponse(w *httptest.ResponseRecorder, target interface{}) {
	err := json.Unmarshal(w.Body.Bytes(), target)
	require.NoError(suite.T(), err)
}

// TestAPITestSuite chạy test suite
func TestAPITestSuite(t *testing.T) {
	suite.Run(t, new(APITestSuite))
}

package integration

import (
	"net/http"

	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/google/uuid"
)

// === PRODUCT API TESTS ===
// Test toàn bộ CRUD operations cho Product API
// Products thuộc về Organizations, test nested resource patterns

// TestProductList test GET /api/v1/organizations/:org_id/products
func (suite *APITestSuite) TestProductList() {
	path := "/api/v1/organizations/" + suite.testOrg.ID + "/products"
	w := suite.makeRequest("GET", path, nil)
	
	suite.Equal(http.StatusOK, w.Code)
	
	var response struct {
		Products   []entities.Product `json:"products"`
		Pagination struct {
			Page       int `json:"page"`
			PageSize   int `json:"page_size"`
			Total      int `json:"total"`
			TotalPages int `json:"total_pages"`
		} `json:"pagination"`
	}
	suite.parseResponse(w, &response)
	
	// Should have at least our test product
	suite.GreaterOrEqual(len(response.Products), 1)
	suite.Equal(1, response.Pagination.Page)
	suite.Equal(20, response.Pagination.PageSize)
	suite.GreaterOrEqual(response.Pagination.Total, 1)
	
	// Verify all products belong to the organization
	for _, product := range response.Products {
		suite.Equal(suite.testOrg.ID, product.OrganizationID)
	}
}

// TestProductListWithPagination test pagination
func (suite *APITestSuite) TestProductListWithPagination() {
	// Create additional products for pagination test
	for i := 0; i < 25; i++ {
		product := &entities.Product{
			ID:             uuid.New().String(),
			OrganizationID: suite.testOrg.ID,
			Name:           "Test Product " + string(rune(i)),
			Slug:           "test-product-" + string(rune(i)),
		}
		suite.db.Create(product)
	}
	
	// Test first page
	path := "/api/v1/organizations/" + suite.testOrg.ID + "/products?page=1&page_size=10"
	w := suite.makeRequest("GET", path, nil)
	suite.Equal(http.StatusOK, w.Code)
	
	var response struct {
		Products   []entities.Product `json:"products"`
		Pagination struct {
			Page       int `json:"page"`
			PageSize   int `json:"page_size"`
			Total      int `json:"total"`
			TotalPages int `json:"total_pages"`
		} `json:"pagination"`
	}
	suite.parseResponse(w, &response)
	
	suite.Equal(10, len(response.Products))
	suite.Equal(1, response.Pagination.Page)
	suite.Equal(10, response.Pagination.PageSize)
	suite.GreaterOrEqual(response.Pagination.Total, 26) // 1 original + 25 new
}

// TestProductCreate test POST /api/v1/organizations/:org_id/products
func (suite *APITestSuite) TestProductCreate() {
	requestData := map[string]interface{}{
		"name": "New Test Product",
		"slug": "new-test-product",
		"metadata": map[string]interface{}{
			"description": "A test product",
			"category":    "Software",
		},
	}
	
	path := "/api/v1/organizations/" + suite.testOrg.ID + "/products"
	w := suite.makeRequest("POST", path, requestData)
	suite.Equal(http.StatusCreated, w.Code)
	
	var response struct {
		Product entities.Product `json:"product"`
	}
	suite.parseResponse(w, &response)
	
	// Verify response
	suite.Equal("New Test Product", response.Product.Name)
	suite.Equal("new-test-product", response.Product.Slug)
	suite.Equal(suite.testOrg.ID, response.Product.OrganizationID)
	suite.NotEmpty(response.Product.ID)
	suite.NotZero(response.Product.CreatedAt)
	
	// Verify in database
	var dbProduct entities.Product
	err := suite.db.Where("id = ?", response.Product.ID).First(&dbProduct).Error
	suite.NoError(err)
	suite.Equal("New Test Product", dbProduct.Name)
	suite.Equal(suite.testOrg.ID, dbProduct.OrganizationID)
}

// TestProductCreateValidation test validation errors
func (suite *APITestSuite) TestProductCreateValidation() {
	// Test missing required fields
	requestData := map[string]interface{}{
		"slug": "test-slug",
		// Missing name
	}
	
	path := "/api/v1/organizations/" + suite.testOrg.ID + "/products"
	w := suite.makeRequest("POST", path, requestData)
	suite.Equal(http.StatusBadRequest, w.Code)
	
	var response struct {
		Error  string `json:"error"`
		Reason string `json:"reason"`
	}
	suite.parseResponse(w, &response)
	suite.Equal("invalid request", response.Error)
	suite.Contains(response.Reason, "name")
}

// TestProductCreateInvalidOrganization test với invalid organization ID
func (suite *APITestSuite) TestProductCreateInvalidOrganization() {
	requestData := map[string]interface{}{
		"name": "Test Product",
		"slug": "test-product",
	}
	
	invalidOrgID := uuid.New().String()
	path := "/api/v1/organizations/" + invalidOrgID + "/products"
	w := suite.makeRequest("POST", path, requestData)
	suite.Equal(http.StatusNotFound, w.Code)
	
	var response struct {
		Error string `json:"error"`
	}
	suite.parseResponse(w, &response)
	suite.Equal("organization not found", response.Error)
}

// TestProductGet test GET /api/v1/organizations/:org_id/products/:product_id
func (suite *APITestSuite) TestProductGet() {
	path := "/api/v1/organizations/" + suite.testOrg.ID + "/products/" + suite.testProduct.ID
	w := suite.makeRequest("GET", path, nil)
	suite.Equal(http.StatusOK, w.Code)
	
	var response struct {
		Product entities.Product `json:"product"`
	}
	suite.parseResponse(w, &response)
	
	suite.Equal(suite.testProduct.ID, response.Product.ID)
	suite.Equal(suite.testProduct.Name, response.Product.Name)
	suite.Equal(suite.testProduct.Slug, response.Product.Slug)
	suite.Equal(suite.testOrg.ID, response.Product.OrganizationID)
}

// TestProductGetNotFound test 404 error
func (suite *APITestSuite) TestProductGetNotFound() {
	nonExistentID := uuid.New().String()
	path := "/api/v1/organizations/" + suite.testOrg.ID + "/products/" + nonExistentID
	w := suite.makeRequest("GET", path, nil)
	suite.Equal(http.StatusNotFound, w.Code)
	
	var response struct {
		Error string `json:"error"`
	}
	suite.parseResponse(w, &response)
	suite.Equal("product not found", response.Error)
}

// TestProductGetWrongOrganization test access product from wrong organization
func (suite *APITestSuite) TestProductGetWrongOrganization() {
	// Create another organization
	otherOrg := &entities.Organization{
		ID:   uuid.New().String(),
		Name: "Other Organization",
		Slug: "other-org",
	}
	suite.db.Create(otherOrg)
	
	// Try to access our test product from other organization
	path := "/api/v1/organizations/" + otherOrg.ID + "/products/" + suite.testProduct.ID
	w := suite.makeRequest("GET", path, nil)
	suite.Equal(http.StatusNotFound, w.Code)
	
	var response struct {
		Error string `json:"error"`
	}
	suite.parseResponse(w, &response)
	suite.Equal("product not found", response.Error)
}

// TestProductUpdate test PUT /api/v1/organizations/:org_id/products/:product_id
func (suite *APITestSuite) TestProductUpdate() {
	requestData := map[string]interface{}{
		"name": "Updated Product Name",
		"slug": "updated-product-slug",
		"metadata": map[string]interface{}{
			"updated": true,
			"version": 2,
		},
	}
	
	path := "/api/v1/organizations/" + suite.testOrg.ID + "/products/" + suite.testProduct.ID
	w := suite.makeRequest("PUT", path, requestData)
	suite.Equal(http.StatusOK, w.Code)
	
	var response struct {
		Product entities.Product `json:"product"`
	}
	suite.parseResponse(w, &response)
	
	// Verify response
	suite.Equal(suite.testProduct.ID, response.Product.ID)
	suite.Equal("Updated Product Name", response.Product.Name)
	suite.Equal("updated-product-slug", response.Product.Slug)
	suite.Equal(suite.testOrg.ID, response.Product.OrganizationID)
	
	// Verify in database
	var dbProduct entities.Product
	err := suite.db.Where("id = ?", suite.testProduct.ID).First(&dbProduct).Error
	suite.NoError(err)
	suite.Equal("Updated Product Name", dbProduct.Name)
	suite.Equal("updated-product-slug", dbProduct.Slug)
}

// TestProductUpdatePartial test partial update
func (suite *APITestSuite) TestProductUpdatePartial() {
	originalName := suite.testProduct.Name
	
	requestData := map[string]interface{}{
		"slug": "new-slug-only",
		// Name không được update
	}
	
	path := "/api/v1/organizations/" + suite.testOrg.ID + "/products/" + suite.testProduct.ID
	w := suite.makeRequest("PUT", path, requestData)
	suite.Equal(http.StatusOK, w.Code)
	
	var response struct {
		Product entities.Product `json:"product"`
	}
	suite.parseResponse(w, &response)
	
	// Name should remain unchanged, slug should be updated
	suite.Equal(originalName, response.Product.Name)
	suite.Equal("new-slug-only", response.Product.Slug)
}

// TestProductDelete test DELETE /api/v1/organizations/:org_id/products/:product_id
func (suite *APITestSuite) TestProductDelete() {
	// Create a separate product for deletion test
	productToDelete := &entities.Product{
		ID:             uuid.New().String(),
		OrganizationID: suite.testOrg.ID,
		Name:           "Product to Delete",
		Slug:           "product-to-delete",
	}
	err := suite.db.Create(productToDelete).Error
	suite.NoError(err)
	
	path := "/api/v1/organizations/" + suite.testOrg.ID + "/products/" + productToDelete.ID
	w := suite.makeRequest("DELETE", path, nil)
	suite.Equal(http.StatusNoContent, w.Code)
	
	// Verify product is deleted (soft delete)
	var dbProduct entities.Product
	err = suite.db.Where("id = ?", productToDelete.ID).First(&dbProduct).Error
	suite.Error(err) // Should not find the product
	
	// Verify with Unscoped (should find soft deleted record)
	err = suite.db.Unscoped().Where("id = ?", productToDelete.ID).First(&dbProduct).Error
	suite.NoError(err)
	suite.NotNil(dbProduct.DeletedAt)
}

// TestProductDeleteNotFound test delete non-existent product
func (suite *APITestSuite) TestProductDeleteNotFound() {
	nonExistentID := uuid.New().String()
	path := "/api/v1/organizations/" + suite.testOrg.ID + "/products/" + nonExistentID
	w := suite.makeRequest("DELETE", path, nil)
	suite.Equal(http.StatusNotFound, w.Code)
	
	var response struct {
		Error string `json:"error"`
	}
	suite.parseResponse(w, &response)
	suite.Equal("product not found", response.Error)
}

// TestProductDeleteWithPolicies test cascade delete behavior
func (suite *APITestSuite) TestProductDeleteWithPolicies() {
	// Create a product with policies
	productWithPolicies := &entities.Product{
		ID:             uuid.New().String(),
		OrganizationID: suite.testOrg.ID,
		Name:           "Product with Policies",
		Slug:           "product-with-policies",
	}
	err := suite.db.Create(productWithPolicies).Error
	suite.NoError(err)
	
	// Create a policy for this product
	policy := &entities.Policy{
		ID:             uuid.New().String(),
		OrganizationID: suite.testOrg.ID,
		ProductID:      productWithPolicies.ID,
		Name:           "Test Policy for Product",
	}
	policy.SetDefaults()
	err = suite.db.Create(policy).Error
	suite.NoError(err)
	
	// Delete the product
	path := "/api/v1/organizations/" + suite.testOrg.ID + "/products/" + productWithPolicies.ID
	w := suite.makeRequest("DELETE", path, nil)
	suite.Equal(http.StatusNoContent, w.Code)
	
	// Verify product is deleted
	var dbProduct entities.Product
	err = suite.db.Where("id = ?", productWithPolicies.ID).First(&dbProduct).Error
	suite.Error(err)
	
	// Verify associated policies are also deleted (cascade)
	var dbPolicy entities.Policy
	err = suite.db.Where("id = ?", policy.ID).First(&dbPolicy).Error
	suite.Error(err) // Should be deleted due to cascade
}

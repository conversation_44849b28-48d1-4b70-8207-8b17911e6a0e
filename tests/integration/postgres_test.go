package integration

import (
	"fmt"
	"os"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"

	"github.com/gokeys/gokeys/internal/domain/entities"
)

// PostgreSQLTestSuite là test suite sử dụng PostgreSQL thực tế
// Cần chạy docker-compose -f docker-compose.simple.yml up -d trước
type PostgreSQLTestSuite struct {
	suite.Suite
	db *gorm.DB
	
	// Test data
	testOrg     *entities.Organization
	testProduct *entities.Product
	testPolicy  *entities.Policy
	testLicense *entities.License
	testMachine *entities.Machine
}

// SetupSuite khởi tạo test suite với PostgreSQL database
func (suite *PostgreSQLTestSuite) SetupSuite() {
	// Database connection config
	dbHost := getEnv("DB_HOST", "localhost")
	dbPort := getEnv("DB_PORT", "5432")
	dbUser := getEnv("DB_USER", "gokeys")
	dbPassword := getEnv("DB_PASSWORD", "gokeys")
	dbName := getEnv("DB_NAME", "gokeys")
	
	dsn := fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s sslmode=disable TimeZone=UTC",
		dbHost, dbPort, dbUser, dbPassword, dbName)
	
	// Connect to PostgreSQL
	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{})
	require.NoError(suite.T(), err)
	suite.db = db
	
	// Test connection
	sqlDB, err := db.DB()
	require.NoError(suite.T(), err)
	err = sqlDB.Ping()
	require.NoError(suite.T(), err)
	
	// Auto migrate all entities
	err = db.AutoMigrate(
		&entities.Organization{},
		&entities.Product{},
		&entities.Policy{},
		&entities.License{},
		&entities.Machine{},
		&entities.User{},
	)
	require.NoError(suite.T(), err)
}

// SetupTest tạo test data mới cho mỗi test case
func (suite *PostgreSQLTestSuite) SetupTest() {
	// Clean database - use TRUNCATE for PostgreSQL
	suite.db.Exec("TRUNCATE TABLE machines, licenses, policies, products, organizations, users RESTART IDENTITY CASCADE")
	
	// Create test organization
	suite.testOrg = &entities.Organization{
		ID:    uuid.New().String(),
		Name:  "Test Organization",
		Slug:  "test-org",
		Email: "<EMAIL>",
		Type:  "vendor",
	}
	err := suite.db.Create(suite.testOrg).Error
	require.NoError(suite.T(), err)
	
	// Create test product
	suite.testProduct = &entities.Product{
		ID:             uuid.New().String(),
		OrganizationID: suite.testOrg.ID,
		Name:           "Test Product",
		Code:           "test-product",
		Key:            "test-product-key",
	}
	err = suite.db.Create(suite.testProduct).Error
	require.NoError(suite.T(), err)
	
	// Create test policy
	suite.testPolicy = &entities.Policy{
		ID:             uuid.New().String(),
		OrganizationID: suite.testOrg.ID,
		ProductID:      suite.testProduct.ID,
		Name:           "Test Policy",
		Floating:       true,
		MaxMachines:    &[]int{5}[0],
		Duration:       &[]int{86400 * 30}[0], // 30 days
	}
	// Set defaults
	suite.testPolicy.SetDefaults()
	err = suite.db.Create(suite.testPolicy).Error
	require.NoError(suite.T(), err)
	
	// Create test license
	suite.testLicense = &entities.License{
		ID:             uuid.New().String(),
		OrganizationID: suite.testOrg.ID,
		ProductID:      suite.testProduct.ID,
		PolicyID:       suite.testPolicy.ID,
		Key:            "TEST-LICENSE-KEY-" + uuid.New().String()[:8],
		OwnerType:      "organization",
		OwnerID:        suite.testOrg.ID,
		Status:         "active",
	}
	err = suite.db.Create(suite.testLicense).Error
	require.NoError(suite.T(), err)
	
	// Create test machine
	suite.testMachine = &entities.Machine{
		ID:          uuid.New().String(),
		LicenseID:   suite.testLicense.ID,
		PolicyID:    suite.testPolicy.ID,
		Fingerprint: "test-fingerprint-" + uuid.New().String()[:8],
		Name:        &[]string{"Test Machine"}[0],
		Platform:    &[]string{"linux"}[0],
		Cores:       4,
		Status:      "active",
	}
	err = suite.db.Create(suite.testMachine).Error
	require.NoError(suite.T(), err)
}

// TearDownSuite cleanup sau khi chạy xong tất cả tests
func (suite *PostgreSQLTestSuite) TearDownSuite() {
	if suite.db != nil {
		sqlDB, _ := suite.db.DB()
		sqlDB.Close()
	}
}

// === POSTGRESQL INTEGRATION TESTS ===

// TestPostgreSQLConnection test database connection
func (suite *PostgreSQLTestSuite) TestPostgreSQLConnection() {
	// Test basic connection
	sqlDB, err := suite.db.DB()
	suite.NoError(err)
	
	err = sqlDB.Ping()
	suite.NoError(err)
	
	// Test query
	var version string
	err = suite.db.Raw("SELECT version()").Scan(&version).Error
	suite.NoError(err)
	suite.Contains(version, "PostgreSQL")
}

// TestEntityCRUDWithConstraints test CRUD với PostgreSQL constraints
func (suite *PostgreSQLTestSuite) TestEntityCRUDWithConstraints() {
	// Test organization creation
	org := &entities.Organization{
		ID:    uuid.New().String(),
		Name:  "New Test Org",
		Slug:  "new-test-org",
		Email: "<EMAIL>",
		Type:  "customer",
	}
	
	err := suite.db.Create(org).Error
	suite.NoError(err)
	
	// Test unique constraint violation
	duplicateOrg := &entities.Organization{
		ID:    uuid.New().String(),
		Name:  "Another Org",
		Slug:  "new-test-org", // Same slug - should fail
		Email: "<EMAIL>",
		Type:  "customer",
	}
	
	err = suite.db.Create(duplicateOrg).Error
	suite.Error(err) // Should fail due to unique constraint
	suite.Contains(err.Error(), "duplicate key")
}

// TestComplexQueries test complex PostgreSQL queries
func (suite *PostgreSQLTestSuite) TestComplexQueries() {
	// Create additional test data
	for i := 0; i < 5; i++ {
		license := &entities.License{
			ID:             uuid.New().String(),
			OrganizationID: suite.testOrg.ID,
			ProductID:      suite.testProduct.ID,
			PolicyID:       suite.testPolicy.ID,
			Key:            fmt.Sprintf("LICENSE-KEY-%d", i),
			OwnerType:      "organization",
			OwnerID:        suite.testOrg.ID,
			Status:         "active",
			Uses:           i * 10,
		}
		err := suite.db.Create(license).Error
		suite.NoError(err)
	}
	
	// Test aggregate queries
	var totalUses int64
	err := suite.db.Model(&entities.License{}).
		Where("organization_id = ?", suite.testOrg.ID).
		Select("SUM(uses)").Scan(&totalUses).Error
	suite.NoError(err)
	suite.Equal(int64(100), totalUses) // 0+10+20+30+40 = 100
	
	// Test JOIN queries
	var results []struct {
		LicenseKey   string
		ProductName  string
		PolicyName   string
		Uses         int
	}
	
	err = suite.db.Table("licenses l").
		Select("l.key as license_key, p.name as product_name, pol.name as policy_name, l.uses").
		Joins("JOIN products p ON l.product_id = p.id").
		Joins("JOIN policies pol ON l.policy_id = pol.id").
		Where("l.organization_id = ?", suite.testOrg.ID).
		Scan(&results).Error
	suite.NoError(err)
	suite.GreaterOrEqual(len(results), 5)
}

// TestJSONBFunctionality test JSONB với PostgreSQL
func (suite *PostgreSQLTestSuite) TestJSONBFunctionality() {
	// Create machine with complex components
	components := entities.MachineComponents{
		"cpu_id":         "Intel-i9-13900K",
		"motherboard_id": "ASUS-Z790-HERO",
		"disk_id":        "Samsung-980-PRO-2TB",
		"mac_address":    "00:11:22:33:44:55",
		"gpu_id":         "RTX-4090",
		"ram_size":       "64GB",
	}
	
	machine := &entities.Machine{
		ID:          uuid.New().String(),
		LicenseID:   suite.testLicense.ID,
		PolicyID:    suite.testPolicy.ID,
		Fingerprint: "jsonb-test-machine",
		Components:  components,
		Status:      "active",
	}
	
	err := suite.db.Create(machine).Error
	suite.NoError(err)
	
	// Test JSONB queries
	var retrievedMachine entities.Machine
	err = suite.db.Where("id = ?", machine.ID).First(&retrievedMachine).Error
	suite.NoError(err)
	
	// Test component access
	suite.Equal("Intel-i9-13900K", retrievedMachine.Components.GetComponent("cpu_id"))
	suite.Equal("RTX-4090", retrievedMachine.Components.GetComponent("gpu_id"))
	suite.True(retrievedMachine.Components.HasComponent("mac_address"))
	
	// Test PostgreSQL JSONB operators
	var machinesWithIntelCPU []entities.Machine
	err = suite.db.Where("components->>'cpu_id' LIKE ?", "%Intel%").Find(&machinesWithIntelCPU).Error
	suite.NoError(err)
	suite.GreaterOrEqual(len(machinesWithIntelCPU), 1)
	
	// Test JSONB path queries
	var machinesWithGPU []entities.Machine
	err = suite.db.Where("components ? 'gpu_id'").Find(&machinesWithGPU).Error
	suite.NoError(err)
	suite.GreaterOrEqual(len(machinesWithGPU), 1)
}

// TestTransactions test database transactions
func (suite *PostgreSQLTestSuite) TestTransactions() {
	// Test successful transaction
	err := suite.db.Transaction(func(tx *gorm.DB) error {
		// Create organization
		org := &entities.Organization{
			ID:    uuid.New().String(),
			Name:  "Transaction Test Org",
			Slug:  "transaction-test-org",
			Email: "<EMAIL>",
			Type:  "vendor",
		}
		if err := tx.Create(org).Error; err != nil {
			return err
		}
		
		// Create product
		product := &entities.Product{
			ID:             uuid.New().String(),
			OrganizationID: org.ID,
			Name:           "Transaction Test Product",
			Code:           "transaction-test-product",
			Key:            "transaction-test-product-key",
		}
		if err := tx.Create(product).Error; err != nil {
			return err
		}
		
		return nil
	})
	suite.NoError(err)
	
	// Verify data was committed
	var count int64
	err = suite.db.Model(&entities.Organization{}).Where("name = ?", "Transaction Test Org").Count(&count).Error
	suite.NoError(err)
	suite.Equal(int64(1), count)
}

// TestPerformanceWithIndexes test performance với PostgreSQL indexes
func (suite *PostgreSQLTestSuite) TestPerformanceWithIndexes() {
	// Create large dataset
	licenses := make([]*entities.License, 100)
	for i := 0; i < 100; i++ {
		licenses[i] = &entities.License{
			ID:             uuid.New().String(),
			OrganizationID: suite.testOrg.ID,
			ProductID:      suite.testProduct.ID,
			PolicyID:       suite.testPolicy.ID,
			Key:            fmt.Sprintf("PERF-LICENSE-%03d", i),
			OwnerType:      "organization",
			OwnerID:        suite.testOrg.ID,
			Status:         []string{"active", "suspended", "expired"}[i%3],
			Uses:           i,
		}
	}
	
	// Batch insert
	err := suite.db.CreateInBatches(licenses, 20).Error
	suite.NoError(err)
	
	// Test indexed query performance
	start := time.Now()
	var activeLicenses []entities.License
	err = suite.db.Where("status = ? AND organization_id = ?", "active", suite.testOrg.ID).Find(&activeLicenses).Error
	suite.NoError(err)
	queryTime := time.Since(start)
	
	suite.Less(queryTime, 100*time.Millisecond, "Query should be fast with indexes")
	suite.GreaterOrEqual(len(activeLicenses), 30) // Should find ~33 active licenses
}

// Helper function to get environment variables
func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// TestPostgreSQLTestSuite chạy test suite
func TestPostgreSQLTestSuite(t *testing.T) {
	suite.Run(t, new(PostgreSQLTestSuite))
}

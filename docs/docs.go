// Package docs Code generated by swaggo/swag. DO NOT EDIT
package docs

import "github.com/swaggo/swag"

const docTemplate = `{
    "schemes": {{ marshal .Schemes }},
    "swagger": "2.0",
    "info": {
        "description": "{{escape .Description}}",
        "title": "{{.Title}}",
        "termsOfService": "https://gokeys.com/terms",
        "contact": {
            "name": "GoKeys API Support",
            "url": "https://gokeys.com/support",
            "email": "<EMAIL>"
        },
        "license": {
            "name": "MIT",
            "url": "https://opensource.org/licenses/MIT"
        },
        "version": "{{.Version}}"
    },
    "host": "{{.Host}}",
    "basePath": "{{.BasePath}}",
    "paths": {
        "/admin/users": {
            "post": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "Tạo user mới và thiết lập user_organization constraints",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "auth"
                ],
                "summary": "Tạo user mới (Admin only)",
                "parameters": [
                    {
                        "description": "User creation data",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/handlers.CreateUserRequest"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Created",
                        "schema": {
                            "$ref": "#/definitions/handlers.UserInfo"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "Forbidden",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/admin/users/{id}": {
            "delete": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "Xóa user và tất cả constraints liên quan",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "auth"
                ],
                "summary": "Xóa user",
                "parameters": [
                    {
                        "type": "string",
                        "format": "uuid",
                        "description": "User ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "204": {
                        "description": "No Content"
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "Forbidden",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/auth/login": {
            "post": {
                "description": "Authenticate user và trả về subject với permissions từ user_organizations",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "auth"
                ],
                "summary": "User login với constraint-based permissions",
                "parameters": [
                    {
                        "description": "Login credentials",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/handlers.LoginRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/handlers.LoginResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/auth/register": {
            "post": {
                "description": "Register a new user and optionally create an organization",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "auth"
                ],
                "summary": "Register new user",
                "parameters": [
                    {
                        "description": "Registration data",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/handlers.RegisterRequest"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Created",
                        "schema": {
                            "$ref": "#/definitions/handlers.LoginResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "409": {
                        "description": "Conflict",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/licenses": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "Lấy danh sách licenses mà user có quyền truy cập",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "license"
                ],
                "summary": "List licenses (Protected - B2B)",
                "parameters": [
                    {
                        "type": "integer",
                        "default": 1,
                        "description": "Page number",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 20,
                        "description": "Items per page",
                        "name": "limit",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "format": "uuid",
                        "description": "Filter by organization ID",
                        "name": "organization_id",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "format": "uuid",
                        "description": "Filter by product ID",
                        "name": "product_id",
                        "in": "query"
                    },
                    {
                        "enum": [
                            "active",
                            "expired",
                            "suspended",
                            "banned"
                        ],
                        "type": "string",
                        "description": "Filter by status",
                        "name": "status",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/handlers.LicenseResponse"
                            }
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "Forbidden",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "Tạo license mới với constraint-based authorization",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "license"
                ],
                "summary": "Create new license (Protected - B2B)",
                "parameters": [
                    {
                        "description": "License creation data",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/handlers.LicenseCreateRequest"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Created",
                        "schema": {
                            "$ref": "#/definitions/handlers.LicenseResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "Forbidden",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/licenses/checkout": {
            "post": {
                "description": "Public endpoint để end-user software checkout license và register machine",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "license-public"
                ],
                "summary": "Checkout license for machine (Public - B2C)",
                "parameters": [
                    {
                        "description": "License checkout data",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/handlers.CheckoutLicenseRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/handlers.ValidationResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/licenses/validate": {
            "post": {
                "description": "Public endpoint để end-user software validate license key",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "license-public"
                ],
                "summary": "Validate license by key (Public - B2C)",
                "parameters": [
                    {
                        "description": "License validation data",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/handlers.ValidateLicenseByKeyRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/handlers.ValidationResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/licenses/{id}": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "Lấy thông tin chi tiết license theo ID",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "license"
                ],
                "summary": "Get license by ID (Protected - B2B)",
                "parameters": [
                    {
                        "type": "string",
                        "format": "uuid",
                        "description": "License ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/handlers.LicenseResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "Forbidden",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "Cập nhật thông tin license theo ID",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "license"
                ],
                "summary": "Update license (Protected - B2B)",
                "parameters": [
                    {
                        "type": "string",
                        "format": "uuid",
                        "description": "License ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "License update data",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/handlers.LicenseUpdateRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/handlers.LicenseResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "Forbidden",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "Xóa license theo ID",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "license"
                ],
                "summary": "Delete license (Protected - B2B)",
                "parameters": [
                    {
                        "type": "string",
                        "format": "uuid",
                        "description": "License ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "204": {
                        "description": "No Content"
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "Forbidden",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/licenses/{id}/validate": {
            "post": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "Validate license với authentication requirements",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "license"
                ],
                "summary": "Validate license (Protected - B2B)",
                "parameters": [
                    {
                        "type": "string",
                        "format": "uuid",
                        "description": "License ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "License validation data",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/handlers.ValidateLicenseRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/handlers.ValidationResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "Forbidden",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/licenses/{key}/info": {
            "get": {
                "description": "Public endpoint để lấy thông tin license theo key",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "license-public"
                ],
                "summary": "Get license info by key (Public - B2C)",
                "parameters": [
                    {
                        "type": "string",
                        "description": "License Key",
                        "name": "key",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/handlers.LicenseResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/machines": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Get a paginated list of machines for the authenticated organization",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Machines"
                ],
                "summary": "List machines",
                "parameters": [
                    {
                        "type": "integer",
                        "example": 1,
                        "description": "Page number (default: 1)",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "example": 25,
                        "description": "Items per page (default: 25, max: 100)",
                        "name": "per_page",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "example": "\"created_at\"",
                        "description": "Sort field (default: created_at)",
                        "name": "sort_by",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "example": "\"DESC\"",
                        "description": "Sort order: ASC or DESC (default: DESC)",
                        "name": "sort_order",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "example": "\"dev-machine\"",
                        "description": "Search term for machine name or fingerprint",
                        "name": "search",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "example": "\"550e8400-e29b-41d4-a716-446655440000\"",
                        "description": "Filter by license ID",
                        "name": "license_id",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "example": "\"550e8400-e29b-41d4-a716-************\"",
                        "description": "Filter by policy ID",
                        "name": "policy_id",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "example": "\"550e8400-e29b-41d4-a716-446655440002\"",
                        "description": "Filter by group ID",
                        "name": "group_id",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "example": "\"550e8400-e29b-41d4-a716-446655440003\"",
                        "description": "Filter by owner ID",
                        "name": "owner_id",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "example": "\"ACTIVE\"",
                        "description": "Filter by status",
                        "name": "status",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "example": "\"windows\"",
                        "description": "Filter by platform",
                        "name": "platform",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "List of machines retrieved successfully",
                        "schema": {
                            "$ref": "#/definitions/handlers.MachineListResponse"
                        }
                    },
                    "401": {
                        "description": "Authentication required",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Create a new machine for license tracking",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Machines"
                ],
                "summary": "Create a new machine",
                "parameters": [
                    {
                        "description": "Machine creation request",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/handlers.MachineCreateRequest"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Machine created successfully",
                        "schema": {
                            "$ref": "#/definitions/handlers.MachineResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid request data",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "401": {
                        "description": "Authentication required",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        },
        "/machines/{id}": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Retrieve detailed information about a specific machine (supports both ID and fingerprint lookup)",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Machines"
                ],
                "summary": "Get machine by ID",
                "parameters": [
                    {
                        "type": "string",
                        "example": "\"550e8400-e29b-41d4-a716-446655440000\"",
                        "description": "Machine ID or fingerprint",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Machine retrieved successfully",
                        "schema": {
                            "$ref": "#/definitions/handlers.MachineResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid machine ID format",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "401": {
                        "description": "Authentication required",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "404": {
                        "description": "Machine not found",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Update an existing machine's properties",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Machines"
                ],
                "summary": "Update machine",
                "parameters": [
                    {
                        "type": "string",
                        "example": "\"550e8400-e29b-41d4-a716-446655440000\"",
                        "description": "Machine ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "Machine update request",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/handlers.MachineUpdateRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Machine updated successfully",
                        "schema": {
                            "$ref": "#/definitions/handlers.MachineResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid request data or machine ID",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "401": {
                        "description": "Authentication required",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "404": {
                        "description": "Machine not found",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Delete a machine by ID (soft delete)",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Machines"
                ],
                "summary": "Delete machine",
                "parameters": [
                    {
                        "type": "string",
                        "example": "\"550e8400-e29b-41d4-a716-446655440000\"",
                        "description": "Machine ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "204": {
                        "description": "Machine deleted successfully"
                    },
                    "400": {
                        "description": "Invalid machine ID format",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Authentication required",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Machine not found",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/machines/{id}/actions/checkout": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Generate and retrieve a signed machine certificate for offline validation",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Machines"
                ],
                "summary": "Checkout machine certificate",
                "parameters": [
                    {
                        "type": "string",
                        "example": "\"550e8400-e29b-41d4-a716-446655440000\"",
                        "description": "Machine ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "boolean",
                        "example": false,
                        "description": "Encrypt the certificate",
                        "name": "encrypt",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "example": "\"ED25519_SIGN\"",
                        "description": "Signing algorithm",
                        "name": "algorithm",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "example": 3600,
                        "description": "Certificate TTL in seconds",
                        "name": "ttl",
                        "in": "query"
                    },
                    {
                        "type": "array",
                        "description": "Additional data to include",
                        "name": "include",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Machine certificate generated successfully",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "400": {
                        "description": "Invalid request parameters",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "401": {
                        "description": "Authentication required",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "404": {
                        "description": "Machine not found",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Generate and retrieve a signed machine certificate for offline validation",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Machines"
                ],
                "summary": "Checkout machine certificate",
                "parameters": [
                    {
                        "type": "string",
                        "example": "\"550e8400-e29b-41d4-a716-446655440000\"",
                        "description": "Machine ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "boolean",
                        "example": false,
                        "description": "Encrypt the certificate",
                        "name": "encrypt",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "example": "\"ED25519_SIGN\"",
                        "description": "Signing algorithm",
                        "name": "algorithm",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "example": 3600,
                        "description": "Certificate TTL in seconds",
                        "name": "ttl",
                        "in": "query"
                    },
                    {
                        "type": "array",
                        "description": "Additional data to include",
                        "name": "include",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Machine certificate generated successfully",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "400": {
                        "description": "Invalid request parameters",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "401": {
                        "description": "Authentication required",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "404": {
                        "description": "Machine not found",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        },
        "/machines/{id}/actions/heartbeats/ping": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Send a heartbeat ping for a machine to indicate it's still active",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Machines"
                ],
                "summary": "Send machine heartbeat ping",
                "parameters": [
                    {
                        "type": "string",
                        "example": "\"550e8400-e29b-41d4-a716-446655440000\"",
                        "description": "Machine ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Heartbeat ping successful",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "400": {
                        "description": "Invalid machine ID format",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Authentication required",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Machine not found",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/machines/{id}/actions/heartbeats/reset": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Reset the heartbeat status for a machine",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Machines"
                ],
                "summary": "Reset machine heartbeat",
                "parameters": [
                    {
                        "type": "string",
                        "example": "\"550e8400-e29b-41d4-a716-446655440000\"",
                        "description": "Machine ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Heartbeat reset successful",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "400": {
                        "description": "Invalid machine ID format",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Authentication required",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Machine not found",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/organization": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Retrieve information about the currently authenticated organization",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Organizations"
                ],
                "summary": "Get current organization",
                "responses": {
                    "200": {
                        "description": "Current organization retrieved successfully",
                        "schema": {
                            "$ref": "#/definitions/handlers.OrganizationResponse"
                        }
                    },
                    "401": {
                        "description": "Authentication required",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "404": {
                        "description": "Organization not found",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Update the currently authenticated organization's properties",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Organizations"
                ],
                "summary": "Update current organization",
                "parameters": [
                    {
                        "description": "Organization update request",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/handlers.OrganizationUpdateRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Current organization updated successfully",
                        "schema": {
                            "$ref": "#/definitions/handlers.OrganizationResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid request data",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "401": {
                        "description": "Authentication required",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "404": {
                        "description": "Organization not found",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        },
        "/organizations": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Get a paginated list of all organizations (admin only)",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Organizations"
                ],
                "summary": "List organizations",
                "parameters": [
                    {
                        "type": "integer",
                        "example": 1,
                        "description": "Page number (default: 1)",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "example": 25,
                        "description": "Items per page (default: 25, max: 100)",
                        "name": "per_page",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "List of organizations retrieved successfully",
                        "schema": {
                            "$ref": "#/definitions/handlers.OrganizationListResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            },
            "post": {
                "description": "Create a new organization for license management",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Organizations"
                ],
                "summary": "Create a new organization",
                "parameters": [
                    {
                        "description": "Organization creation request",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/handlers.OrganizationCreateRequest"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Organization created successfully",
                        "schema": {
                            "$ref": "#/definitions/handlers.OrganizationResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid request data",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        },
        "/organizations/{id}": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Retrieve detailed information about a specific organization",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Organizations"
                ],
                "summary": "Get organization by ID",
                "parameters": [
                    {
                        "type": "string",
                        "example": "\"550e8400-e29b-41d4-a716-446655440000\"",
                        "description": "Organization ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Organization retrieved successfully",
                        "schema": {
                            "$ref": "#/definitions/handlers.OrganizationResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid organization ID format",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "404": {
                        "description": "Organization not found",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Update an existing organization's properties",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Organizations"
                ],
                "summary": "Update organization",
                "parameters": [
                    {
                        "type": "string",
                        "example": "\"550e8400-e29b-41d4-a716-446655440000\"",
                        "description": "Organization ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "Organization update request",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/handlers.OrganizationUpdateRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Organization updated successfully",
                        "schema": {
                            "$ref": "#/definitions/handlers.OrganizationResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid request data or organization ID",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "404": {
                        "description": "Organization not found",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Delete an organization by ID (soft delete)",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Organizations"
                ],
                "summary": "Delete organization",
                "parameters": [
                    {
                        "type": "string",
                        "example": "\"550e8400-e29b-41d4-a716-446655440000\"",
                        "description": "Organization ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Organization deleted successfully",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "400": {
                        "description": "Invalid organization ID format",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "404": {
                        "description": "Organization not found",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        },
        "/organizations/{organization_id}/machines/{id}/actions/check-out": {
            "get": {
                "responses": {}
            },
            "post": {
                "responses": {}
            }
        },
        "/organizations/{organization_id}/machines/{id}/actions/ping": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Send a heartbeat ping for a machine to indicate it's still active",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Machines"
                ],
                "summary": "Send machine heartbeat ping",
                "parameters": [
                    {
                        "type": "string",
                        "example": "\"550e8400-e29b-41d4-a716-446655440000\"",
                        "description": "Machine ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "example": "\"550e8400-e29b-41d4-a716-************\"",
                        "description": "Organization ID (for organization-scoped routes)",
                        "name": "organization_id",
                        "in": "path"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Heartbeat ping successful",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "400": {
                        "description": "Invalid machine ID format",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Authentication required",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Machine not found",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/organizations/{organization_id}/machines/{id}/actions/reset": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Reset the heartbeat status for a machine",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Machines"
                ],
                "summary": "Reset machine heartbeat",
                "parameters": [
                    {
                        "type": "string",
                        "example": "\"550e8400-e29b-41d4-a716-446655440000\"",
                        "description": "Machine ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "example": "\"550e8400-e29b-41d4-a716-************\"",
                        "description": "Organization ID (for organization-scoped routes)",
                        "name": "organization_id",
                        "in": "path"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Heartbeat reset successful",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "400": {
                        "description": "Invalid machine ID format",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Authentication required",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Machine not found",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/policies": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Get a paginated list of policies for the authenticated organization",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Policies"
                ],
                "summary": "List policies",
                "parameters": [
                    {
                        "type": "integer",
                        "example": 1,
                        "description": "Page number (default: 1)",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "example": 25,
                        "description": "Items per page (default: 25, max: 100)",
                        "name": "per_page",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "example": "\"created_at\"",
                        "description": "Sort field (default: created_at)",
                        "name": "sort_by",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "example": "\"DESC\"",
                        "description": "Sort order: ASC or DESC (default: DESC)",
                        "name": "sort_order",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "example": "\"standard\"",
                        "description": "Search term for policy name",
                        "name": "search",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "example": "\"550e8400-e29b-41d4-a716-446655440000\"",
                        "description": "Filter by product ID",
                        "name": "product_id",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "example": "\"550e8400-e29b-41d4-a716-************\"",
                        "description": "Filter by environment ID",
                        "name": "environment_id",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "List of policies retrieved successfully",
                        "schema": {
                            "$ref": "#/definitions/handlers.PolicyListResponse"
                        }
                    },
                    "401": {
                        "description": "Authentication required",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Create a new policy for license management",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Policies"
                ],
                "summary": "Create a new policy",
                "parameters": [
                    {
                        "description": "Policy creation request",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/handlers.PolicyCreateRequest"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Policy created successfully",
                        "schema": {
                            "$ref": "#/definitions/handlers.PolicyResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid request data",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "401": {
                        "description": "Authentication required",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        },
        "/policies/{id}": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Retrieve detailed information about a specific policy",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Policies"
                ],
                "summary": "Get policy by ID",
                "parameters": [
                    {
                        "type": "string",
                        "example": "\"550e8400-e29b-41d4-a716-446655440000\"",
                        "description": "Policy ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Policy retrieved successfully",
                        "schema": {
                            "$ref": "#/definitions/handlers.PolicyResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid policy ID format",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "401": {
                        "description": "Authentication required",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "404": {
                        "description": "Policy not found",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Update an existing policy's properties",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Policies"
                ],
                "summary": "Update policy",
                "parameters": [
                    {
                        "type": "string",
                        "example": "\"550e8400-e29b-41d4-a716-446655440000\"",
                        "description": "Policy ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "Policy update request",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/handlers.PolicyUpdateRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Policy updated successfully",
                        "schema": {
                            "$ref": "#/definitions/handlers.PolicyResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid request data or policy ID",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "401": {
                        "description": "Authentication required",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "404": {
                        "description": "Policy not found",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Delete a policy by ID (soft delete)",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Policies"
                ],
                "summary": "Delete policy",
                "parameters": [
                    {
                        "type": "string",
                        "example": "\"550e8400-e29b-41d4-a716-446655440000\"",
                        "description": "Policy ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "204": {
                        "description": "Policy deleted successfully"
                    },
                    "400": {
                        "description": "Invalid policy ID format",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Authentication required",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Policy not found",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/products": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Get a paginated list of products for the authenticated organization",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Products"
                ],
                "summary": "List products",
                "parameters": [
                    {
                        "type": "integer",
                        "example": 1,
                        "description": "Page number (default: 1)",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "example": 25,
                        "description": "Items per page (default: 25, max: 100)",
                        "name": "per_page",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "example": "\"created_at\"",
                        "description": "Sort field (default: created_at)",
                        "name": "sort_by",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "example": "\"DESC\"",
                        "description": "Sort order: ASC or DESC (default: DESC)",
                        "name": "sort_order",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "example": "\"software\"",
                        "description": "Search term for product name or code",
                        "name": "search",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "example": "\"licensed\"",
                        "description": "Filter by distribution strategy",
                        "name": "distribution_strategy",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "List of products retrieved successfully",
                        "schema": {
                            "$ref": "#/definitions/handlers.ProductListResponse"
                        }
                    },
                    "401": {
                        "description": "Authentication required",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Create a new product for license management",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Products"
                ],
                "summary": "Create a new product",
                "parameters": [
                    {
                        "description": "Product creation request",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/handlers.ProductCreateRequest"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Product created successfully",
                        "schema": {
                            "$ref": "#/definitions/handlers.ProductResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid request data",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "401": {
                        "description": "Authentication required",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        },
        "/products/{id}": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Retrieve detailed information about a specific product",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Products"
                ],
                "summary": "Get product by ID",
                "parameters": [
                    {
                        "type": "string",
                        "example": "\"550e8400-e29b-41d4-a716-446655440000\"",
                        "description": "Product ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Product retrieved successfully",
                        "schema": {
                            "$ref": "#/definitions/handlers.ProductResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid product ID format",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "401": {
                        "description": "Authentication required",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "404": {
                        "description": "Product not found",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Update an existing product's properties",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Products"
                ],
                "summary": "Update product",
                "parameters": [
                    {
                        "type": "string",
                        "example": "\"550e8400-e29b-41d4-a716-446655440000\"",
                        "description": "Product ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "Product update request",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/handlers.ProductUpdateRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Product updated successfully",
                        "schema": {
                            "$ref": "#/definitions/handlers.ProductResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid request data or product ID",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "401": {
                        "description": "Authentication required",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "404": {
                        "description": "Product not found",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Delete a product by ID (soft delete)",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Products"
                ],
                "summary": "Delete product",
                "parameters": [
                    {
                        "type": "string",
                        "example": "\"550e8400-e29b-41d4-a716-446655440000\"",
                        "description": "Product ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "204": {
                        "description": "Product deleted successfully"
                    },
                    "400": {
                        "description": "Invalid product ID format",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Authentication required",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Product not found",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/user-organizations": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Get paginated list of user-organization relationships for constraint-based authorization",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "UserOrganizations"
                ],
                "summary": "List user-organization relationships",
                "parameters": [
                    {
                        "type": "integer",
                        "example": 1,
                        "description": "Page number (default: 1)",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "example": 25,
                        "description": "Items per page (default: 25, max: 100)",
                        "name": "per_page",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "example": "\"created_at\"",
                        "description": "Sort field (default: created_at)",
                        "name": "sort_by",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "example": "\"DESC\"",
                        "description": "Sort order: ASC or DESC (default: DESC)",
                        "name": "sort_order",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "example": "\"550e8400-e29b-41d4-a716-446655440000\"",
                        "description": "Filter by user ID",
                        "name": "user_id",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "example": "\"550e8400-e29b-41d4-a716-************\"",
                        "description": "Filter by organization ID",
                        "name": "organization_id",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "example": "\"member\"",
                        "description": "Filter by role",
                        "name": "role",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "example": "\"product\"",
                        "description": "Filter by resource type",
                        "name": "resource_type",
                        "in": "query"
                    },
                    {
                        "type": "boolean",
                        "description": "Filter by active status",
                        "name": "active",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "List retrieved successfully",
                        "schema": {
                            "$ref": "#/definitions/handlers.UserOrganizationListResponse"
                        }
                    },
                    "401": {
                        "description": "Authentication required",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "403": {
                        "description": "Insufficient permissions",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Create a new user-organization relationship with constraint-based permissions",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "UserOrganizations"
                ],
                "summary": "Create user-organization relationship",
                "parameters": [
                    {
                        "description": "User organization creation request",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/handlers.UserOrganizationCreateRequest"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "User organization relationship created successfully",
                        "schema": {
                            "$ref": "#/definitions/handlers.UserOrganizationResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid request data",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "401": {
                        "description": "Authentication required",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "403": {
                        "description": "Insufficient permissions",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "409": {
                        "description": "Relationship already exists",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        },
        "/user-organizations/{id}": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Retrieve detailed information about a specific user-organization relationship",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "UserOrganizations"
                ],
                "summary": "Get user-organization relationship by ID",
                "parameters": [
                    {
                        "type": "string",
                        "example": "\"550e8400-e29b-41d4-a716-446655440000\"",
                        "description": "User Organization ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "User organization relationship retrieved successfully",
                        "schema": {
                            "$ref": "#/definitions/handlers.UserOrganizationResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid ID format",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "401": {
                        "description": "Authentication required",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "404": {
                        "description": "Relationship not found",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Update an existing user-organization relationship's permissions",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "UserOrganizations"
                ],
                "summary": "Update user-organization relationship",
                "parameters": [
                    {
                        "type": "string",
                        "example": "\"550e8400-e29b-41d4-a716-446655440000\"",
                        "description": "User Organization ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "User organization update request",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/handlers.UserOrganizationUpdateRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "User organization relationship updated successfully",
                        "schema": {
                            "$ref": "#/definitions/handlers.UserOrganizationResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid request data or ID",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "401": {
                        "description": "Authentication required",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "404": {
                        "description": "Relationship not found",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Delete a user-organization relationship (soft delete)",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "UserOrganizations"
                ],
                "summary": "Delete user-organization relationship",
                "parameters": [
                    {
                        "type": "string",
                        "example": "\"550e8400-e29b-41d4-a716-446655440000\"",
                        "description": "User Organization ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "204": {
                        "description": "User organization relationship deleted successfully"
                    },
                    "400": {
                        "description": "Invalid ID format",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "401": {
                        "description": "Authentication required",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "404": {
                        "description": "Relationship not found",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        },
        "/users": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "Lấy danh sách users mà user hiện tại có quyền truy cập",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "auth"
                ],
                "summary": "Liệt kê users",
                "parameters": [
                    {
                        "type": "integer",
                        "default": 1,
                        "description": "Page number",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 20,
                        "description": "Items per page",
                        "name": "limit",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/handlers.UserInfo"
                            }
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "Forbidden",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/users/{id}": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "Lấy thông tin chi tiết user theo ID",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "auth"
                ],
                "summary": "Lấy thông tin user",
                "parameters": [
                    {
                        "type": "string",
                        "format": "uuid",
                        "description": "User ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/handlers.UserInfo"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "Forbidden",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "Cập nhật thông tin user theo ID",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "auth"
                ],
                "summary": "Cập nhật user",
                "parameters": [
                    {
                        "type": "string",
                        "format": "uuid",
                        "description": "User ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "User update data",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/handlers.UpdateUserRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/handlers.UserInfo"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "Forbidden",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/users/{user_id}/permissions": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Get all effective permissions for a user across all organizations and resources",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "UserOrganizations"
                ],
                "summary": "Get user's effective permissions",
                "parameters": [
                    {
                        "type": "string",
                        "example": "\"550e8400-e29b-41d4-a716-446655440000\"",
                        "description": "User ID",
                        "name": "user_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "example": "\"550e8400-e29b-41d4-a716-************\"",
                        "description": "Filter by organization ID",
                        "name": "organization_id",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "User permissions retrieved successfully",
                        "schema": {
                            "$ref": "#/definitions/handlers.UserOrganizationListResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid user ID format",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "401": {
                        "description": "Authentication required",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "404": {
                        "description": "User not found",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        }
    },
    "definitions": {
        "auth.Subject": {
            "type": "object",
            "properties": {
                "id": {
                    "type": "string"
                },
                "metadata": {
                    "type": "object",
                    "additionalProperties": true
                },
                "organization_id": {
                    "type": "string"
                },
                "permissions": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "roles": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "type": {
                    "description": "user, api_token, license, machine",
                    "type": "string"
                }
            }
        },
        "handlers.CheckoutLicenseRequest": {
            "type": "object",
            "required": [
                "fingerprint",
                "license_key",
                "machine_info"
            ],
            "properties": {
                "fingerprint": {
                    "type": "string",
                    "example": "fp-mac-12345678"
                },
                "include": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    },
                    "example": [
                        "[\"entitlements\"",
                        " \"user\"]"
                    ]
                },
                "license_key": {
                    "type": "string",
                    "example": "LIC-12345-ABCDE-67890-FGHIJ"
                },
                "machine_info": {
                    "type": "object",
                    "additionalProperties": true
                },
                "ttl": {
                    "type": "integer",
                    "example": 3600
                }
            }
        },
        "handlers.CreateUserRequest": {
            "type": "object",
            "required": [
                "allowed_actions",
                "email",
                "password",
                "resource_scope",
                "resource_type",
                "role"
            ],
            "properties": {
                "allowed_actions": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    },
                    "example": [
                        "[\"read\"",
                        " \"create\"",
                        " \"update\"]"
                    ]
                },
                "email": {
                    "type": "string",
                    "example": "<EMAIL>"
                },
                "first_name": {
                    "type": "string",
                    "example": "John"
                },
                "last_name": {
                    "type": "string",
                    "example": "Doe"
                },
                "metadata": {
                    "type": "object",
                    "additionalProperties": true
                },
                "organization_id": {
                    "type": "string",
                    "example": "550e8400-e29b-41d4-a716-************"
                },
                "password": {
                    "type": "string",
                    "minLength": 6,
                    "example": "password123"
                },
                "resource_id": {
                    "type": "string",
                    "example": "550e8400-e29b-41d4-a716-446655440002"
                },
                "resource_scope": {
                    "type": "string",
                    "enum": [
                        "specific",
                        "type_wildcard",
                        "global",
                        "owner"
                    ],
                    "example": "type_wildcard"
                },
                "resource_type": {
                    "type": "string",
                    "example": "license"
                },
                "role": {
                    "type": "string",
                    "example": "member"
                }
            }
        },
        "handlers.LicenseCreateRequest": {
            "type": "object",
            "required": [
                "name",
                "owner_id",
                "owner_type",
                "policy_id",
                "product_id"
            ],
            "properties": {
                "expires_at": {
                    "type": "string",
                    "example": "2025-12-31T23:59:59Z"
                },
                "key": {
                    "type": "string",
                    "example": "LIC-12345-ABCDE-67890-FGHIJ"
                },
                "metadata": {
                    "type": "object",
                    "additionalProperties": true
                },
                "name": {
                    "type": "string",
                    "maxLength": 255,
                    "minLength": 1,
                    "example": "Enterprise License"
                },
                "owner_id": {
                    "type": "string",
                    "example": "550e8400-e29b-41d4-a716-446655440002"
                },
                "owner_type": {
                    "type": "string",
                    "enum": [
                        "user",
                        "organization"
                    ],
                    "example": "user"
                },
                "policy_id": {
                    "type": "string",
                    "example": "550e8400-e29b-41d4-a716-446655440000"
                },
                "product_id": {
                    "type": "string",
                    "example": "550e8400-e29b-41d4-a716-************"
                },
                "protected": {
                    "type": "boolean",
                    "example": false
                },
                "suspended": {
                    "type": "boolean",
                    "example": false
                },
                "uses": {
                    "type": "integer",
                    "example": 0
                }
            }
        },
        "handlers.LicenseResponse": {
            "type": "object",
            "properties": {
                "created_at": {
                    "type": "string",
                    "example": "2024-01-01T00:00:00Z"
                },
                "expires_at": {
                    "type": "string",
                    "example": "2025-12-31T23:59:59Z"
                },
                "id": {
                    "type": "string",
                    "example": "550e8400-e29b-41d4-a716-446655440000"
                },
                "key": {
                    "type": "string",
                    "example": "LIC-12345-ABCDE-67890-FGHIJ"
                },
                "last_used": {
                    "type": "string",
                    "example": "2024-01-15T10:30:00Z"
                },
                "metadata": {
                    "type": "object",
                    "additionalProperties": true
                },
                "name": {
                    "type": "string",
                    "example": "Enterprise License"
                },
                "organization_id": {
                    "type": "string",
                    "example": "550e8400-e29b-41d4-a716-************"
                },
                "owner_id": {
                    "type": "string",
                    "example": "550e8400-e29b-41d4-a716-446655440004"
                },
                "owner_type": {
                    "type": "string",
                    "example": "user"
                },
                "policy_id": {
                    "type": "string",
                    "example": "550e8400-e29b-41d4-a716-446655440003"
                },
                "product_id": {
                    "type": "string",
                    "example": "550e8400-e29b-41d4-a716-446655440002"
                },
                "protected": {
                    "type": "boolean",
                    "example": false
                },
                "status": {
                    "type": "string",
                    "example": "active"
                },
                "suspended": {
                    "type": "boolean",
                    "example": false
                },
                "updated_at": {
                    "type": "string",
                    "example": "2024-01-15T10:30:00Z"
                },
                "uses": {
                    "type": "integer",
                    "example": 0
                }
            }
        },
        "handlers.LicenseUpdateRequest": {
            "type": "object",
            "properties": {
                "expires_at": {
                    "type": "string",
                    "example": "2025-12-31T23:59:59Z"
                },
                "metadata": {
                    "type": "object",
                    "additionalProperties": true
                },
                "name": {
                    "type": "string",
                    "maxLength": 255,
                    "minLength": 1,
                    "example": "Updated Enterprise License"
                },
                "protected": {
                    "type": "boolean",
                    "example": false
                },
                "status": {
                    "type": "string",
                    "enum": [
                        "active",
                        "expired",
                        "suspended",
                        "banned"
                    ],
                    "example": "active"
                },
                "suspended": {
                    "type": "boolean",
                    "example": false
                }
            }
        },
        "handlers.LoginRequest": {
            "type": "object",
            "required": [
                "email",
                "password"
            ],
            "properties": {
                "email": {
                    "type": "string",
                    "example": "<EMAIL>"
                },
                "password": {
                    "type": "string",
                    "minLength": 6,
                    "example": "password123"
                }
            }
        },
        "handlers.LoginResponse": {
            "type": "object",
            "properties": {
                "expires_in": {
                    "type": "integer",
                    "example": 3600
                },
                "subject": {
                    "$ref": "#/definitions/auth.Subject"
                },
                "token": {
                    "type": "string",
                    "example": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
                },
                "token_type": {
                    "type": "string",
                    "example": "Bearer"
                },
                "user": {
                    "$ref": "#/definitions/handlers.UserInfo"
                }
            }
        },
        "handlers.MachineCreateRequest": {
            "description": "Request payload for creating a new machine",
            "type": "object",
            "required": [
                "fingerprint",
                "license_id"
            ],
            "properties": {
                "cores": {
                    "type": "integer",
                    "minimum": 1,
                    "example": 8
                },
                "fingerprint": {
                    "type": "string",
                    "maxLength": 255,
                    "minLength": 1,
                    "example": "fp-mac-12345678"
                },
                "hostname": {
                    "type": "string",
                    "example": "dev-machine-01"
                },
                "ip": {
                    "type": "string",
                    "example": "*************"
                },
                "license_id": {
                    "type": "string",
                    "example": "550e8400-e29b-41d4-a716-446655440000"
                },
                "metadata": {
                    "type": "object",
                    "additionalProperties": true
                },
                "name": {
                    "type": "string",
                    "example": "Development Machine"
                },
                "platform": {
                    "type": "string",
                    "example": "windows"
                }
            }
        },
        "handlers.MachineListResponse": {
            "description": "Paginated list of machines",
            "type": "object",
            "properties": {
                "machines": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/handlers.MachineResponse"
                    }
                },
                "pagination": {
                    "$ref": "#/definitions/responses.PaginationMeta"
                }
            }
        },
        "handlers.MachineResponse": {
            "description": "Machine information returned by the API",
            "type": "object",
            "properties": {
                "cores": {
                    "type": "integer",
                    "example": 8
                },
                "created_at": {
                    "type": "string",
                    "example": "2025-01-01T00:00:00Z"
                },
                "fingerprint": {
                    "type": "string",
                    "example": "fp-mac-12345678"
                },
                "hostname": {
                    "type": "string",
                    "example": "dev-machine-01"
                },
                "id": {
                    "type": "string",
                    "example": "550e8400-e29b-41d4-a716-446655440000"
                },
                "ip": {
                    "type": "string",
                    "example": "*************"
                },
                "last_seen": {
                    "type": "string",
                    "example": "2025-07-15T14:30:00Z"
                },
                "license_id": {
                    "type": "string",
                    "example": "550e8400-e29b-41d4-a716-************"
                },
                "metadata": {
                    "type": "object",
                    "additionalProperties": true
                },
                "name": {
                    "type": "string",
                    "example": "Development Machine"
                },
                "owner_id": {
                    "type": "string",
                    "example": "550e8400-e29b-41d4-a716-446655440004"
                },
                "platform": {
                    "type": "string",
                    "example": "windows"
                },
                "policy_id": {
                    "type": "string",
                    "example": "550e8400-e29b-41d4-a716-446655440002"
                },
                "status": {
                    "type": "string",
                    "example": "ACTIVE"
                },
                "updated_at": {
                    "type": "string",
                    "example": "2025-01-15T10:30:00Z"
                }
            }
        },
        "handlers.MachineUpdateRequest": {
            "description": "Request payload for updating a machine",
            "type": "object",
            "properties": {
                "cores": {
                    "type": "integer",
                    "minimum": 1,
                    "example": 16
                },
                "fingerprint": {
                    "type": "string",
                    "maxLength": 255,
                    "minLength": 1,
                    "example": "fp-mac-87654321"
                },
                "hostname": {
                    "type": "string",
                    "example": "updated-dev-machine"
                },
                "ip": {
                    "type": "string",
                    "example": "*************"
                },
                "metadata": {
                    "type": "object",
                    "additionalProperties": true
                },
                "name": {
                    "type": "string",
                    "example": "Updated Development Machine"
                },
                "platform": {
                    "type": "string",
                    "example": "linux"
                }
            }
        },
        "handlers.OrganizationCreateRequest": {
            "description": "Request payload for creating a new organization",
            "type": "object",
            "required": [
                "email",
                "name",
                "slug"
            ],
            "properties": {
                "email": {
                    "type": "string",
                    "example": "<EMAIL>"
                },
                "metadata": {
                    "type": "object",
                    "additionalProperties": true
                },
                "name": {
                    "type": "string",
                    "maxLength": 255,
                    "minLength": 1,
                    "example": "Acme Corporation"
                },
                "slug": {
                    "type": "string",
                    "maxLength": 255,
                    "minLength": 1,
                    "example": "acme-corp"
                }
            }
        },
        "handlers.OrganizationListResponse": {
            "description": "Paginated list of organizations",
            "type": "object",
            "properties": {
                "organizations": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/handlers.OrganizationResponse"
                    }
                },
                "pagination": {
                    "$ref": "#/definitions/handlers.PaginationInfo"
                }
            }
        },
        "handlers.OrganizationResponse": {
            "description": "Organization information returned by the API",
            "type": "object",
            "properties": {
                "created_at": {
                    "type": "string",
                    "example": "2025-01-01T00:00:00Z"
                },
                "email": {
                    "type": "string",
                    "example": "<EMAIL>"
                },
                "id": {
                    "type": "string",
                    "example": "550e8400-e29b-41d4-a716-446655440000"
                },
                "metadata": {
                    "type": "object",
                    "additionalProperties": true
                },
                "name": {
                    "type": "string",
                    "example": "Acme Corporation"
                },
                "slug": {
                    "type": "string",
                    "example": "acme-corp"
                },
                "updated_at": {
                    "type": "string",
                    "example": "2025-01-15T10:30:00Z"
                }
            }
        },
        "handlers.OrganizationUpdateRequest": {
            "description": "Request payload for updating an organization",
            "type": "object",
            "properties": {
                "metadata": {
                    "type": "object",
                    "additionalProperties": true
                },
                "name": {
                    "type": "string",
                    "maxLength": 255,
                    "minLength": 1,
                    "example": "Updated Acme Corporation"
                },
                "slug": {
                    "type": "string",
                    "maxLength": 255,
                    "minLength": 1,
                    "example": "updated-acme"
                }
            }
        },
        "handlers.PaginationInfo": {
            "description": "Pagination information for list responses",
            "type": "object",
            "properties": {
                "page": {
                    "type": "integer",
                    "example": 1
                },
                "per_page": {
                    "type": "integer",
                    "example": 20
                },
                "total": {
                    "type": "integer",
                    "example": 150
                },
                "total_pages": {
                    "type": "integer",
                    "example": 8
                }
            }
        },
        "handlers.PolicyCreateRequest": {
            "description": "Request payload for creating a new policy",
            "type": "object",
            "required": [
                "name",
                "product_id"
            ],
            "properties": {
                "check_in_interval": {
                    "type": "string",
                    "example": "daily"
                },
                "check_in_interval_count": {
                    "type": "integer",
                    "minimum": 0,
                    "example": 1
                },
                "description": {
                    "type": "string",
                    "example": "A standard policy for regular licenses"
                },
                "duration": {
                    "description": "EnvironmentID    *string ` + "`" + `json:\"environment_id,omitempty\" binding:\"omitempty,uuid\" example:\"550e8400-e29b-41d4-a716-************\"` + "`" + `",
                    "type": "integer",
                    "minimum": 0,
                    "example": 365
                },
                "expiration_strategy": {
                    "type": "string",
                    "enum": [
                        "RESTRICT_ACCESS",
                        "REVOKE_ACCESS",
                        "MAINTAIN_ACCESS"
                    ],
                    "example": "RESTRICT_ACCESS"
                },
                "floating": {
                    "type": "boolean",
                    "example": false
                },
                "heartbeat_duration": {
                    "type": "integer",
                    "minimum": 0,
                    "example": 3600
                },
                "machine_uniqueness_strategy": {
                    "type": "string",
                    "enum": [
                        "UNIQUE_PER_ACCOUNT",
                        "UNIQUE_PER_PRODUCT",
                        "UNIQUE_PER_POLICY",
                        "UNIQUE_PER_LICENSE"
                    ],
                    "example": "UNIQUE_PER_LICENSE"
                },
                "max_activations": {
                    "type": "integer",
                    "minimum": 0,
                    "example": 10
                },
                "max_cores": {
                    "type": "integer",
                    "minimum": 0,
                    "example": 8
                },
                "max_deactivations": {
                    "type": "integer",
                    "minimum": 0,
                    "example": 5
                },
                "max_machines": {
                    "description": "Machine limits",
                    "type": "integer",
                    "minimum": 0,
                    "example": 5
                },
                "max_processes": {
                    "type": "integer",
                    "minimum": 0,
                    "example": 10
                },
                "max_users": {
                    "type": "integer",
                    "minimum": 0,
                    "example": 3
                },
                "max_uses": {
                    "type": "integer",
                    "minimum": 0,
                    "example": 100
                },
                "metadata": {
                    "type": "object",
                    "additionalProperties": true
                },
                "name": {
                    "type": "string",
                    "maxLength": 255,
                    "minLength": 1,
                    "example": "Standard License Policy"
                },
                "overage_strategy": {
                    "type": "string",
                    "enum": [
                        "NO_OVERAGE",
                        "ALWAYS_ALLOW_OVERAGE",
                        "ALLOW_1_25X_OVERAGE",
                        "ALLOW_1_5X_OVERAGE",
                        "ALLOW_2X_OVERAGE"
                    ],
                    "example": "NO_OVERAGE"
                },
                "product_id": {
                    "type": "string",
                    "example": "550e8400-e29b-41d4-a716-446655440000"
                },
                "protected": {
                    "type": "boolean",
                    "example": false
                },
                "require_check_in": {
                    "description": "Check-in settings",
                    "type": "boolean",
                    "example": false
                },
                "require_heartbeat": {
                    "type": "boolean",
                    "example": true
                },
                "scheme": {
                    "description": "Advanced settings (simplified)",
                    "type": "string",
                    "enum": [
                        "ED25519_SIGN",
                        "RSA_PKCS1_SIGN",
                        "RSA_PSS_SIGN"
                    ],
                    "example": "ED25519_SIGN"
                },
                "strict": {
                    "type": "boolean",
                    "example": true
                },
                "use_pool": {
                    "description": "Pool and activation limits",
                    "type": "boolean",
                    "example": false
                }
            }
        },
        "handlers.PolicyListResponse": {
            "description": "Paginated list of policies",
            "type": "object",
            "properties": {
                "pagination": {
                    "$ref": "#/definitions/responses.PaginationMeta"
                },
                "policies": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/handlers.PolicyResponse"
                    }
                }
            }
        },
        "handlers.PolicyResponse": {
            "description": "Policy information returned by the API",
            "type": "object",
            "properties": {
                "check_in_interval": {
                    "type": "string",
                    "example": "daily"
                },
                "check_in_interval_count": {
                    "type": "integer",
                    "example": 1
                },
                "created_at": {
                    "type": "string",
                    "example": "2025-01-01T00:00:00Z"
                },
                "description": {
                    "type": "string",
                    "example": "A standard policy for regular licenses"
                },
                "duration": {
                    "description": "EnvironmentID             *string                ` + "`" + `json:\"environment_id,omitempty\" example:\"550e8400-e29b-41d4-a716-446655440002\"` + "`" + `",
                    "type": "integer",
                    "example": 365
                },
                "expiration_strategy": {
                    "type": "string",
                    "example": "RESTRICT_ACCESS"
                },
                "floating": {
                    "type": "boolean",
                    "example": false
                },
                "heartbeat_duration": {
                    "type": "integer",
                    "example": 3600
                },
                "id": {
                    "type": "string",
                    "example": "550e8400-e29b-41d4-a716-446655440000"
                },
                "machine_uniqueness_strategy": {
                    "type": "string",
                    "example": "UNIQUE_PER_LICENSE"
                },
                "max_activations": {
                    "type": "integer",
                    "example": 10
                },
                "max_cores": {
                    "type": "integer",
                    "example": 8
                },
                "max_deactivations": {
                    "type": "integer",
                    "example": 5
                },
                "max_machines": {
                    "type": "integer",
                    "example": 5
                },
                "max_processes": {
                    "type": "integer",
                    "example": 10
                },
                "max_users": {
                    "type": "integer",
                    "example": 3
                },
                "max_uses": {
                    "type": "integer",
                    "example": 100
                },
                "metadata": {
                    "type": "object",
                    "additionalProperties": true
                },
                "name": {
                    "type": "string",
                    "example": "Standard License Policy"
                },
                "overage_strategy": {
                    "type": "string",
                    "example": "NO_OVERAGE"
                },
                "product_id": {
                    "type": "string",
                    "example": "550e8400-e29b-41d4-a716-************"
                },
                "protected": {
                    "type": "boolean",
                    "example": false
                },
                "require_check_in": {
                    "type": "boolean",
                    "example": false
                },
                "require_heartbeat": {
                    "type": "boolean",
                    "example": true
                },
                "scheme": {
                    "type": "string",
                    "example": "ED25519_SIGN"
                },
                "strict": {
                    "type": "boolean",
                    "example": true
                },
                "updated_at": {
                    "type": "string",
                    "example": "2025-01-15T10:30:00Z"
                },
                "use_pool": {
                    "type": "boolean",
                    "example": false
                }
            }
        },
        "handlers.PolicyUpdateRequest": {
            "description": "Request payload for updating a policy",
            "type": "object",
            "properties": {
                "check_in_interval": {
                    "type": "string",
                    "example": "weekly"
                },
                "check_in_interval_count": {
                    "type": "integer",
                    "minimum": 0,
                    "example": 2
                },
                "description": {
                    "type": "string",
                    "example": "An updated standard policy for regular licenses"
                },
                "duration": {
                    "type": "integer",
                    "minimum": 0,
                    "example": 730
                },
                "expiration_strategy": {
                    "type": "string",
                    "example": "REVOKE_ACCESS"
                },
                "floating": {
                    "type": "boolean",
                    "example": true
                },
                "heartbeat_duration": {
                    "type": "integer",
                    "minimum": 0,
                    "example": 7200
                },
                "machine_uniqueness_strategy": {
                    "type": "string",
                    "example": "UNIQUE_PER_POLICY"
                },
                "max_activations": {
                    "type": "integer",
                    "minimum": 0,
                    "example": 20
                },
                "max_cores": {
                    "type": "integer",
                    "minimum": 0,
                    "example": 16
                },
                "max_deactivations": {
                    "type": "integer",
                    "minimum": 0,
                    "example": 10
                },
                "max_machines": {
                    "description": "Machine limits",
                    "type": "integer",
                    "minimum": 0,
                    "example": 10
                },
                "max_processes": {
                    "type": "integer",
                    "minimum": 0,
                    "example": 20
                },
                "max_users": {
                    "type": "integer",
                    "minimum": 0,
                    "example": 5
                },
                "max_uses": {
                    "type": "integer",
                    "minimum": 0,
                    "example": 200
                },
                "metadata": {
                    "type": "object",
                    "additionalProperties": true
                },
                "name": {
                    "type": "string",
                    "maxLength": 255,
                    "minLength": 1,
                    "example": "Updated Standard License Policy"
                },
                "overage_strategy": {
                    "type": "string",
                    "example": "ALLOW_1_25X_OVERAGE"
                },
                "protected": {
                    "type": "boolean",
                    "example": true
                },
                "require_check_in": {
                    "description": "Check-in settings",
                    "type": "boolean",
                    "example": true
                },
                "require_heartbeat": {
                    "type": "boolean",
                    "example": false
                },
                "scheme": {
                    "description": "Advanced settings",
                    "type": "string",
                    "enum": [
                        "ED25519_SIGN",
                        "RSA_PKCS1_SIGN",
                        "RSA_PSS_SIGN"
                    ],
                    "example": "RSA_PKCS1_SIGN"
                },
                "strict": {
                    "type": "boolean",
                    "example": false
                },
                "use_pool": {
                    "description": "Pool and activation limits",
                    "type": "boolean",
                    "example": true
                }
            }
        },
        "handlers.ProductCreateRequest": {
            "description": "Request payload for creating a new product",
            "type": "object",
            "required": [
                "code",
                "name"
            ],
            "properties": {
                "code": {
                    "type": "string",
                    "maxLength": 255,
                    "minLength": 1,
                    "example": "my-software"
                },
                "description": {
                    "type": "string",
                    "example": "A comprehensive software solution"
                },
                "distribution_strategy": {
                    "type": "string",
                    "enum": [
                        "licensed",
                        "open"
                    ],
                    "example": "licensed"
                },
                "metadata": {
                    "type": "object",
                    "additionalProperties": true
                },
                "name": {
                    "type": "string",
                    "maxLength": 255,
                    "minLength": 1,
                    "example": "My Software Product"
                },
                "platforms": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    },
                    "example": [
                        "windows",
                        "macos",
                        "linux"
                    ]
                },
                "url": {
                    "type": "string",
                    "example": "https://example.com/product"
                }
            }
        },
        "handlers.ProductListResponse": {
            "description": "Paginated list of products",
            "type": "object",
            "properties": {
                "pagination": {
                    "$ref": "#/definitions/responses.PaginationMeta"
                },
                "products": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/handlers.ProductResponse"
                    }
                }
            }
        },
        "handlers.ProductResponse": {
            "description": "Product information returned by the API",
            "type": "object",
            "properties": {
                "code": {
                    "type": "string",
                    "example": "my-software"
                },
                "created_at": {
                    "type": "string",
                    "example": "2025-01-01T00:00:00Z"
                },
                "description": {
                    "type": "string",
                    "example": "A comprehensive software solution"
                },
                "distribution_strategy": {
                    "type": "string",
                    "example": "licensed"
                },
                "id": {
                    "type": "string",
                    "example": "550e8400-e29b-41d4-a716-446655440000"
                },
                "metadata": {
                    "type": "object",
                    "additionalProperties": true
                },
                "name": {
                    "type": "string",
                    "example": "My Software Product"
                },
                "platforms": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    },
                    "example": [
                        "windows",
                        "macos",
                        "linux"
                    ]
                },
                "updated_at": {
                    "type": "string",
                    "example": "2025-01-15T10:30:00Z"
                },
                "url": {
                    "type": "string",
                    "example": "https://example.com/product"
                }
            }
        },
        "handlers.ProductUpdateRequest": {
            "description": "Request payload for updating a product",
            "type": "object",
            "properties": {
                "code": {
                    "type": "string",
                    "maxLength": 255,
                    "minLength": 1,
                    "example": "updated-software"
                },
                "description": {
                    "type": "string",
                    "example": "An updated comprehensive software solution"
                },
                "distribution_strategy": {
                    "type": "string",
                    "enum": [
                        "licensed",
                        "open"
                    ],
                    "example": "licensed"
                },
                "metadata": {
                    "type": "object",
                    "additionalProperties": true
                },
                "name": {
                    "type": "string",
                    "maxLength": 255,
                    "minLength": 1,
                    "example": "Updated Software Product"
                },
                "platforms": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    },
                    "example": [
                        "windows",
                        "macos",
                        "linux",
                        "android"
                    ]
                },
                "url": {
                    "type": "string",
                    "example": "https://example.com/updated-product"
                }
            }
        },
        "handlers.RegisterRequest": {
            "type": "object",
            "required": [
                "email",
                "password",
                "password_confirm"
            ],
            "properties": {
                "email": {
                    "type": "string",
                    "example": "<EMAIL>"
                },
                "first_name": {
                    "type": "string",
                    "example": "John"
                },
                "last_name": {
                    "type": "string",
                    "example": "Doe"
                },
                "metadata": {
                    "type": "object",
                    "additionalProperties": true
                },
                "organization_name": {
                    "type": "string",
                    "example": "Acme Corp"
                },
                "password": {
                    "type": "string",
                    "minLength": 6,
                    "example": "password123"
                },
                "password_confirm": {
                    "type": "string",
                    "example": "password123"
                }
            }
        },
        "handlers.UpdateUserRequest": {
            "type": "object",
            "properties": {
                "first_name": {
                    "type": "string",
                    "example": "John"
                },
                "last_name": {
                    "type": "string",
                    "example": "Doe"
                },
                "metadata": {
                    "type": "object",
                    "additionalProperties": true
                },
                "status": {
                    "type": "string",
                    "enum": [
                        "active",
                        "inactive",
                        "banned"
                    ],
                    "example": "active"
                }
            }
        },
        "handlers.UserInfo": {
            "type": "object",
            "properties": {
                "email": {
                    "type": "string",
                    "example": "<EMAIL>"
                },
                "first_name": {
                    "type": "string",
                    "example": "John"
                },
                "id": {
                    "type": "string",
                    "example": "550e8400-e29b-41d4-a716-446655440000"
                },
                "last_name": {
                    "type": "string",
                    "example": "Doe"
                },
                "organization_id": {
                    "type": "string",
                    "example": "550e8400-e29b-41d4-a716-************"
                },
                "permissions": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    },
                    "example": [
                        "[\"license.read\"",
                        " \"machine.read\"]"
                    ]
                },
                "roles": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    },
                    "example": [
                        "[\"admin\"]"
                    ]
                }
            }
        },
        "handlers.UserOrganizationCreateRequest": {
            "description": "Request payload for creating user-organization relationship with permissions",
            "type": "object",
            "required": [
                "allowed_actions",
                "resource_scope",
                "resource_type",
                "role",
                "user_id"
            ],
            "properties": {
                "allowed_actions": {
                    "type": "array",
                    "minItems": 1,
                    "items": {
                        "type": "string"
                    },
                    "example": [
                        "read",
                        "update"
                    ]
                },
                "expires_at": {
                    "type": "string",
                    "example": "2025-12-31T23:59:59Z"
                },
                "organization_id": {
                    "type": "string",
                    "example": "550e8400-e29b-41d4-a716-************"
                },
                "resource_id": {
                    "type": "string",
                    "example": "550e8400-e29b-41d4-a716-446655440002"
                },
                "resource_scope": {
                    "type": "string",
                    "enum": [
                        "specific",
                        "type_wildcard",
                        "global",
                        "owner"
                    ],
                    "example": "specific"
                },
                "resource_type": {
                    "type": "string",
                    "example": "product"
                },
                "role": {
                    "type": "string",
                    "enum": [
                        "admin",
                        "sales_agent",
                        "member",
                        "viewer",
                        "customer"
                    ],
                    "example": "member"
                },
                "user_id": {
                    "type": "string",
                    "example": "550e8400-e29b-41d4-a716-446655440000"
                }
            }
        },
        "handlers.UserOrganizationListResponse": {
            "description": "Paginated list of user-organization relationships",
            "type": "object",
            "properties": {
                "pagination": {
                    "$ref": "#/definitions/responses.PaginationMeta"
                },
                "user_organizations": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/handlers.UserOrganizationResponse"
                    }
                }
            }
        },
        "handlers.UserOrganizationResponse": {
            "description": "User-organization relationship with constraint-based permissions",
            "type": "object",
            "properties": {
                "active": {
                    "type": "boolean",
                    "example": true
                },
                "allowed_actions": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    },
                    "example": [
                        "read",
                        "update"
                    ]
                },
                "created_at": {
                    "type": "string",
                    "example": "2025-01-01T00:00:00Z"
                },
                "expires_at": {
                    "type": "string",
                    "example": "2025-12-31T23:59:59Z"
                },
                "granted_by": {
                    "type": "string",
                    "example": "550e8400-e29b-41d4-a716-446655440004"
                },
                "id": {
                    "type": "string",
                    "example": "550e8400-e29b-41d4-a716-446655440000"
                },
                "organization_id": {
                    "type": "string",
                    "example": "550e8400-e29b-41d4-a716-446655440002"
                },
                "resource_id": {
                    "type": "string",
                    "example": "550e8400-e29b-41d4-a716-446655440003"
                },
                "resource_scope": {
                    "type": "string",
                    "example": "specific"
                },
                "resource_type": {
                    "type": "string",
                    "example": "product"
                },
                "role": {
                    "type": "string",
                    "example": "member"
                },
                "updated_at": {
                    "type": "string",
                    "example": "2025-01-15T10:30:00Z"
                },
                "user_id": {
                    "type": "string",
                    "example": "550e8400-e29b-41d4-a716-************"
                }
            }
        },
        "handlers.UserOrganizationUpdateRequest": {
            "description": "Request payload for updating user-organization relationship permissions",
            "type": "object",
            "properties": {
                "active": {
                    "type": "boolean",
                    "example": true
                },
                "allowed_actions": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    },
                    "example": [
                        "*"
                    ]
                },
                "expires_at": {
                    "type": "string",
                    "example": "2026-12-31T23:59:59Z"
                },
                "resource_id": {
                    "type": "string",
                    "example": "550e8400-e29b-41d4-a716-446655440003"
                },
                "resource_scope": {
                    "type": "string",
                    "enum": [
                        "specific",
                        "type_wildcard",
                        "global",
                        "owner"
                    ],
                    "example": "type_wildcard"
                },
                "resource_type": {
                    "type": "string",
                    "example": "license"
                },
                "role": {
                    "type": "string",
                    "enum": [
                        "admin",
                        "sales_agent",
                        "member",
                        "viewer",
                        "customer"
                    ],
                    "example": "admin"
                }
            }
        },
        "handlers.ValidateLicenseByKeyRequest": {
            "type": "object",
            "required": [
                "key"
            ],
            "properties": {
                "client_version": {
                    "type": "string",
                    "example": "1.0.0"
                },
                "environment": {
                    "type": "string",
                    "example": "production"
                },
                "fingerprint": {
                    "type": "string",
                    "example": "fp-mac-12345678"
                },
                "key": {
                    "type": "string",
                    "example": "LIC-12345-ABCDE-67890-FGHIJ"
                },
                "machine_info": {
                    "type": "object",
                    "additionalProperties": true
                }
            }
        },
        "handlers.ValidateLicenseRequest": {
            "type": "object",
            "required": [
                "license_key"
            ],
            "properties": {
                "environment": {
                    "type": "string",
                    "example": "production"
                },
                "license_key": {
                    "type": "string",
                    "example": "LIC-12345-ABCDE-67890-FGHIJ"
                },
                "machine_fingerprint": {
                    "type": "string",
                    "example": "fp-mac-12345678"
                },
                "machine_info": {
                    "type": "object",
                    "additionalProperties": true
                }
            }
        },
        "handlers.ValidationResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "string",
                    "example": "VALID"
                },
                "expires_at": {
                    "type": "string"
                },
                "license": {
                    "$ref": "#/definitions/handlers.LicenseResponse"
                },
                "message": {
                    "type": "string",
                    "example": "License is valid"
                },
                "meta": {
                    "type": "object",
                    "additionalProperties": true
                },
                "policy": {
                    "type": "object",
                    "additionalProperties": true
                },
                "ttl": {
                    "type": "integer",
                    "example": 3600
                },
                "valid": {
                    "type": "boolean",
                    "example": true
                }
            }
        },
        "responses.ErrorCode": {
            "type": "string",
            "enum": [
                "UNAUTHORIZED",
                "INVALID_TOKEN",
                "TOKEN_EXPIRED",
                "INSUFFICIENT_SCOPE",
                "FORBIDDEN",
                "INSUFFICIENT_PERMISSIONS",
                "VALIDATION_FAILED",
                "INVALID_REQUEST",
                "MISSING_PARAMETER",
                "INVALID_PARAMETER",
                "NOT_FOUND",
                "RESOURCE_NOT_FOUND",
                "CONFLICT",
                "RESOURCE_EXISTS",
                "LICENSE_EXPIRED",
                "LICENSE_SUSPENDED",
                "LICENSE_INVALID",
                "LICENSE_NOT_FOUND",
                "MACHINE_HEARTBEAT_DEAD",
                "TOO_MANY_MACHINES",
                "NO_MACHINE",
                "CHECKOUT_ALGORITHM_INVALID",
                "CHECKOUT_INCLUDE_INVALID",
                "CHECKOUT_TTL_INVALID",
                "INTERNAL_ERROR",
                "SERVICE_UNAVAILABLE",
                "RATE_LIMIT_EXCEEDED"
            ],
            "x-enum-varnames": [
                "ErrorCodeUnauthorized",
                "ErrorCodeInvalidToken",
                "ErrorCodeTokenExpired",
                "ErrorCodeInsufficientScope",
                "ErrorCodeForbidden",
                "ErrorCodeInsufficientPermissions",
                "ErrorCodeValidationFailed",
                "ErrorCodeInvalidRequest",
                "ErrorCodeMissingParameter",
                "ErrorCodeInvalidParameter",
                "ErrorCodeNotFound",
                "ErrorCodeResourceNotFound",
                "ErrorCodeConflict",
                "ErrorCodeResourceExists",
                "ErrorCodeLicenseExpired",
                "ErrorCodeLicenseSuspended",
                "ErrorCodeLicenseInvalid",
                "ErrorCodeLicenseNotFound",
                "ErrorCodeMachineHeartbeatDead",
                "ErrorCodeTooManyMachines",
                "ErrorCodeNoMachine",
                "ErrorCodeCheckoutAlgorithmInvalid",
                "ErrorCodeCheckoutIncludeInvalid",
                "ErrorCodeCheckoutTTLInvalid",
                "ErrorCodeInternalError",
                "ErrorCodeServiceUnavailable",
                "ErrorCodeRateLimitExceeded"
            ]
        },
        "responses.ErrorDetail": {
            "type": "object",
            "properties": {
                "code": {
                    "$ref": "#/definitions/responses.ErrorCode"
                },
                "detail": {
                    "type": "string"
                },
                "meta": {
                    "type": "object",
                    "additionalProperties": true
                },
                "source": {
                    "type": "object",
                    "additionalProperties": true
                },
                "title": {
                    "type": "string"
                }
            }
        },
        "responses.ErrorResponse": {
            "type": "object",
            "properties": {
                "errors": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/responses.ErrorDetail"
                    }
                }
            }
        },
        "responses.PaginationMeta": {
            "type": "object",
            "properties": {
                "page": {
                    "type": "integer"
                },
                "per_page": {
                    "type": "integer"
                },
                "total": {
                    "type": "integer"
                },
                "total_pages": {
                    "type": "integer"
                }
            }
        }
    },
    "securityDefinitions": {
        "ApiKeyAuth": {
            "description": "API key for authentication",
            "type": "apiKey",
            "name": "X-API-Key",
            "in": "header"
        },
        "BearerAuth": {
            "description": "Type \"Bearer\" followed by a space and JWT token.",
            "type": "apiKey",
            "name": "Authorization",
            "in": "header"
        },
        "LicenseKeyAuth": {
            "description": "License key for validation endpoints",
            "type": "apiKey",
            "name": "X-License-Key",
            "in": "header"
        }
    },
    "tags": [
        {
            "description": "Authentication and authorization endpoints",
            "name": "Authentication"
        },
        {
            "description": "License management and validation",
            "name": "Licenses"
        },
        {
            "description": "Account management",
            "name": "Accounts"
        },
        {
            "description": "Product management",
            "name": "Products"
        },
        {
            "description": "Policy configuration",
            "name": "Policies"
        },
        {
            "description": "Machine registration and tracking",
            "name": "Machines"
        },
        {
            "description": "User management",
            "name": "Users"
        },
        {
            "description": "System health and monitoring",
            "name": "Health"
        },
        {
            "description": "System metrics and analytics",
            "name": "Metrics"
        }
    ]
}`

// SwaggerInfo holds exported Swagger Info so clients can modify it
var SwaggerInfo = &swag.Spec{
	Version:          "1.0",
	Host:             "localhost:8080",
	BasePath:         "/v1",
	Schemes:          []string{},
	Title:            "GoKeys License Management API",
	Description:      "Enterprise License Management Platform with comprehensive license validation, machine tracking, and policy management capabilities.",
	InfoInstanceName: "swagger",
	SwaggerTemplate:  docTemplate,
	LeftDelim:        "{{",
	RightDelim:       "}}",
}

func init() {
	swag.Register(SwaggerInfo.InstanceName(), SwaggerInfo)
}

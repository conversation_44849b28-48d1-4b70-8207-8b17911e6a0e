openapi: 3.0.3
info:
  title: GoKeys License Management API
  description: |
    Enterprise License Management Platform with comprehensive license validation,
    machine tracking, policy management, and webhook integration capabilities.
    
    ## Authentication
    
    The API supports multiple authentication methods:
    - **Bearer Token**: JWT tokens for user authentication
    - **API Key**: Custom API keys for service-to-service communication  
    - **License Key**: License keys for validation endpoints
    
    ## Rate Limiting
    
    API requests are subject to rate limiting to ensure fair usage and system stability.
    Rate limits are applied per authentication method and endpoint.
    
    ## Webhooks
    
    The platform supports webhook notifications for real-time event processing.
    Events are broadcast for license validation, machine registration, policy changes, and more.
    
  version: "1.0"
  contact:
    name: GoKeys API Support
    url: https://gokeys.com/support
    email: <EMAIL>
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT
  termsOfService: https://gokeys.com/terms

servers:
  - url: http://localhost:8080/api/v1
    description: Development server
  - url: https://api.gokeys.com/v1
    description: Production server

security:
  - BearerAuth: []
  - ApiKeyAuth: []
  - LicenseKeyAuth: []

tags:
  - name: Authentication
    description: Authentication and authorization endpoints
  - name: Accounts
    description: Account management and configuration
  - name: Licenses
    description: License management and validation
  - name: Machines
    description: Machine registration and tracking
  - name: Products
    description: Product management and configuration
  - name: Policies
    description: Policy configuration and management
  - name: Users
    description: User management and permissions
  - name: Groups
    description: Group management and organization
  - name: Plans
    description: Subscription plan management
  - name: Entitlements
    description: Feature entitlement management
  - name: Webhooks
    description: Webhook endpoint management
  - name: Health
    description: System health and monitoring
  - name: Metrics
    description: System metrics and analytics

paths:
  # Account Management
  /accounts:
    get:
      tags: [Accounts]
      summary: List accounts
      description: Get a paginated list of all accounts (admin only)
      security:
        - BearerAuth: []
      parameters:
        - name: page
          in: query
          description: Page number (default 1)
          schema:
            type: integer
            minimum: 1
            default: 1
          example: 1
        - name: per_page
          in: query
          description: Items per page (default 25, max 100)
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 25
          example: 25
        - name: search
          in: query
          description: Search term for account name or email
          schema:
            type: string
          example: "acme"
        - name: sort_by
          in: query
          description: Sort field
          schema:
            type: string
            enum: [name, email, created_at, updated_at]
            default: created_at
          example: "name"
        - name: sort_order
          in: query
          description: Sort order
          schema:
            type: string
            enum: [ASC, DESC]
            default: DESC
          example: "ASC"
      responses:
        '200':
          description: List of accounts retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AccountListResponse'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '500':
          $ref: '#/components/responses/InternalServerError'
    post:
      tags: [Accounts]
      summary: Create account
      description: Create a new account for license management
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AccountCreateRequest'
      responses:
        '201':
          description: Account created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AccountResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '409':
          $ref: '#/components/responses/Conflict'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /accounts/{id}:
    get:
      tags: [Accounts]
      summary: Get account by ID
      description: Retrieve detailed information about a specific account
      security:
        - BearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          description: Account ID
          schema:
            type: string
            format: uuid
          example: "550e8400-e29b-41d4-a716-************"
      responses:
        '200':
          description: Account retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AccountResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'
    put:
      tags: [Accounts]
      summary: Update account
      description: Update an existing account's properties
      security:
        - BearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          description: Account ID
          schema:
            type: string
            format: uuid
          example: "550e8400-e29b-41d4-a716-************"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AccountUpdateRequest'
      responses:
        '200':
          description: Account updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AccountResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'
    delete:
      tags: [Accounts]
      summary: Delete account
      description: Delete an account (soft delete)
      security:
        - BearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          description: Account ID
          schema:
            type: string
            format: uuid
          example: "550e8400-e29b-41d4-a716-************"
      responses:
        '204':
          description: Account deleted successfully
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'

  # License Management
  /licenses:
    get:
      tags: [Licenses]
      summary: List licenses
      description: Get a paginated list of licenses for the authenticated account
      security:
        - BearerAuth: []
      parameters:
        - name: page
          in: query
          description: Page number (default 1)
          schema:
            type: integer
            minimum: 1
            default: 1
          example: 1
        - name: page_size
          in: query
          description: Page size (default 20, max 100)
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 20
          example: 20
        - name: sort_by
          in: query
          description: Sort field
          schema:
            type: string
            enum: [name, key, status, created_at, updated_at, expires_at]
            default: created_at
          example: "created_at"
        - name: sort_order
          in: query
          description: Sort order
          schema:
            type: string
            enum: [ASC, DESC]
            default: DESC
          example: "DESC"
        - name: search
          in: query
          description: Search term for license name or key
          schema:
            type: string
          example: "enterprise"
        - name: status
          in: query
          description: Filter by license status
          schema:
            type: string
            enum: [ACTIVE, SUSPENDED, EXPIRED, REVOKED]
          example: "ACTIVE"
        - name: product_id
          in: query
          description: Filter by product ID
          schema:
            type: string
            format: uuid
          example: "550e8400-e29b-41d4-a716-************"
        - name: policy_id
          in: query
          description: Filter by policy ID
          schema:
            type: string
            format: uuid
          example: "550e8400-e29b-41d4-a716-************"
        - name: user_id
          in: query
          description: Filter by user ID
          schema:
            type: string
            format: uuid
          example: "550e8400-e29b-41d4-a716-************"
      responses:
        '200':
          description: List of licenses retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LicenseListResponse'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/InternalServerError'
    post:
      tags: [Licenses]
      summary: Create license
      description: Create a new license for a product with specified policy
      security:
        - BearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LicenseCreateRequest'
      responses:
        '201':
          description: License created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LicenseResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '409':
          $ref: '#/components/responses/Conflict'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /licenses/{id}:
    get:
      tags: [Licenses]
      summary: Get license by ID
      description: Retrieve detailed information about a specific license
      security:
        - BearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          description: License ID
          schema:
            type: string
            format: uuid
          example: "550e8400-e29b-41d4-a716-************"
      responses:
        '200':
          description: License retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LicenseResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'
    put:
      tags: [Licenses]
      summary: Update license
      description: Update an existing license's properties
      security:
        - BearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          description: License ID
          schema:
            type: string
            format: uuid
          example: "550e8400-e29b-41d4-a716-************"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LicenseUpdateRequest'
      responses:
        '200':
          description: License updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LicenseResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'
    delete:
      tags: [Licenses]
      summary: Delete license
      description: Delete a license by ID (soft delete)
      security:
        - BearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          description: License ID
          schema:
            type: string
            format: uuid
          example: "550e8400-e29b-41d4-a716-************"
      responses:
        '204':
          description: License deleted successfully
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /licenses/validate:
    post:
      tags: [Licenses]
      summary: Validate license
      description: Validates a license key and returns detailed validation result including machine tracking
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ValidateLicenseRequest'
      responses:
        '200':
          description: License validation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidateLicenseResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '403':
          description: License validation failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidateLicenseResponse'
        '500':
          $ref: '#/components/responses/InternalServerError'
    get:
      tags: [Licenses]
      summary: Validate license (GET)
      description: Validates a license key via GET request with query parameters
      parameters:
        - name: license_key
          in: query
          required: true
          description: License key to validate
          schema:
            type: string
          example: "LIC-12345-ABCDE-67890-FGHIJ"
        - name: machine_fingerprint
          in: query
          description: Machine fingerprint for tracking
          schema:
            type: string
          example: "fp-mac-12345678"
        - name: environment
          in: query
          description: Environment context
          schema:
            type: string
          example: "production"
      responses:
        '200':
          description: License validation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidateLicenseResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '403':
          description: License validation failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidateLicenseResponse'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /licenses/quick-validate:
    get:
      tags: [Licenses]
      summary: Quick license validation
      description: Performs a quick validation of a license key without detailed tracking
      parameters:
        - name: license_key
          in: query
          required: true
          description: License key to validate
          schema:
            type: string
          example: "LIC-12345-ABCDE-67890-FGHIJ"
      responses:
        '200':
          description: License is valid
          content:
            application/json:
              schema:
                type: object
                properties:
                  valid:
                    type: boolean
                    example: true
                  validation_time:
                    type: string
                    format: date-time
                    example: "2025-07-15T10:30:00Z"
        '400':
          $ref: '#/components/responses/BadRequest'
        '403':
          description: License is invalid
          content:
            application/json:
              schema:
                type: object
                properties:
                  valid:
                    type: boolean
                    example: false
                  validation_time:
                    type: string
                    format: date-time
                    example: "2025-07-15T10:30:00Z"
        '500':
          $ref: '#/components/responses/InternalServerError'

  /licenses/info:
    get:
      tags: [Licenses]
      summary: Get license information
      description: Retrieves detailed information about a license without performing validation
      parameters:
        - name: license_key
          in: query
          required: true
          description: License key to get info for
          schema:
            type: string
          example: "LIC-12345-ABCDE-67890-FGHIJ"
      responses:
        '200':
          description: License information retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LicenseInfoResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /licenses/invalidate-cache:
    post:
      tags: [Licenses]
      summary: Invalidate license cache
      description: Invalidates the cache for a specific license key
      parameters:
        - name: license_key
          in: query
          description: License key to invalidate cache for
          schema:
            type: string
          example: "LIC-12345-ABCDE-67890-FGHIJ"
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                license_key:
                  type: string
                  description: License key to invalidate cache for
                  example: "LIC-12345-ABCDE-67890-FGHIJ"
              required:
                - license_key
      responses:
        '200':
          description: Cache invalidated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: "Cache invalidated successfully"
                  license_key:
                    type: string
                    example: "LIC-12345-ABCDE-67890-FGHIJ"
        '400':
          $ref: '#/components/responses/BadRequest'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /licenses/{id}/validate:
    get:
      tags: [Licenses]
      summary: Validate license by ID
      description: Validates a license by ID with scope and machine tracking
      security:
        - BearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          description: License ID
          schema:
            type: string
            format: uuid
          example: "550e8400-e29b-41d4-a716-************"
        - name: machine_fingerprint
          in: query
          description: Machine fingerprint for tracking
          schema:
            type: string
          example: "fp-mac-12345678"
        - name: environment
          in: query
          description: Environment context
          schema:
            type: string
          example: "production"
      responses:
        '200':
          description: License validation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidateLicenseResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          description: License validation failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidateLicenseResponse'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /licenses/{id}/quick-validate:
    get:
      tags: [Licenses]
      summary: Quick validate license by ID
      description: Performs quick validation of a license by ID without detailed tracking
      security:
        - BearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          description: License ID
          schema:
            type: string
            format: uuid
          example: "550e8400-e29b-41d4-a716-************"
      responses:
        '200':
          description: License validation result
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: object
                    properties:
                      id:
                        type: string
                        format: uuid
                      type:
                        type: string
                        example: "licenses"
                      attributes:
                        type: object
                        properties:
                          key:
                            type: string
                            example: "LIC-12345-ABCDE-67890-FGHIJ"
                          name:
                            type: string
                            example: "Enterprise License"
                          status:
                            type: string
                            example: "ACTIVE"
                          suspended:
                            type: boolean
                            example: false
                          expires_at:
                            type: string
                            format: date-time
                            nullable: true
                          created_at:
                            type: string
                            format: date-time
                          updated_at:
                            type: string
                            format: date-time
                  meta:
                    type: object
                    properties:
                      ts:
                        type: string
                        format: date-time
                      valid:
                        type: boolean
                      detail:
                        type: string
                      code:
                        type: string
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /licenses/{id}/checkout:
    get:
      tags: [Licenses]
      summary: Checkout license certificate (download)
      description: Downloads a signed license certificate file
      security:
        - BearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          description: License ID
          schema:
            type: string
            format: uuid
          example: "550e8400-e29b-41d4-a716-************"
        - name: encrypt
          in: query
          description: Whether to encrypt the certificate
          schema:
            type: boolean
            default: false
          example: false
        - name: algorithm
          in: query
          description: Cryptographic algorithm to use
          schema:
            type: string
            enum: [rsa-2048, ed25519, aes-256-gcm]
            default: rsa-2048
          example: "rsa-2048"
        - name: ttl
          in: query
          description: Time-to-live in seconds for the certificate
          schema:
            type: integer
            minimum: 0
          example: 3600
        - name: include
          in: query
          description: Additional data to include in certificate
          schema:
            type: array
            items:
              type: string
              enum: [policy, product, account, user, machines]
          style: form
          explode: true
          example: ["policy", "product"]
      responses:
        '200':
          description: License certificate file
          content:
            application/octet-stream:
              schema:
                type: string
                format: binary
          headers:
            Content-Disposition:
              description: Attachment with filename
              schema:
                type: string
                example: 'attachment; filename="550e8400-e29b-41d4-a716-************.lic"'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'
    post:
      tags: [Licenses]
      summary: Checkout license certificate (JSON)
      description: Generates a signed license certificate and returns as JSON
      security:
        - BearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          description: License ID
          schema:
            type: string
            format: uuid
          example: "550e8400-e29b-41d4-a716-************"
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                meta:
                  type: object
                  properties:
                    encrypt:
                      type: boolean
                      default: false
                      description: Whether to encrypt the certificate
                    algorithm:
                      type: string
                      enum: [rsa-2048, ed25519, aes-256-gcm]
                      default: rsa-2048
                      description: Cryptographic algorithm to use
                    ttl:
                      type: integer
                      minimum: 0
                      description: Time-to-live in seconds for the certificate
                    include:
                      type: array
                      items:
                        type: string
                        enum: [policy, product, account, user, machines]
                      description: Additional data to include in certificate
      responses:
        '200':
          description: License certificate generated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: object
                    properties:
                      id:
                        type: string
                        format: uuid
                      certificate:
                        type: string
                        description: Base64-encoded certificate data
                      algorithm:
                        type: string
                      encrypted:
                        type: boolean
                  meta:
                    type: object
                    properties:
                      ts:
                        type: string
                        format: date-time
                      ttl:
                        type: integer
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'

  # Machine Management
  /machines:
    get:
      tags: [Machines]
      summary: List machines
      description: Get a paginated list of machines for the authenticated account
      security:
        - BearerAuth: []
      parameters:
        - name: page
          in: query
          description: Page number (default 1)
          schema:
            type: integer
            minimum: 1
            default: 1
          example: 1
        - name: per_page
          in: query
          description: Items per page (default 25, max 100)
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 25
          example: 25
        - name: sort_by
          in: query
          description: Sort field
          schema:
            type: string
            enum: [name, fingerprint, platform, status, created_at, updated_at, last_seen]
            default: created_at
          example: "created_at"
        - name: sort_order
          in: query
          description: Sort order
          schema:
            type: string
            enum: [ASC, DESC]
            default: DESC
          example: "DESC"
        - name: search
          in: query
          description: Search term for machine name, fingerprint, or hostname
          schema:
            type: string
          example: "server"
        - name: license_id
          in: query
          description: Filter by license ID
          schema:
            type: string
            format: uuid
          example: "550e8400-e29b-41d4-a716-************"
        - name: policy_id
          in: query
          description: Filter by policy ID
          schema:
            type: string
            format: uuid
          example: "550e8400-e29b-41d4-a716-************"
        - name: group_id
          in: query
          description: Filter by group ID
          schema:
            type: string
            format: uuid
          example: "550e8400-e29b-41d4-a716-************"
        - name: owner_id
          in: query
          description: Filter by owner ID
          schema:
            type: string
            format: uuid
          example: "550e8400-e29b-41d4-a716-************"
        - name: status
          in: query
          description: Filter by machine status
          schema:
            type: string
            enum: [ACTIVE, INACTIVE, BANNED, DEAD]
          example: "ACTIVE"
        - name: platform
          in: query
          description: Filter by platform
          schema:
            type: string
            enum: [windows, macos, linux, android, ios]
          example: "linux"
      responses:
        '200':
          description: List of machines retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MachineListResponse'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/InternalServerError'
    post:
      tags: [Machines]
      summary: Create machine
      description: Register a new machine for license tracking
      security:
        - BearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MachineCreateRequest'
      responses:
        '201':
          description: Machine created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MachineResponse'
          headers:
            Location:
              description: URL of the created machine
              schema:
                type: string
                example: "/api/v1/machines/550e8400-e29b-41d4-a716-************"
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '409':
          $ref: '#/components/responses/Conflict'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /machines/{id}:
    get:
      tags: [Machines]
      summary: Get machine by ID
      description: Retrieve detailed information about a specific machine (supports ID or fingerprint)
      security:
        - BearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          description: Machine ID or fingerprint
          schema:
            type: string
          example: "550e8400-e29b-41d4-a716-************"
      responses:
        '200':
          description: Machine retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MachineResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'
    put:
      tags: [Machines]
      summary: Update machine
      description: Update an existing machine's properties
      security:
        - BearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          description: Machine ID
          schema:
            type: string
            format: uuid
          example: "550e8400-e29b-41d4-a716-************"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MachineUpdateRequest'
      responses:
        '200':
          description: Machine updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MachineResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'
    delete:
      tags: [Machines]
      summary: Delete machine
      description: Delete a machine (soft delete)
      security:
        - BearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          description: Machine ID
          schema:
            type: string
            format: uuid
          example: "550e8400-e29b-41d4-a716-************"
      responses:
        '204':
          description: Machine deleted successfully
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /machines/{id}/actions/heartbeats/ping:
    post:
      tags: [Machines]
      summary: Machine heartbeat ping
      description: Send a heartbeat ping from a machine to indicate it's alive
      security:
        - BearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          description: Machine ID
          schema:
            type: string
            format: uuid
          example: "550e8400-e29b-41d4-a716-************"
      responses:
        '200':
          description: Heartbeat ping successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    $ref: '#/components/schemas/MachineResponse'
                  meta:
                    type: object
                    properties:
                      ts:
                        type: string
                        format: date-time
                        description: Timestamp of the ping
                      ping_time:
                        type: string
                        format: date-time
                        description: Time when ping was processed
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /machines/{id}/actions/heartbeats/reset:
    post:
      tags: [Machines]
      summary: Reset machine heartbeat
      description: Reset the heartbeat status of a machine (marks as dead)
      security:
        - BearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          description: Machine ID
          schema:
            type: string
            format: uuid
          example: "550e8400-e29b-41d4-a716-************"
      responses:
        '200':
          description: Heartbeat reset successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    $ref: '#/components/schemas/MachineResponse'
                  meta:
                    type: object
                    properties:
                      ts:
                        type: string
                        format: date-time
                        description: Timestamp of the reset
                      reset_time:
                        type: string
                        format: date-time
                        description: Time when reset was processed
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /machines/{id}/checkout:
    get:
      tags: [Machines]
      summary: Checkout machine certificate (download)
      description: Downloads a signed machine certificate file
      security:
        - BearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          description: Machine ID
          schema:
            type: string
            format: uuid
          example: "550e8400-e29b-41d4-a716-************"
        - name: encrypt
          in: query
          description: Whether to encrypt the certificate
          schema:
            type: boolean
            default: false
          example: false
        - name: algorithm
          in: query
          description: Cryptographic algorithm to use
          schema:
            type: string
            enum: [rsa-2048, ed25519, aes-256-gcm]
            default: rsa-2048
          example: "rsa-2048"
        - name: ttl
          in: query
          description: Time-to-live in seconds for the certificate
          schema:
            type: integer
            minimum: 0
          example: 3600
        - name: include
          in: query
          description: Additional data to include in certificate
          schema:
            type: array
            items:
              type: string
              enum: [license, policy, product, account, user]
          style: form
          explode: true
          example: ["license", "policy"]
      responses:
        '200':
          description: Machine certificate file
          content:
            application/octet-stream:
              schema:
                type: string
                format: binary
          headers:
            Content-Disposition:
              description: Attachment with filename
              schema:
                type: string
                example: 'attachment; filename="550e8400-e29b-41d4-a716-************.lic"'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'
    post:
      tags: [Machines]
      summary: Checkout machine certificate (JSON)
      description: Generates a signed machine certificate and returns as JSON
      security:
        - BearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          description: Machine ID
          schema:
            type: string
            format: uuid
          example: "550e8400-e29b-41d4-a716-************"
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                meta:
                  type: object
                  properties:
                    encrypt:
                      type: boolean
                      default: false
                      description: Whether to encrypt the certificate
                    algorithm:
                      type: string
                      enum: [rsa-2048, ed25519, aes-256-gcm]
                      default: rsa-2048
                      description: Cryptographic algorithm to use
                    ttl:
                      type: integer
                      minimum: 0
                      description: Time-to-live in seconds for the certificate
                    include:
                      type: array
                      items:
                        type: string
                        enum: [license, policy, product, account, user]
                      description: Additional data to include in certificate
      responses:
        '200':
          description: Machine certificate generated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: object
                    properties:
                      id:
                        type: string
                        format: uuid
                      certificate:
                        type: string
                        description: Base64-encoded certificate data
                      algorithm:
                        type: string
                      encrypted:
                        type: boolean
                  meta:
                    type: object
                    properties:
                      ts:
                        type: string
                        format: date-time
                      ttl:
                        type: integer
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'

  # Product Management
  /products:
    get:
      tags: [Products]
      summary: List products
      description: Get a paginated list of products for the authenticated account
      security:
        - BearerAuth: []
      parameters:
        - name: page
          in: query
          description: Page number (default 1)
          schema:
            type: integer
            minimum: 1
            default: 1
          example: 1
        - name: per_page
          in: query
          description: Items per page (default 25, max 100)
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 25
          example: 25
        - name: sort_by
          in: query
          description: Sort field
          schema:
            type: string
            enum: [name, code, created_at, updated_at]
            default: created_at
          example: "created_at"
        - name: sort_order
          in: query
          description: Sort order
          schema:
            type: string
            enum: [ASC, DESC]
            default: DESC
          example: "DESC"
        - name: search
          in: query
          description: Search term for product name or code
          schema:
            type: string
          example: "software"
        - name: distribution_strategy
          in: query
          description: Filter by distribution strategy
          schema:
            type: string
            enum: [licensed, open]
          example: "licensed"
        - name: environment_id
          in: query
          description: Filter by environment ID
          schema:
            type: string
            format: uuid
          example: "550e8400-e29b-41d4-a716-************"
      responses:
        '200':
          description: List of products retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProductListResponse'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/InternalServerError'
    post:
      tags: [Products]
      summary: Create product
      description: Create a new product for license management
      security:
        - BearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ProductCreateRequest'
      responses:
        '201':
          description: Product created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProductResponse'
          headers:
            Location:
              description: URL of the created product
              schema:
                type: string
                example: "/api/v1/products/550e8400-e29b-41d4-a716-************"
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '409':
          $ref: '#/components/responses/Conflict'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /products/{id}:
    get:
      tags: [Products]
      summary: Get product by ID
      description: Retrieve detailed information about a specific product
      security:
        - BearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          description: Product ID
          schema:
            type: string
            format: uuid
          example: "550e8400-e29b-41d4-a716-************"
      responses:
        '200':
          description: Product retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProductResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'
    put:
      tags: [Products]
      summary: Update product
      description: Update an existing product's properties
      security:
        - BearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          description: Product ID
          schema:
            type: string
            format: uuid
          example: "550e8400-e29b-41d4-a716-************"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ProductUpdateRequest'
      responses:
        '200':
          description: Product updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProductResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'
    delete:
      tags: [Products]
      summary: Delete product
      description: Delete a product (soft delete)
      security:
        - BearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          description: Product ID
          schema:
            type: string
            format: uuid
          example: "550e8400-e29b-41d4-a716-************"
      responses:
        '204':
          description: Product deleted successfully
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'

  # Policy Management
  /policies:
    get:
      tags: [Policies]
      summary: List policies
      description: Get a paginated list of policies for the authenticated account
      security:
        - BearerAuth: []
      parameters:
        - name: page
          in: query
          description: Page number (default 1)
          schema:
            type: integer
            minimum: 1
            default: 1
          example: 1
        - name: per_page
          in: query
          description: Items per page (default 25, max 100)
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 25
          example: 25
        - name: sort_by
          in: query
          description: Sort field
          schema:
            type: string
            enum: [name, created_at, updated_at]
            default: created_at
          example: "created_at"
        - name: sort_order
          in: query
          description: Sort order
          schema:
            type: string
            enum: [ASC, DESC]
            default: DESC
          example: "DESC"
        - name: search
          in: query
          description: Search term for policy name
          schema:
            type: string
          example: "enterprise"
        - name: product_id
          in: query
          description: Filter by product ID
          schema:
            type: string
            format: uuid
          example: "550e8400-e29b-41d4-a716-************"
        - name: environment_id
          in: query
          description: Filter by environment ID
          schema:
            type: string
            format: uuid
          example: "550e8400-e29b-41d4-a716-************"
      responses:
        '200':
          description: List of policies retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PolicyListResponse'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/InternalServerError'
    post:
      tags: [Policies]
      summary: Create policy
      description: Create a new policy for license management
      security:
        - BearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PolicyCreateRequest'
      responses:
        '201':
          description: Policy created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PolicyResponse'
          headers:
            Location:
              description: URL of the created policy
              schema:
                type: string
                example: "/api/v1/policies/550e8400-e29b-41d4-a716-************"
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '409':
          $ref: '#/components/responses/Conflict'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /policies/{id}:
    get:
      tags: [Policies]
      summary: Get policy by ID
      description: Retrieve detailed information about a specific policy
      security:
        - BearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          description: Policy ID
          schema:
            type: string
            format: uuid
          example: "550e8400-e29b-41d4-a716-************"
      responses:
        '200':
          description: Policy retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PolicyResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'
    put:
      tags: [Policies]
      summary: Update policy
      description: Update an existing policy's properties
      security:
        - BearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          description: Policy ID
          schema:
            type: string
            format: uuid
          example: "550e8400-e29b-41d4-a716-************"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PolicyUpdateRequest'
      responses:
        '200':
          description: Policy updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PolicyResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'
    delete:
      tags: [Policies]
      summary: Delete policy
      description: Delete a policy (soft delete)
      security:
        - BearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          description: Policy ID
          schema:
            type: string
            format: uuid
          example: "550e8400-e29b-41d4-a716-************"
      responses:
        '204':
          description: Policy deleted successfully
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'

  # Health and Monitoring
  /health:
    get:
      tags: [Health]
      summary: Health check
      description: Get the overall health status of the API
      responses:
        '200':
          description: Service is healthy
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: "healthy"
                  timestamp:
                    type: string
                    format: date-time
                    example: "2025-07-15T10:30:00Z"
                  version:
                    type: string
                    example: "1.0.0"
                  services:
                    type: object
                    properties:
                      database:
                        type: string
                        example: "healthy"
                      cache:
                        type: string
                        example: "healthy"
                      events:
                        type: string
                        example: "healthy"
        '503':
          description: Service is unhealthy
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: "unhealthy"
                  timestamp:
                    type: string
                    format: date-time
                  errors:
                    type: array
                    items:
                      type: string

  /health/live:
    get:
      tags: [Health]
      summary: Liveness check
      description: Check if the service is alive and accepting requests
      responses:
        '200':
          description: Service is alive
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: "alive"
                  timestamp:
                    type: string
                    format: date-time

  /health/ready:
    get:
      tags: [Health]
      summary: Readiness check
      description: Check if the service is ready to handle requests
      responses:
        '200':
          description: Service is ready
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: "ready"
                  timestamp:
                    type: string
                    format: date-time
        '503':
          description: Service is not ready
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: "not ready"
                  timestamp:
                    type: string
                    format: date-time
                  errors:
                    type: array
                    items:
                      type: string

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: JWT token for user authentication
    ApiKeyAuth:
      type: apiKey
      in: header
      name: X-API-Key
      description: API key for service-to-service authentication
    LicenseKeyAuth:
      type: apiKey
      in: header
      name: X-License-Key
      description: License key for validation endpoints

  schemas:
    # Account Schemas
    AccountCreateRequest:
      type: object
      required: [name, slug, email]
      properties:
        name:
          type: string
          minLength: 1
          maxLength: 255
          description: Account name
          example: "Acme Corporation"
        slug:
          type: string
          minLength: 1
          maxLength: 255
          pattern: '^[a-zA-Z0-9-_]+$'
          description: Account slug (alphanumeric, dash, underscore only)
          example: "acme-corp"
        email:
          type: string
          format: email
          description: Contact email for the account
          example: "<EMAIL>"
        metadata:
          type: object
          additionalProperties: true
          description: Custom metadata for the account
          example:
            plan: "enterprise"
            industry: "technology"

    AccountUpdateRequest:
      type: object
      properties:
        name:
          type: string
          minLength: 1
          maxLength: 255
          description: Account name
          example: "Updated Acme Corporation"
        slug:
          type: string
          minLength: 1
          maxLength: 255
          pattern: '^[a-zA-Z0-9-_]+$'
          description: Account slug (alphanumeric, dash, underscore only)
          example: "updated-acme"
        metadata:
          type: object
          additionalProperties: true
          description: Custom metadata for the account

    AccountResponse:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: Account ID
          example: "550e8400-e29b-41d4-a716-************"
        name:
          type: string
          description: Account name
          example: "Acme Corporation"
        slug:
          type: string
          description: Account slug
          example: "acme-corp"
        email:
          type: string
          format: email
          description: Contact email for the account
          example: "<EMAIL>"
        metadata:
          type: object
          additionalProperties: true
          description: Custom metadata for the account
        created_at:
          type: string
          format: date-time
          description: Account creation timestamp
          example: "2025-01-01T00:00:00Z"
        updated_at:
          type: string
          format: date-time
          description: Account last update timestamp
          example: "2025-01-15T10:30:00Z"

    AccountListResponse:
      type: object
      properties:
        accounts:
          type: array
          items:
            $ref: '#/components/schemas/AccountResponse'
        pagination:
          $ref: '#/components/schemas/PaginationInfo'

    # License Schemas
    LicenseCreateRequest:
      type: object
      required: [name, policy_id]
      properties:
        name:
          type: string
          minLength: 1
          maxLength: 255
          description: License name
          example: "Enterprise License"
        key:
          type: string
          description: Custom license key (auto-generated if not provided)
          example: "LIC-12345-ABCDE-67890-FGHIJ"
        policy_id:
          type: string
          format: uuid
          description: Policy ID that defines license terms
          example: "550e8400-e29b-41d4-a716-************"
        user_id:
          type: string
          format: uuid
          description: User ID for user-specific licenses
          example: "550e8400-e29b-41d4-a716-************"
        expires_at:
          type: string
          format: date-time
          description: License expiration timestamp
          example: "2025-12-31T23:59:59Z"
        metadata:
          type: object
          additionalProperties: true
          description: Custom metadata for the license
          example:
            department: "engineering"
            cost_center: "R&D"

    LicenseUpdateRequest:
      type: object
      properties:
        name:
          type: string
          minLength: 1
          maxLength: 255
          description: License name
          example: "Updated Enterprise License"
        status:
          type: string
          enum: [ACTIVE, SUSPENDED, EXPIRED, REVOKED]
          description: License status
          example: "ACTIVE"
        expires_at:
          type: string
          format: date-time
          description: License expiration timestamp
          example: "2025-12-31T23:59:59Z"
        metadata:
          type: object
          additionalProperties: true
          description: Custom metadata for the license

    LicenseResponse:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: License ID
          example: "550e8400-e29b-41d4-a716-************"
        name:
          type: string
          description: License name
          example: "Enterprise License"
        key:
          type: string
          description: License key
          example: "LIC-12345-ABCDE-67890-FGHIJ"
        status:
          type: string
          enum: [ACTIVE, SUSPENDED, EXPIRED, REVOKED]
          description: License status
          example: "ACTIVE"
        policy_id:
          type: string
          format: uuid
          description: Policy ID
          example: "550e8400-e29b-41d4-a716-************"
        user_id:
          type: string
          format: uuid
          nullable: true
          description: User ID for user-specific licenses
          example: "550e8400-e29b-41d4-a716-************"
        expires_at:
          type: string
          format: date-time
          nullable: true
          description: License expiration timestamp
          example: "2025-12-31T23:59:59Z"
        metadata:
          type: object
          additionalProperties: true
          description: Custom metadata for the license
        created_at:
          type: string
          format: date-time
          description: License creation timestamp
          example: "2025-01-01T00:00:00Z"
        updated_at:
          type: string
          format: date-time
          description: License last update timestamp
          example: "2025-01-15T10:30:00Z"

    LicenseListResponse:
      type: object
      properties:
        licenses:
          type: array
          items:
            $ref: '#/components/schemas/LicenseResponse'
        pagination:
          $ref: '#/components/schemas/PaginationInfo'

    ValidateLicenseRequest:
      type: object
      required: [license_key]
      properties:
        license_key:
          type: string
          description: License key to validate
          example: "LIC-12345-ABCDE-67890-FGHIJ"
        machine_fingerprint:
          type: string
          description: Machine fingerprint for tracking
          example: "fp-mac-12345678"
        environment:
          type: string
          description: Environment context
          example: "production"
        machine_info:
          type: object
          additionalProperties: true
          description: Additional machine information
          example:
            hostname: "server-001"
            platform: "linux"
            arch: "amd64"

    ValidateLicenseResponse:
      type: object
      properties:
        valid:
          type: boolean
          description: Whether the license is valid
          example: true
        license:
          type: object
          description: License details (if valid)
        policy:
          type: object
          description: Policy details (if valid)
        account:
          type: object
          description: Account details (if valid)
        validation_time:
          type: string
          format: date-time
          description: Timestamp when validation was performed
          example: "2025-07-15T10:30:00Z"
        expires_at:
          type: string
          format: date-time
          nullable: true
          description: License expiration timestamp
          example: "2025-12-31T23:59:59Z"
        machines_used:
          type: integer
          description: Number of machines currently using this license
          example: 2
        machines_allowed:
          type: integer
          description: Maximum number of machines allowed for this license
          example: 5
        claims:
          type: object
          additionalProperties: true
          description: License claims and attributes
        errors:
          type: array
          items:
            type: string
          description: Validation errors (if any)
        warnings:
          type: array
          items:
            type: string
          description: Validation warnings (if any)
        cache_hit:
          type: boolean
          description: Whether the result came from cache
          example: false

    LicenseInfoResponse:
      type: object
      properties:
        format:
          type: string
          description: Response format
          example: "json"
        status:
          type: string
          description: License status
          example: "ACTIVE"
        account_id:
          type: string
          format: uuid
          nullable: true
          description: Account ID
          example: "550e8400-e29b-41d4-a716-************"
        product_id:
          type: string
          format: uuid
          nullable: true
          description: Product ID
          example: "550e8400-e29b-41d4-a716-************"
        expires_at:
          type: string
          format: date-time
          nullable: true
          description: License expiration timestamp
          example: "2025-12-31T23:59:59Z"
        expired:
          type: boolean
          description: Whether the license has expired
          example: false
        suspended:
          type: boolean
          description: Whether the license is suspended
          example: false
        claims:
          type: object
          additionalProperties: true
          description: License claims and attributes

    # Machine Schemas
    MachineCreateRequest:
      type: object
      required: [fingerprint, license_id]
      properties:
        name:
          type: string
          description: Machine name
          example: "Production Server 001"
        fingerprint:
          type: string
          minLength: 1
          maxLength: 255
          description: Unique machine fingerprint
          example: "fp-mac-12345678"
        platform:
          type: string
          enum: [windows, macos, linux, android, ios]
          description: Operating system platform
          example: "linux"
        hostname:
          type: string
          description: Machine hostname
          example: "server-001.example.com"
        ip:
          type: string
          format: ipv4
          description: Machine IP address
          example: "*************"
        cores:
          type: integer
          minimum: 1
          description: Number of CPU cores
          example: 8
        license_id:
          type: string
          format: uuid
          description: License ID this machine is associated with
          example: "550e8400-e29b-41d4-a716-************"
        metadata:
          type: object
          additionalProperties: true
          description: Custom metadata for the machine
          example:
            location: "datacenter-west"
            environment: "production"

    MachineUpdateRequest:
      type: object
      properties:
        name:
          type: string
          description: Machine name
          example: "Updated Production Server 001"
        fingerprint:
          type: string
          minLength: 1
          maxLength: 255
          description: Unique machine fingerprint
          example: "fp-mac-87654321"
        platform:
          type: string
          enum: [windows, macos, linux, android, ios]
          description: Operating system platform
          example: "linux"
        hostname:
          type: string
          description: Machine hostname
          example: "server-001-updated.example.com"
        ip:
          type: string
          format: ipv4
          description: Machine IP address
          example: "*************"
        cores:
          type: integer
          minimum: 1
          description: Number of CPU cores
          example: 16
        metadata:
          type: object
          additionalProperties: true
          description: Custom metadata for the machine

    MachineResponse:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: Machine ID
          example: "550e8400-e29b-41d4-a716-************"
        name:
          type: string
          description: Machine name
          example: "Production Server 001"
        fingerprint:
          type: string
          description: Unique machine fingerprint
          example: "fp-mac-12345678"
        platform:
          type: string
          enum: [windows, macos, linux, android, ios]
          description: Operating system platform
          example: "linux"
        hostname:
          type: string
          description: Machine hostname
          example: "server-001.example.com"
        ip:
          type: string
          format: ipv4
          description: Machine IP address
          example: "*************"
        cores:
          type: integer
          nullable: true
          description: Number of CPU cores
          example: 8
        license_id:
          type: string
          format: uuid
          description: License ID this machine is associated with
          example: "550e8400-e29b-41d4-a716-************"
        policy_id:
          type: string
          format: uuid
          description: Policy ID
          example: "550e8400-e29b-41d4-a716-************"
        group_id:
          type: string
          format: uuid
          nullable: true
          description: Group ID
          example: "550e8400-e29b-41d4-a716-************"
        owner_id:
          type: string
          format: uuid
          nullable: true
          description: Owner ID
          example: "550e8400-e29b-41d4-a716-************"
        environment_id:
          type: string
          format: uuid
          nullable: true
          description: Environment ID
          example: "550e8400-e29b-41d4-a716-************"
        status:
          type: string
          enum: [ACTIVE, INACTIVE, BANNED, DEAD]
          description: Machine status
          example: "ACTIVE"
        last_seen:
          type: string
          format: date-time
          nullable: true
          description: Last heartbeat timestamp
          example: "2025-07-15T10:25:00Z"
        metadata:
          type: object
          additionalProperties: true
          description: Custom metadata for the machine
        created_at:
          type: string
          format: date-time
          description: Machine registration timestamp
          example: "2025-01-01T00:00:00Z"
        updated_at:
          type: string
          format: date-time
          description: Machine last update timestamp
          example: "2025-01-15T10:30:00Z"

    MachineListResponse:
      type: object
      properties:
        machines:
          type: array
          items:
            $ref: '#/components/schemas/MachineResponse'
        pagination:
          $ref: '#/components/schemas/PaginationInfo'

    # Product Schemas
    ProductCreateRequest:
      type: object
      required: [name, code]
      properties:
        name:
          type: string
          minLength: 1
          maxLength: 255
          description: Product name
          example: "Enterprise Software Suite"
        code:
          type: string
          minLength: 1
          maxLength: 255
          pattern: '^[a-zA-Z0-9-_]+$'
          description: Product code (alphanumeric, dash, underscore only)
          example: "enterprise-suite"
        description:
          type: string
          description: Product description
          example: "Comprehensive enterprise software solution"
        distribution_strategy:
          type: string
          enum: [licensed, open]
          description: Distribution strategy
          example: "licensed"
        url:
          type: string
          format: uri
          description: Product URL
          example: "https://example.com/products/enterprise-suite"
        platforms:
          type: array
          items:
            type: string
            enum: [windows, macos, linux, android, ios, web]
          description: Supported platforms
          example: ["windows", "macos", "linux"]
        environment_id:
          type: string
          format: uuid
          description: Environment ID
          example: "550e8400-e29b-41d4-a716-************"
        metadata:
          type: object
          additionalProperties: true
          description: Custom metadata for the product
          example:
            category: "productivity"
            version: "2.0"

    ProductUpdateRequest:
      type: object
      properties:
        name:
          type: string
          minLength: 1
          maxLength: 255
          description: Product name
          example: "Updated Enterprise Software Suite"
        code:
          type: string
          minLength: 1
          maxLength: 255
          pattern: '^[a-zA-Z0-9-_]+$'
          description: Product code (alphanumeric, dash, underscore only)
          example: "updated-enterprise-suite"
        description:
          type: string
          description: Product description
          example: "Updated comprehensive enterprise software solution"
        distribution_strategy:
          type: string
          enum: [licensed, open]
          description: Distribution strategy
          example: "licensed"
        url:
          type: string
          format: uri
          description: Product URL
          example: "https://example.com/products/updated-enterprise-suite"
        platforms:
          type: array
          items:
            type: string
            enum: [windows, macos, linux, android, ios, web]
          description: Supported platforms
          example: ["windows", "macos", "linux", "web"]
        metadata:
          type: object
          additionalProperties: true
          description: Custom metadata for the product

    ProductResponse:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: Product ID
          example: "550e8400-e29b-41d4-a716-************"
        name:
          type: string
          description: Product name
          example: "Enterprise Software Suite"
        code:
          type: string
          description: Product code
          example: "enterprise-suite"
        description:
          type: string
          description: Product description
          example: "Comprehensive enterprise software solution"
        distribution_strategy:
          type: string
          enum: [licensed, open]
          description: Distribution strategy
          example: "licensed"
        url:
          type: string
          format: uri
          description: Product URL
          example: "https://example.com/products/enterprise-suite"
        platforms:
          type: array
          items:
            type: string
            enum: [windows, macos, linux, android, ios, web]
          description: Supported platforms
          example: ["windows", "macos", "linux"]
        environment_id:
          type: string
          format: uuid
          nullable: true
          description: Environment ID
          example: "550e8400-e29b-41d4-a716-************"
        metadata:
          type: object
          additionalProperties: true
          description: Custom metadata for the product
        created_at:
          type: string
          format: date-time
          description: Product creation timestamp
          example: "2025-01-01T00:00:00Z"
        updated_at:
          type: string
          format: date-time
          description: Product last update timestamp
          example: "2025-01-15T10:30:00Z"

    ProductListResponse:
      type: object
      properties:
        products:
          type: array
          items:
            $ref: '#/components/schemas/ProductResponse'
        pagination:
          $ref: '#/components/schemas/PaginationInfo'

    # Policy Schemas
    PolicyCreateRequest:
      type: object
      required: [name, product_id]
      properties:
        name:
          type: string
          minLength: 1
          maxLength: 255
          description: Policy name
          example: "Enterprise Policy"
        description:
          type: string
          description: Policy description
          example: "Enterprise licensing policy with advanced features"
        product_id:
          type: string
          format: uuid
          description: Product ID this policy belongs to
          example: "550e8400-e29b-41d4-a716-************"
        environment_id:
          type: string
          format: uuid
          description: Environment ID
          example: "550e8400-e29b-41d4-a716-************"
        duration:
          type: integer
          minimum: 0
          description: License duration in seconds
          example: 31536000
        strict:
          type: boolean
          description: Whether to enforce strict validation
          example: true
        floating:
          type: boolean
          description: Whether licenses are floating
          example: false
        require_heartbeat:
          type: boolean
          description: Whether to require machine heartbeats
          example: true
        max_machines:
          type: integer
          minimum: 0
          description: Maximum number of machines allowed
          example: 5
        max_processes:
          type: integer
          minimum: 0
          description: Maximum number of processes allowed
          example: 10
        max_users:
          type: integer
          minimum: 0
          description: Maximum number of users allowed
          example: 100
        max_cores:
          type: integer
          minimum: 0
          description: Maximum number of CPU cores allowed
          example: 32
        max_uses:
          type: integer
          minimum: 0
          description: Maximum number of uses allowed
          example: 1000
        scheme:
          type: string
          enum: [ED25519_SIGN, RSA_PKCS1_SIGN, RSA_PSS_SIGN]
          description: Cryptographic signature scheme
          example: "RSA_PKCS1_SIGN"
        heartbeat_duration:
          type: integer
          minimum: 0
          description: Heartbeat duration in seconds
          example: 300
        machine_uniqueness_strategy:
          type: string
          enum: [UNIQUE_PER_ACCOUNT, UNIQUE_PER_PRODUCT, UNIQUE_PER_POLICY, UNIQUE_PER_LICENSE]
          description: Strategy for machine uniqueness
          example: "UNIQUE_PER_LICENSE"
        expiration_strategy:
          type: string
          enum: [RESTRICT_ACCESS, REVOKE_ACCESS, MAINTAIN_ACCESS]
          description: Strategy when license expires
          example: "RESTRICT_ACCESS"
        overage_strategy:
          type: string
          enum: [NO_OVERAGE, ALWAYS_ALLOW_OVERAGE, ALLOW_1_25X_OVERAGE, ALLOW_1_5X_OVERAGE, ALLOW_2X_OVERAGE]
          description: Strategy for handling overages
          example: "NO_OVERAGE"
        require_check_in:
          type: boolean
          description: Whether to require periodic check-ins
          example: false
        check_in_interval:
          type: string
          description: Check-in interval
          example: "daily"
        check_in_interval_count:
          type: integer
          minimum: 0
          description: Check-in interval count
          example: 1
        use_pool:
          type: boolean
          description: Whether to use license pooling
          example: false
        max_activations:
          type: integer
          minimum: 0
          description: Maximum number of activations allowed
          example: 10
        max_deactivations:
          type: integer
          minimum: 0
          description: Maximum number of deactivations allowed
          example: 5
        protected:
          type: boolean
          description: Whether policy is protected from modification
          example: false
        metadata:
          type: object
          additionalProperties: true
          description: Custom metadata for the policy
          example:
            tier: "enterprise"
            features: ["advanced_analytics", "priority_support"]

    PolicyUpdateRequest:
      type: object
      properties:
        name:
          type: string
          minLength: 1
          maxLength: 255
          description: Policy name
          example: "Updated Enterprise Policy"
        description:
          type: string
          description: Policy description
          example: "Updated enterprise licensing policy with advanced features"
        duration:
          type: integer
          minimum: 0
          description: License duration in seconds
          example: 63072000
        strict:
          type: boolean
          description: Whether to enforce strict validation
          example: false
        floating:
          type: boolean
          description: Whether licenses are floating
          example: true
        require_heartbeat:
          type: boolean
          description: Whether to require machine heartbeats
          example: false
        max_machines:
          type: integer
          minimum: 0
          description: Maximum number of machines allowed
          example: 10
        max_processes:
          type: integer
          minimum: 0
          description: Maximum number of processes allowed
          example: 20
        max_users:
          type: integer
          minimum: 0
          description: Maximum number of users allowed
          example: 200
        max_cores:
          type: integer
          minimum: 0
          description: Maximum number of CPU cores allowed
          example: 64
        max_uses:
          type: integer
          minimum: 0
          description: Maximum number of uses allowed
          example: 2000
        scheme:
          type: string
          enum: [ED25519_SIGN, RSA_PKCS1_SIGN, RSA_PSS_SIGN]
          description: Cryptographic signature scheme
          example: "ED25519_SIGN"
        heartbeat_duration:
          type: integer
          minimum: 0
          description: Heartbeat duration in seconds
          example: 600
        machine_uniqueness_strategy:
          type: string
          enum: [UNIQUE_PER_ACCOUNT, UNIQUE_PER_PRODUCT, UNIQUE_PER_POLICY, UNIQUE_PER_LICENSE]
          description: Strategy for machine uniqueness
          example: "UNIQUE_PER_POLICY"
        expiration_strategy:
          type: string
          enum: [RESTRICT_ACCESS, REVOKE_ACCESS, MAINTAIN_ACCESS]
          description: Strategy when license expires
          example: "MAINTAIN_ACCESS"
        overage_strategy:
          type: string
          enum: [NO_OVERAGE, ALWAYS_ALLOW_OVERAGE, ALLOW_1_25X_OVERAGE, ALLOW_1_5X_OVERAGE, ALLOW_2X_OVERAGE]
          description: Strategy for handling overages
          example: "ALLOW_1_25X_OVERAGE"
        require_check_in:
          type: boolean
          description: Whether to require periodic check-ins
          example: true
        check_in_interval:
          type: string
          description: Check-in interval
          example: "weekly"
        check_in_interval_count:
          type: integer
          minimum: 0
          description: Check-in interval count
          example: 2
        use_pool:
          type: boolean
          description: Whether to use license pooling
          example: true
        max_activations:
          type: integer
          minimum: 0
          description: Maximum number of activations allowed
          example: 20
        max_deactivations:
          type: integer
          minimum: 0
          description: Maximum number of deactivations allowed
          example: 10
        protected:
          type: boolean
          description: Whether policy is protected from modification
          example: true
        metadata:
          type: object
          additionalProperties: true
          description: Custom metadata for the policy

    PolicyResponse:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: Policy ID
          example: "550e8400-e29b-41d4-a716-************"
        name:
          type: string
          description: Policy name
          example: "Enterprise Policy"
        description:
          type: string
          description: Policy description
          example: "Enterprise licensing policy with advanced features"
        product_id:
          type: string
          format: uuid
          description: Product ID this policy belongs to
          example: "550e8400-e29b-41d4-a716-************"
        environment_id:
          type: string
          format: uuid
          nullable: true
          description: Environment ID
          example: "550e8400-e29b-41d4-a716-************"
        duration:
          type: integer
          nullable: true
          description: License duration in seconds
          example: 31536000
        strict:
          type: boolean
          description: Whether to enforce strict validation
          example: true
        floating:
          type: boolean
          description: Whether licenses are floating
          example: false
        require_heartbeat:
          type: boolean
          description: Whether to require machine heartbeats
          example: true
        max_machines:
          type: integer
          nullable: true
          description: Maximum number of machines allowed
          example: 5
        max_processes:
          type: integer
          nullable: true
          description: Maximum number of processes allowed
          example: 10
        max_users:
          type: integer
          nullable: true
          description: Maximum number of users allowed
          example: 100
        max_cores:
          type: integer
          nullable: true
          description: Maximum number of CPU cores allowed
          example: 32
        max_uses:
          type: integer
          nullable: true
          description: Maximum number of uses allowed
          example: 1000
        scheme:
          type: string
          enum: [ED25519_SIGN, RSA_PKCS1_SIGN, RSA_PSS_SIGN]
          description: Cryptographic signature scheme
          example: "RSA_PKCS1_SIGN"
        heartbeat_duration:
          type: integer
          nullable: true
          description: Heartbeat duration in seconds
          example: 300
        machine_uniqueness_strategy:
          type: string
          enum: [UNIQUE_PER_ACCOUNT, UNIQUE_PER_PRODUCT, UNIQUE_PER_POLICY, UNIQUE_PER_LICENSE]
          description: Strategy for machine uniqueness
          example: "UNIQUE_PER_LICENSE"
        expiration_strategy:
          type: string
          enum: [RESTRICT_ACCESS, REVOKE_ACCESS, MAINTAIN_ACCESS]
          description: Strategy when license expires
          example: "RESTRICT_ACCESS"
        overage_strategy:
          type: string
          enum: [NO_OVERAGE, ALWAYS_ALLOW_OVERAGE, ALLOW_1_25X_OVERAGE, ALLOW_1_5X_OVERAGE, ALLOW_2X_OVERAGE]
          description: Strategy for handling overages
          example: "NO_OVERAGE"
        require_check_in:
          type: boolean
          description: Whether to require periodic check-ins
          example: false
        check_in_interval:
          type: string
          nullable: true
          description: Check-in interval
          example: "daily"
        check_in_interval_count:
          type: integer
          nullable: true
          description: Check-in interval count
          example: 1
        use_pool:
          type: boolean
          description: Whether to use license pooling
          example: false
        max_activations:
          type: integer
          nullable: true
          description: Maximum number of activations allowed
          example: 10
        max_deactivations:
          type: integer
          nullable: true
          description: Maximum number of deactivations allowed
          example: 5
        protected:
          type: boolean
          description: Whether policy is protected from modification
          example: false
        metadata:
          type: object
          additionalProperties: true
          description: Custom metadata for the policy
        created_at:
          type: string
          format: date-time
          description: Policy creation timestamp
          example: "2025-01-01T00:00:00Z"
        updated_at:
          type: string
          format: date-time
          description: Policy last update timestamp
          example: "2025-01-15T10:30:00Z"

    PolicyListResponse:
      type: object
      properties:
        policies:
          type: array
          items:
            $ref: '#/components/schemas/PolicyResponse'
        pagination:
          $ref: '#/components/schemas/PaginationInfo'

    # Common Schemas
    PaginationInfo:
      type: object
      properties:
        page:
          type: integer
          description: Current page number
          example: 1
        per_page:
          type: integer
          description: Items per page
          example: 20
        total:
          type: integer
          format: int64
          description: Total number of items
          example: 150
        total_pages:
          type: integer
          description: Total number of pages
          example: 8

    ErrorDetail:
      type: object
      properties:
        code:
          type: string
          description: Error code
          example: "VALIDATION_FAILED"
        title:
          type: string
          description: Error title
          example: "Validation Failed"
        detail:
          type: string
          description: Detailed error message
          example: "The request data is invalid"
        source:
          type: object
          additionalProperties: true
          description: Source of the error
          example:
            pointer: "/data/attributes/name"
        meta:
          type: object
          additionalProperties: true
          description: Additional error metadata

    ErrorResponse:
      type: object
      properties:
        errors:
          type: array
          items:
            $ref: '#/components/schemas/ErrorDetail'

  responses:
    BadRequest:
      description: Bad request - invalid input data
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            errors:
              - code: "VALIDATION_FAILED"
                title: "Validation Failed"
                detail: "Invalid request data"

    Unauthorized:
      description: Unauthorized - authentication required
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            errors:
              - code: "UNAUTHORIZED"
                title: "Authentication Required"
                detail: "Valid authentication credentials required"

    Forbidden:
      description: Forbidden - insufficient permissions
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            errors:
              - code: "FORBIDDEN"
                title: "Access Forbidden"
                detail: "Insufficient permissions to access this resource"

    NotFound:
      description: Not found - resource does not exist
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            errors:
              - code: "NOT_FOUND"
                title: "Resource Not Found"
                detail: "The requested resource was not found"

    Conflict:
      description: Conflict - resource already exists
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            errors:
              - code: "CONFLICT"
                title: "Resource Conflict"
                detail: "A resource with this identifier already exists"

    InternalServerError:
      description: Internal server error
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            errors:
              - code: "INTERNAL_ERROR"
                title: "Internal Server Error"
                detail: "An unexpected error occurred"